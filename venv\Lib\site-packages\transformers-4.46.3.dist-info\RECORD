transformers/__init__.py,sha256=cacppEC7psDJ0fkgnG7dYe2yE4DIkVRGG-AAY9hryCk,305220
transformers/activations.py,sha256=EMN-kVzitS1TmltS7Kr2ROKwxW0oLbAHeAmNdDQuvu4,8177
transformers/activations_tf.py,sha256=u2Y9dgDRgW-YbN_J-xmd05EK4p24rV8ZkzrQzpz4lCI,4689
transformers/audio_utils.py,sha256=5LXuKdw8VjY_gQlq86dvES7NqsD5wb6aoxJfw5EzId8,50345
transformers/cache_utils.py,sha256=OA7Z4J1lb9fZN3U3a6xUKoayumD3y4aqIFpRdCtD-gE,99754
transformers/configuration_utils.py,sha256=NPzgqMyUi-xMH7dDnVDla3Kn2ua4RojJ2HnxDjyYBh4,55814
transformers/convert_graph_to_onnx.py,sha256=eoA4_4LmxwK-dirCgB0A75dAIMn_v9BoYEoJ_HaJc1Q,20151
transformers/convert_pytorch_checkpoint_to_tf2.py,sha256=hTMei3XHETc9a9gxVLtNPpF5m4QVh_lQE8DI-Jn_NaI,14653
transformers/convert_slow_tokenizer.py,sha256=hP2kwzyYSQIgTGx4SzA_NkIpNvWI08-AiRgStDKYtbY,60355
transformers/convert_slow_tokenizers_checkpoints_to_fast.py,sha256=5FiWOleZOLTCtR7T8h2l1-XaryCz8VPbOfT8lK46vMQ,5076
transformers/convert_tf_hub_seq_to_seq_bert_to_pytorch.py,sha256=dy9yjETinWJl2MeQ-wv1J5HtmGm3j6Ki3r65optejRg,2910
transformers/debug_utils.py,sha256=6q8ArB104GdcIC2qfBQzKLxO7PfXmHEKdYtfL2FOK2w,12907
transformers/dependency_versions_check.py,sha256=6HbgtT2Wp-QZGOAdyUOklHvNA4rOVITGHrX34dtMOqg,2115
transformers/dependency_versions_table.py,sha256=Mllg2XSWMix4B5C3b8H57ma1qGxu4eno_3Ev8jDPpyQ,3528
transformers/dynamic_module_utils.py,sha256=tg_4XEQ80GL5_BlJovh_0_fB7L5R3lLsKPXndrtP7iw,29712
transformers/feature_extraction_sequence_utils.py,sha256=xE5f0cSWWodEjCwNDsG0Dl9kL3B9KPs-SsF4YTWNh0M,18307
transformers/feature_extraction_utils.py,sha256=oCiDor7Zi1WtMX_8mmB-ktUIfasEkz6M1k_k9eaFUmI,30307
transformers/file_utils.py,sha256=qI7cWTYpFy0v9HZSRBASv2yvD2U1OJgYShIOsQ7cCUg,3744
transformers/hf_argparser.py,sha256=hNp_XsFe5vKwn6FTZ-bckyGDk0RVqMBv0WUenYHfJtM,20378
transformers/hyperparameter_search.py,sha256=wmfAWk_NTUQj3MezO_6CaDaJyUt9pbARcs-tbo_BdeM,4171
transformers/image_processing_base.py,sha256=oDDjDlyWlqCWWgXok3WbWRbDyP1nZRT5jDIgBfMqJps,25015
transformers/image_processing_utils.py,sha256=EDSL4O-qWBofy1a5sZLNtSG9b-PvFpteAmP7U9_ai0I,12742
transformers/image_processing_utils_fast.py,sha256=C0DJr5wuXMMI66J8NQJQDgQpDBIHR--qnAoJXy9-O1Q,2090
transformers/image_transforms.py,sha256=Ge6l2FQo2vDaX0kD-q0G5zth3FUBWrC6oNHUbt_R_mM,36166
transformers/image_utils.py,sha256=QWoFTx8xeIu-NK4nlaEcEgjR9NKTmCvdEiiiGs5btRw,31299
transformers/keras_callbacks.py,sha256=i95nrEd_QsEo10x3T9RqZf3xGzfPiMOhmU1Ef_HvnGE,20675
transformers/modelcard.py,sha256=7oXrCHfeWDdwK5dSjuNDUrs-rqrjCmsfY0s2jXsVfjM,35555
transformers/modeling_attn_mask_utils.py,sha256=a0hgiIe6FnEpKRZdjAW96cE2sdXKwZrP3milQ1pXDM4,20989
transformers/modeling_flash_attention_utils.py,sha256=J1PtC9ubN_cqS00GY-LygzrVWodL431Fmdaeuagm7eA,13988
transformers/modeling_flax_outputs.py,sha256=wXse1g9VyQyVOZ9DrbPALeoZBdS45fsBA9fNrGnwaZc,41961
transformers/modeling_flax_pytorch_utils.py,sha256=P20VD_JjurkZjc4Et_1tloFlXVCOfg1OeIWJ1I7mfUc,21890
transformers/modeling_flax_utils.py,sha256=koz6c6GgLrK2YpJdLUQr_WsBPKl9QE9J_G_o_7FbkOo,61486
transformers/modeling_gguf_pytorch_utils.py,sha256=j1Uk1KYHfM9tNsT-vdJIqSdtTGyHodUYXCoARHfw3Ro,11894
transformers/modeling_outputs.py,sha256=CYpjijqZNOVUc-kixDLI-jMFru9MhpDQvnncSfp0wb4,112567
transformers/modeling_rope_utils.py,sha256=yQ_x6AzAGrS9vgcOCQU_UbtQcny37oZw2_yXs3bYxac,28849
transformers/modeling_tf_outputs.py,sha256=nXCMOmFZ7IZFVuiQr7EU2ciV9QqwOYPYld_r2jBxVpE,56074
transformers/modeling_tf_pytorch_utils.py,sha256=F9RBC-lPIZwTCsnIZlC68JCpdUJH1yzgIzGf7pnqfi0,27906
transformers/modeling_tf_utils.py,sha256=4v71q5OmNGY62xqwPp8I3uIYy443IY5STb_a4gnZV_4,166841
transformers/modeling_utils.py,sha256=EYvXWDjnixmlWszdzwqRM4bYIugSwhXVDamcPQ6R09o,274590
transformers/optimization.py,sha256=pOi-3ADMoveZKUqQfi3yRbzyplmi_x5z-gaUNhGXbBk,39133
transformers/optimization_tf.py,sha256=UPtbbeR__ZoPG7eBD5XMBBiYfjAZR8a5L2zWJsCLL_8,16854
transformers/processing_utils.py,sha256=mrj_cKDznauCr4TBKVf_x4bWG64Gij6ft5-fo76yD2s,52025
transformers/pytorch_utils.py,sha256=u16YGuopXMeCXGN3rVIV23xTIQmBct9B0lrJ191Quv0,13334
transformers/safetensors_conversion.py,sha256=tHyyBWc4SGbfyamAfhvSG94Lmk7PysSLWP6g_XI73mA,4074
transformers/testing_utils.py,sha256=iwB_EuMKK_HCOUTFcwopSIitOoDvPXpVPfPdCKaapFE,90154
transformers/tf_utils.py,sha256=v4iybFTb3eRDgGzhAUTVYim-qNZvYF2k6rlHk7wTii4,11386
transformers/time_series_utils.py,sha256=MT780YtbZhdZcz7I9WJ9XVpmZgCVUT2eJ4-g8snaYvQ,7521
transformers/tokenization_utils.py,sha256=kyvjxa0HuQYIBRvEHaVYLNg4rj-hrjRGn1us9GeUfQ8,47771
transformers/tokenization_utils_base.py,sha256=_UrrinBsDhgUpRhy5IQSSw1h78CEreASn86GoGDYnDo,211016
transformers/tokenization_utils_fast.py,sha256=3upuT3pbZ97T3X26gTydscHkhPKJXv-Hpds31Q_jh-g,39807
transformers/trainer.py,sha256=K55MncBj7vTxpabgPquY1RP-FiVwA_hK8LUIH9S9EuQ,246741
transformers/trainer_callback.py,sha256=bpadE1W7v3tjcoe_8BDiZnzMhgIj48EcpL0a456DVdU,30854
transformers/trainer_pt_utils.py,sha256=YbCCitRp1yzUqMkJD6uycCRv8VAKxZZA4t563rVcSQk,60764
transformers/trainer_seq2seq.py,sha256=1uv257cKzZQZ8IWGmV2alZPcAOcq4N7HxDbgavLwe4I,18202
transformers/trainer_utils.py,sha256=DuLxepNe6QOnvoe8d3bxM_LYCCY4YOejP_b4kx9eSrk,32773
transformers/training_args.py,sha256=bFlOl8TdkwYSzLqP52NlDvka6dfiCyDTJgF7980G8jc,158485
transformers/training_args_seq2seq.py,sha256=k8qyPQAo5GWlcToN3tnzW7dE4xyP7i7HRjP_sgxlllA,4308
transformers/training_args_tf.py,sha256=2m7MCkBCsSiHHmej59xpmVKgYYhoJ9sM544xRNlg6Gc,14576
transformers/agents/__init__.py,sha256=wVjvkIafUIotTIDrniFMuqMc_iyq4MGAG9aim8VYFMU,2859
transformers/agents/agent_types.py,sha256=2_oA8kkl2qdnfp0sLtZaTieuBg4fix7fuo55IQMNO14,8370
transformers/agents/agents.py,sha256=Wkjvac9Z_biXl9JtI6yew4YAhqPDiLsHCZ4fogc_EpE,49148
transformers/agents/default_tools.py,sha256=kY4Ae9yn1l_hs1E-wQe3zT48WBU9hitIsBPiCvZBFVU,5252
transformers/agents/document_question_answering.py,sha256=-xI5v_R5MATPdra5VMHAEi1Zt3oiRv_fJu4N16Expkk,3634
transformers/agents/evaluate_agent.py,sha256=-q4K6vyCFIbPx8uYtf85jh8i7a6wFY_u5XOiRas58UQ,14929
transformers/agents/image_question_answering.py,sha256=yk7pGDeL0MVoRj56H11qt7JPwNTR-8q5dD80kbaHFW8,2004
transformers/agents/llm_engine.py,sha256=CtBmV16r_3C2X5puvNZImyieYfSb9ccKgx-xVa_CkdM,4785
transformers/agents/monitoring.py,sha256=wlmp1NKwit72Y-C2AElRMR9kbh2kw4N_MsYY3TVLchA,2910
transformers/agents/prompts.py,sha256=uWJ49ETR_J4Lz6fKmQALFUKdoj55-syIwQuXsv6aKFM,31409
transformers/agents/python_interpreter.py,sha256=gQBL5l0RwZyIxsCp9Vy2exzm-OUvl6JwzXcGzJDyoy4,38590
transformers/agents/search.py,sha256=sZAYHlDsIek_GZpPqfjckYNcJWpGU5G9yxd1L8Is16c,2777
transformers/agents/speech_to_text.py,sha256=9VNfM56c-H9bL0g84hzGkDCT1_EWGwBtF0A8mQYpJ88,1496
transformers/agents/text_to_speech.py,sha256=FhVbSolKvMlSB454HF_RqtAULygdQ8xMas_B2G8gIyw,2468
transformers/agents/tools.py,sha256=QSUhSnSTMnHlV5B6h7zJk18vcTYrSV99OI7Az1Br9No,34030
transformers/agents/translation.py,sha256=P-Dnk3rUA3NPrJKiveRKptCwJ_M-3k1JEPyNze7VAAc,8671
transformers/benchmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/benchmark/benchmark.py,sha256=efSeIH606x5u9IA6CkKXL7t7CoH9kORKSrXypR8gfHk,10747
transformers/benchmark/benchmark_args.py,sha256=djFAjBC11MnI-auxByCWSVVAqRqXGV650Leosd60VmA,4050
transformers/benchmark/benchmark_args_tf.py,sha256=bAcsgf7bOUyoo8AGFSiQhciR8S5wMJqnL5iVlvbQzow,4735
transformers/benchmark/benchmark_args_utils.py,sha256=pkgvor3IuC5v9BubOCFVuwbgGHsoGkNp1CDdgJlyBi4,6499
transformers/benchmark/benchmark_tf.py,sha256=wSXldL0TVlNzBB0YOUTQ9REia8Eib6nz4WsNg6UXBsU,13246
transformers/benchmark/benchmark_utils.py,sha256=ZzHCNnPKtygQpdq3vD1q_p5Y2rwo4ctmKjOc5e8sca8,37599
transformers/commands/__init__.py,sha256=aFO3I7C6G9OLA9JZSc_yMaZl0glOQtjNPjqMFfu9wfQ,923
transformers/commands/add_new_model_like.py,sha256=Nl8vdHyP1it2FieiiymuEmyjURPytbBNNhgSW0QsrgU,71130
transformers/commands/convert.py,sha256=lHz2sQti9HubMNwObLCc_sw9Y7L-IPcaYJMSJR_AVWM,7068
transformers/commands/download.py,sha256=GKPadx-YGBL7dHJSEcUp-QNOP3R2L71-gPGP0z6NNQI,2395
transformers/commands/env.py,sha256=g54Hwtyp3ZpYq782cKwvLrnliLg2PhUrZ7yOQsOgZ5c,5756
transformers/commands/lfs.py,sha256=4QDGBbJxBcRpgmhHXvigZQUsXuTPwrRY60t1qGjzfWU,8001
transformers/commands/pt_to_tf.py,sha256=c4xczk0sjiH_Y4cgq5n9nvWaYXxfP5HoPgiqqDx1yoQ,5087
transformers/commands/run.py,sha256=nyEe2lOoj6e0EOxjKeF08hdW9WVWa101r9hWXl9v3Jo,4249
transformers/commands/serving.py,sha256=CnNHFVM_SK_-aNxEJnq7vJK5dBqDBw7bxxQiv5truEU,8027
transformers/commands/train.py,sha256=FKlH-IYr3mVc7_mS5ObCyJaHs9JincYLg3Zt6WQz1ag,6341
transformers/commands/transformers_cli.py,sha256=ynsTme9TyR81oBP6a9hEbDdR8vZvQwOaE2-lRlsnLvs,1941
transformers/commands/user.py,sha256=UxHTw7L2qU8Svdj6v2Nb8OB8nGI5dRXawrXt398DkXQ,7091
transformers/data/__init__.py,sha256=Ey608EH-9P9qajKIrBxopGKH4FwyEL5tB8fQ3SfzSJk,1455
transformers/data/data_collator.py,sha256=z7L3VIEyAFm_pYh6-P3eXbvznpsXlRILdXS6nY2Cac4,82223
transformers/data/datasets/__init__.py,sha256=PGzUJjdmTPOPMyjV4-Tj3sNrmmh-lspjyxrVbrfJoX8,909
transformers/data/datasets/glue.py,sha256=K3h2KxjIg0kWegPCw6ikbOL-lCFbKoQewb7R8wLZoIc,6163
transformers/data/datasets/language_modeling.py,sha256=E-VGwuyb09J4KmV8v37bNH5in90wDPuZHCYsqGdT7W0,23721
transformers/data/datasets/squad.py,sha256=OUTQDd687SQns7HRWDCgAjnuo_ZXihifLS6jF2bhUhc,9219
transformers/data/metrics/__init__.py,sha256=o9t_VTQtqU3lEhqvocDzFMm7OvAKD-uxrjPWy0r74BI,3632
transformers/data/metrics/squad_metrics.py,sha256=mP6eaDcGTLsS4EhnvnD3U_Yyvcua_LVgElCkuxy2XJE,29697
transformers/data/processors/__init__.py,sha256=lvN5mp9mdrr5v6QvZT6VcoZ78zZUvXiumTm6Gdvlgvo,1014
transformers/data/processors/glue.py,sha256=1sHcfSWbl-ooNIEu3emKmDlpW-95UZT1JfDlGYx5TFA,23218
transformers/data/processors/squad.py,sha256=_4WNLcZA6TAy7uNZO46948tmL5ngVF0LSB0y8nUn6rs,33153
transformers/data/processors/utils.py,sha256=GSaZbJ--XYq57vqyRVx_5LHSR4tklzFyR7ZKHGWsTAs,13829
transformers/data/processors/xnli.py,sha256=sgcYz9YSfHY9NS0LO_YeFRRjq-nJFsDhFUP4NJeu-Q4,3481
transformers/generation/__init__.py,sha256=_UjMbeup_2ad8j3_b0EF87s-stg0bZ1CSZYes95vrAM,12186
transformers/generation/beam_constraints.py,sha256=Yt9dtQARVET_WaC26Sil49DO-b2YfosyhP2Rx5ILReI,19274
transformers/generation/beam_search.py,sha256=rGjKNmKPa61POCkEh5VmZn9oUaqM3eIJ1jXhkn7CPYU,49536
transformers/generation/candidate_generator.py,sha256=B9Vw5lRMcNzmXeinvVvYjASbJi8SlHjXIVJvuJHu3RQ,38168
transformers/generation/configuration_utils.py,sha256=dImcm2Qa7J2LCJe4BP75YgmBgdsY8zc3Yl7ybF_1eSE,79581
transformers/generation/flax_logits_process.py,sha256=99pNEs2Lw63VO6014gBesHxyg8n3PKG2oAsEwhd_7EY,23006
transformers/generation/flax_utils.py,sha256=x4J5blTRgFVp8o0lK-UvjOYzpeTP54kdy5m5eK8apzQ,50078
transformers/generation/logits_process.py,sha256=FnzZON9VNod72muJOYRkg0CQhH0P9ev62DUqT1CHa2I,138398
transformers/generation/stopping_criteria.py,sha256=U0vcGHAuBAxuSbV2Jb7nFd2n0AkxdMSeI_8rvtG5R4I,28583
transformers/generation/streamers.py,sha256=ArJCKAVRKIKALqdGBAsQu038-BwZbo05tzOXZWP9yng,9213
transformers/generation/tf_logits_process.py,sha256=ldiRDY6M1t2Lj2ydwImS4b44bD3O2i6TCXMcODp3Ikg,28713
transformers/generation/tf_utils.py,sha256=kuKYrCvLa_JDAFAe3wD064byH6t62gtC2Y-qQVg07a0,175608
transformers/generation/utils.py,sha256=JOcZvglCoG8ZVsrdRpoQCkVQEupvgIzKiUo-lO2wa_M,253244
transformers/generation/watermarking.py,sha256=8d4AFs9CRnGsguiqLKy8Lz6g2pg0wLAaZtAwA7qsIxA,24416
transformers/integrations/__init__.py,sha256=gQ3LC0yjMmxMwDzQH9gIbtf-9cnUgj8WW9D8NrYeg9E,6802
transformers/integrations/aqlm.py,sha256=g4ujHmqCr66ugoP93lemtIaMQLLDMDZ1TeeR0U1MIOw,4536
transformers/integrations/awq.py,sha256=gZjVCxWhVErViMh1AHtCWw3bjtHRGwlu5ick6Qy2pFY,20580
transformers/integrations/bitnet.py,sha256=z9I6sEsWN6VYMPeof10eWLqR17xwZOFJM7ehRHgwZQo,10626
transformers/integrations/bitsandbytes.py,sha256=YgsOSNKQSauv_cgUP2X_is1V_IjusI0FRVGFwPzNCac,24905
transformers/integrations/deepspeed.py,sha256=Ui0mjL7i7sn6kI1pmYoj0bovrFvogV6xyW6qpbHnPqM,19136
transformers/integrations/eetq.py,sha256=ZDxYk6vfjGsjQoQeFOQ2odn58hc6xrYbwdKtfLdDp-Q,5365
transformers/integrations/executorch.py,sha256=Kle1GRkyPrbNw4GoMIA5dXPVI-uOLYH5GOn2yn6iAg0,9445
transformers/integrations/fbgemm_fp8.py,sha256=g322A5Hhz7agAACHYgeEvh6P4gWcnRt1BxkUBwYbMLA,7431
transformers/integrations/fsdp.py,sha256=_jkV54fIk9TIz8ZmU_51KIJUzc3yEMi_nE-dUdCXwFw,1053
transformers/integrations/ggml.py,sha256=FkkA1RMcB_ecDSwgj1ZUnr06rpQZAi7N8XgHmcUcqro,25437
transformers/integrations/hqq.py,sha256=meBkmmy99NgsTG0Oir9P_DMEifRgpbuCvbmgu73KTTM,5088
transformers/integrations/integration_utils.py,sha256=s4W5B6hYIpIbS0MIRTwSTtsC7cgsaydWFvz5o94mIYU,96617
transformers/integrations/peft.py,sha256=Zmk2HOcJicTfnMlwNpiAKhX_WVjmqEbdwDohnxWlby0,23897
transformers/integrations/quanto.py,sha256=l0eh9tBi4jL_ohzmNHT-kjtCOPtpJRHYXAeJHCNXgjk,4679
transformers/integrations/tpu.py,sha256=Y8YMwIrEgh1s-OCNbOQZFD1_3Tvqpo3e1H6eECTceSU,1392
transformers/kernels/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/kernels/deformable_detr/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deformable_detr/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cu,sha256=9X92GS3siNseh_ToGfwQm-_N_o_ZQBmgm0rAa6tpCzk,7553
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cuh,sha256=HD7bMWLoGrDKw7XUPPgILCAdOSo1IC8RIv_KyKAnLb0,61539
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.h,sha256=xxP17aer-SiU9J5ASLHdtLIyhFmHC5iLcPIPNW2xkrg,1694
transformers/kernels/deformable_detr/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/deta/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deta/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/deta/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deta/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cu,sha256=M5-bW9g5z-upTFMNPIfnyLAqKTxGMCjAPqBr0GmWHX8,7360
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cuh,sha256=hygB20Vh3RttOSdCuTFz8V0d3CXNp-Q89x22rYmD258,61433
transformers/kernels/deta/cuda/ms_deform_attn_cuda.h,sha256=rPWOOMo3QyFdB5kMiexpApLFZ4dnRtx4CluEAGwsfO8,1139
transformers/kernels/deta/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/falcon_mamba/__init__.py,sha256=bt0j851F1uuH7flSsTvIqdh9zdKVTOVKWt3datb15SI,721
transformers/kernels/falcon_mamba/selective_scan_with_ln_interface.py,sha256=649oJD0sox1I-TCkZuRMjYm3tWQkQ3VoPXLNeOcN_ss,19731
transformers/kernels/mra/cuda_kernel.cu,sha256=LxxRYTymSoBEQpWXHA0PMzwZwpolcwX7mFAjwU8-ZMc,11678
transformers/kernels/mra/cuda_kernel.h,sha256=UJvYq_MDzhcp07bZpYcOBn8ZGFcf_Ax1dynuiVTBvmA,1682
transformers/kernels/mra/cuda_launch.cu,sha256=Ox5MTACriC30CGyn-g1Kb5EgQSMAZSaN6fpit3xLFWc,4072
transformers/kernels/mra/cuda_launch.h,sha256=RVCkN_euasvgPK0zADNRvRYGWd4ah5l9X-7UG_AcdH8,707
transformers/kernels/mra/torch_extension.cpp,sha256=N0YdBLVX0lZabckJzV_RYTHS2atCNvn13E4Ivobt25g,1405
transformers/kernels/rwkv/wkv_cuda.cu,sha256=EvaUrEnw_qr2EjMKP-Pq7VPzFfGlMJnFhdHNLtn1fPU,6219
transformers/kernels/rwkv/wkv_cuda_bf16.cu,sha256=DG9hTtOAlrnpDFahjt-MmnOxjMuhGU55GPsmV21HtrQ,6633
transformers/kernels/rwkv/wkv_op.cpp,sha256=qSExhKdT6p3hyaTv5SypCnH_c7EmaX6HbhTcCntvZWg,4022
transformers/kernels/yoso/common.h,sha256=Tq2rOUtE8Y4DRAUrRISvwIwVI3u8JBf21WgWSAYiDlQ,273
transformers/kernels/yoso/common_cuda.h,sha256=Sji70AuVcuZSotLF7Gotmun9MJuOHo8wEkxizKXLRtc,258
transformers/kernels/yoso/common_cuda_device.h,sha256=y6WUgAiapnMKqthRMS5s-DMSWNVkar_i8g4KPFvqiuk,2063
transformers/kernels/yoso/fast_lsh_cumulation.cu,sha256=LA4LGNgyXT3osIyQtFBcRanSyNQWm8yqmpz7AeLP7cw,19061
transformers/kernels/yoso/fast_lsh_cumulation.h,sha256=1cTWZjOm751HGiEB5P-UPJ8SE1VO7XRyXmBgyxYDyjI,1575
transformers/kernels/yoso/fast_lsh_cumulation_cuda.cu,sha256=HKGLWl-WFz5BXjaAPHTNTbG6IUkJjhBdvFf2K7hrDVQ,32870
transformers/kernels/yoso/fast_lsh_cumulation_cuda.h,sha256=_KGI8HQbVFtCN5KAcSGpyiJ2foGi26RKen138CUc2fY,5490
transformers/kernels/yoso/fast_lsh_cumulation_torch.cpp,sha256=-Rh7o39Z3rtOPwNnEM-c51TCqywpVdK0WVaA7VRrXbQ,3154
transformers/loss/__init__.py,sha256=qETsqCwayu6Ymj_J4_A_eiwiaMRHQ0noWKM35naanzc,606
transformers/loss/loss_deformable_detr.py,sha256=6nybwni_dj2_H8UEe3e2o3kymMVHcbFhPpjPyx87Kqc,7335
transformers/loss/loss_for_object_detection.py,sha256=__Siy5m1zEQPZk0JbUOcA3ZtoJ0jRt0qq7dwGR6MGtA,24594
transformers/loss/loss_rt_detr.py,sha256=04kG6ucpCbsuim9etCZpoWLS2ZKI3Ui0PzDdhGPFqFY,21734
transformers/loss/loss_utils.py,sha256=P84HARp8kntCT8tzqfUu8hqDRsgB2gXO2iaNTh_RBTs,5109
transformers/models/__init__.py,sha256=CiFF-RoZ6-ws8tjZO2VtF7vdfWLBZuxD2rHrBTHu4xA,4434
transformers/models/albert/__init__.py,sha256=WjQ4NtNxKNj7Hvk9lA2OXmdgD_SNFp1wLS2eeL3-WoE,1154
transformers/models/albert/configuration_albert.py,sha256=nwBi1Gg1MRw_z-9Pwr7kjtJykLOhj5_zJC9zGzggrfQ,8137
transformers/models/albert/convert_albert_original_tf_checkpoint_to_pytorch.py,sha256=sj2zzT7a1poQRuEpKB_9RUHaIPV6uc0P5zhgvSBfZV0,2161
transformers/models/albert/modeling_albert.py,sha256=rbM9k-zdZfH_qqKQCwkgxAaJKZQU2yM2aIJheQaTsSo,64790
transformers/models/albert/modeling_flax_albert.py,sha256=_oJ81lgLHwfYC-9DQq96Isrlj99jI02X5mol4eyu_j4,41027
transformers/models/albert/modeling_tf_albert.py,sha256=l4cknO2O9IvaGgQgHD5drqAWkEjfOvMjdc6oBooSoYc,69145
transformers/models/albert/tokenization_albert.py,sha256=y71xBZjcpGMdpWk6PC8ljQ1O4V4FLPDHHk6QF2e-LbY,14531
transformers/models/albert/tokenization_albert_fast.py,sha256=2I7-4DbfP_xh3kib611aL7FgFY3TE3R3jrCCY0KUquw,8866
transformers/models/align/__init__.py,sha256=QqTKk-Z4BylY6EkBSlYvKXVhT2te-m2Al626OUAz-r4,1027
transformers/models/align/configuration_align.py,sha256=Q6Ivey0Ujl3mcQdmd6uxCnVc0MBSKJ0qFZM7dWyT5UI,18166
transformers/models/align/convert_align_tf_to_hf.py,sha256=tzPoEMyLV_ckVngYdvJ6uAFZ6RgsuX55JYjEkIMtPTg,15536
transformers/models/align/modeling_align.py,sha256=nBuKM89UxX3vfBsGky5y2Om_TikGIJ8yhKiUW-AaWC4,71972
transformers/models/align/processing_align.py,sha256=BrpIj1U9nYwxnuOQM9NmsMwuxWJxK-2gyHxqhp-b9Ik,7311
transformers/models/altclip/__init__.py,sha256=405IijUCYr1EGvOqg1xzds_GHOlxCl0HCsf1rI0wtPY,1033
transformers/models/altclip/configuration_altclip.py,sha256=ojTxheh8bskubpoarCP8UXQVOWVmRzgArIrRTpmlUbM,19779
transformers/models/altclip/modeling_altclip.py,sha256=b2hEJSG_wtTq1EBF1XbvB1zVr0JJPkULJqUEgjui_VQ,81014
transformers/models/altclip/processing_altclip.py,sha256=ak5ALJowiGiUcIrgMOIK1OaBYGqneQQuwcxoEL_1qSI,6904
transformers/models/audio_spectrogram_transformer/__init__.py,sha256=WkcfCgQ_K5we_2ErzqZoiJY8WcWpPgT5Trw3uN09Fy8,1860
transformers/models/audio_spectrogram_transformer/configuration_audio_spectrogram_transformer.py,sha256=Opk8e1P7IjhZQgj8Pa5YzesLFPMSUfMsRHLlLY4BgFk,5881
transformers/models/audio_spectrogram_transformer/convert_audio_spectrogram_transformer_original_to_pytorch.py,sha256=clecd7YFd7_RqtMhbHrCjnC7KCiPZSRcJ7Ee7Eg_9Fw,11125
transformers/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.py,sha256=CLMcdUUk8ehA2PC9wEBwvWd68tIMFtZswNhVbVwXWc8,9908
transformers/models/audio_spectrogram_transformer/modeling_audio_spectrogram_transformer.py,sha256=r-c_RS_k8zdyCvteDfS8JrzQV5oJpFx5f9Ey3xyR3KA,28349
transformers/models/auto/__init__.py,sha256=qvOz4k4IwdYsnf0UAFepP4BAiaxxpxhNKw13cUS91kI,16946
transformers/models/auto/auto_factory.py,sha256=wBc5SzZKV1CxyR3RzocS9Vr4kCHCqM0XBTqthpjngb0,44542
transformers/models/auto/configuration_auto.py,sha256=CIPYMnVEGL5e6e-98G1XiHDT2VS7_PUQiSb48o4cu7I,41232
transformers/models/auto/feature_extraction_auto.py,sha256=u_PFerxtgoUZpGD0B1Wxtzy9jfpZvIgBlTHTs_fm4So,19674
transformers/models/auto/image_processing_auto.py,sha256=Bbx9PL71ZLIioCB3QInJr0n-qilxu2hnfy9m7Q0VTms,28620
transformers/models/auto/modeling_auto.py,sha256=h_nsHRBwMnpd8bRZJ4AguDRvLwtfaefQVNVc8cbnqIY,74732
transformers/models/auto/modeling_flax_auto.py,sha256=UTBFXa5sZxdO_663zWvhcJGpdt1bhpLCl337eZLB7H0,14568
transformers/models/auto/modeling_tf_auto.py,sha256=hgO8Re6rW2lPm1r9etcjQLmgF2aiIVL5m1aWyyZ5szE,28420
transformers/models/auto/processing_auto.py,sha256=Z_cok1iIJB9z8gLJJgTtvNV0IGgBptYDnxy3dl-FSLI,17639
transformers/models/auto/tokenization_auto.py,sha256=7XEA8k_HzeYIdHq8yQBvC3yhzi02ZMHNg3rk4i-pVKA,50242
transformers/models/autoformer/__init__.py,sha256=5xv9eb6R-4PmNJ4v-ogeo7pgobDRokFl4nWqqjnWII0,1691
transformers/models/autoformer/configuration_autoformer.py,sha256=IqjxujPU316HLjmljrBarAH8rnTsbJrlmrwyR7oTLPU,12165
transformers/models/autoformer/modeling_autoformer.py,sha256=nH0M5AHGv6ft8hcVU2PpGPZOBfh0gGnNM2QZ3oZx0Ms,108745
transformers/models/bark/__init__.py,sha256=0ycNDLHODu0Xlo6TJoe-0AJFyEHwUb8biq6aCAtmKRw,2028
transformers/models/bark/configuration_bark.py,sha256=JG07VbDPKgp8INf4NlWC4qPmVQ1vXpM_8ZpP8Vwy_Z0,12835
transformers/models/bark/convert_suno_to_hf.py,sha256=9Al9mccdhIX1pT3k_bECmGOXii25gx6D1A7dSs94U4Y,9374
transformers/models/bark/generation_configuration_bark.py,sha256=6YiZkHuloUVbfqzR36xmvhKm5SZ2-b2MWMHSwMvRWTA,14947
transformers/models/bark/modeling_bark.py,sha256=hMiJaB4PVuqjYNPZnBJwycOvezeqwE7NFP1CWJkJp8k,82539
transformers/models/bark/processing_bark.py,sha256=tJrG0Oc5z90mNQD8wZNFxKZQnFT_ikFeAMM9xljT3MA,13310
transformers/models/bart/__init__.py,sha256=1waRouI1H1jDBwRDuupPEI3b9ZNUC3E4gPl9IBjZr34,4229
transformers/models/bart/configuration_bart.py,sha256=LO1wezTZSHet8E4-o54nHosRErJqYPj8FBGHIReBOZ8,18783
transformers/models/bart/convert_bart_original_pytorch_checkpoint_to_pytorch.py,sha256=kJ1H5FbOt_p3YqeR8ezBKaOV0Po2UcOHJRiRdUcJ90c,6054
transformers/models/bart/modeling_bart.py,sha256=oN9ecR5UZS6jmWuXV47VM1vi6vdC7O6p5NJbZNtpC0A,102483
transformers/models/bart/modeling_flax_bart.py,sha256=IJCatuRYnsYgDresrfjfL0I4WZs9YZYqufMXTWp5eZg,82714
transformers/models/bart/modeling_tf_bart.py,sha256=pHqwdJ68DT4V4AWdVWr8Sz0UCg6SsqBSUdejwnCoFN0,80771
transformers/models/bart/tokenization_bart.py,sha256=fqHZCAZwmM4QoFAk6unDGwrbXtZkV1kPWMsVIu7HrPg,16250
transformers/models/bart/tokenization_bart_fast.py,sha256=h-DIyLB7ii7QjTUFF78nU8e5fPVEMUCXaPKkHqMI71E,11723
transformers/models/barthez/__init__.py,sha256=7IXg6okZoJ10NCYRWn0GvoWWUvGUN27eIw7CzJ5CVGA,1848
transformers/models/barthez/tokenization_barthez.py,sha256=I0jL8PMdPvxb_uLh7IRqVnSnzzMgo_dSx8J_wB_tGlg,12064
transformers/models/barthez/tokenization_barthez_fast.py,sha256=DAC2_iaNphgJCF1ZQHFUDHDPW47d8JCPzxz30lhMTY4,7836
transformers/models/bartpho/__init__.py,sha256=Q0mAOPJGQaHHigdajLg5-2TPOw9NWw5uIRQlmfhh8Ds,1362
transformers/models/bartpho/tokenization_bartpho.py,sha256=bxgUG0U4zgzBJKapa7snw3facOwyj12YJePC-jwQDno,13523
transformers/models/beit/__init__.py,sha256=zVVV08SMWyks3fXFUsyS5jK2-dZGLOyvcZmp4RvnWpY,3171
transformers/models/beit/configuration_beit.py,sha256=4oK_IfBO94rh0qkLYwhdW3qthVpPyQQOMg54HU79F1U,11548
transformers/models/beit/convert_beit_unilm_to_pytorch.py,sha256=Pdqslow71bJYQioU21U5rWEjXSQC4z_dOnVgKfPxWYI,16601
transformers/models/beit/feature_extraction_beit.py,sha256=C9wchKLt3K__wzqOkDWsbK0hMPzVn9HZtm5KPI5Oq2s,1172
transformers/models/beit/image_processing_beit.py,sha256=3-vR1CISdkww0gHbmmfzukEp108okffY46PhhPZZnS8,24416
transformers/models/beit/modeling_beit.py,sha256=P-jxX4rJPoCt2xz6i-6np66QK1idDnZmd3wijZVtY2U,66591
transformers/models/beit/modeling_flax_beit.py,sha256=9_xkFN7xtiLrxbShhpX8EgpY8kuOKIui-OlRidmNUAI,36996
transformers/models/bert/__init__.py,sha256=HWErikSRkfAKjM5ugAe71O36-EzQ3wJdvgYCErXI4Ew,5789
transformers/models/bert/configuration_bert.py,sha256=OQ1E5rqkz3RcpeMBO1sybR7VCoKefI938yyILrbcot8,7244
transformers/models/bert/convert_bert_original_tf2_checkpoint_to_pytorch.py,sha256=q7RA_4S9OkAxFdmmGUV_Lf9qKYoRshJJbQMAHxscsRU,10491
transformers/models/bert/convert_bert_original_tf_checkpoint_to_pytorch.py,sha256=eSJs7TBQPBfZBPAmBJ7L8JKqKWBewvWKsnVbklmHkNc,2158
transformers/models/bert/convert_bert_pytorch_checkpoint_to_original_tf.py,sha256=6nISsCdgO_sJFFiLpnkGGsmTqC9Yp-gzDPDM-EafVXA,4112
transformers/models/bert/convert_bert_token_dropping_original_tf2_checkpoint_to_pytorch.py,sha256=ksipaccVHXMHrGrNJp_zAHemziQvYMMcG7fzIo00DQw,7607
transformers/models/bert/modeling_bert.py,sha256=NJO_9dqQ_czpja1chKr-TTzhxVDc2TvJkokwmVNVnso,89949
transformers/models/bert/modeling_flax_bert.py,sha256=UMRUMxvvwu8oIzkLfVjXWP9Y47WolZPtZFELypsG-pg,63672
transformers/models/bert/modeling_tf_bert.py,sha256=NbS-w_QdlhN509DcrPvdYFzO4bzfHSRMvfHNF0kafQE,94293
transformers/models/bert/tokenization_bert.py,sha256=KtRTr0d6hG4suSYat9dbBbsRGLVohXOAdKxROPsfTkw,20845
transformers/models/bert/tokenization_bert_fast.py,sha256=FoAutpMtmt_D77Z82RtBcttl8Cl5P2Rdt_HFIKUT2m8,7652
transformers/models/bert/tokenization_bert_tf.py,sha256=1zWzz3FPrh5zWqRG7YVY_wIVCzzB8iNGR6MGx48ke3c,11895
transformers/models/bert_generation/__init__.py,sha256=2XUvSVePne5Hspjzn6l_PonKfZ9WXjRBub9bevOv8R4,2275
transformers/models/bert_generation/configuration_bert_generation.py,sha256=zrKF15fHFYwjaQxRB0dOjQWYVoHl1DZfQ90d3gkamlU,6340
transformers/models/bert_generation/modeling_bert_generation.py,sha256=ZEPkddj_JWEgY_3V5ls1e7moDqr9KFHKE6BvfopDFeI,47551
transformers/models/bert_generation/tokenization_bert_generation.py,sha256=rN1VJOLn-nw8AU3ev9DXeXQs0xzzEt7qt_eI6UtMGJU,7076
transformers/models/bert_japanese/__init__.py,sha256=6prQNXS2J4cWXqAqkqDyxNmzx-vaFQtOjJQio-ZUc4g,1053
transformers/models/bert_japanese/tokenization_bert_japanese.py,sha256=j1X-ktJNEoR-MyXenFxnM1dtSvQhMr1VB8QirAkgigc,39008
transformers/models/bertweet/__init__.py,sha256=sXE2NweoWp8UIaJkuSaLSw4EaSEzpWwBe3pegec_Kj0,959
transformers/models/bertweet/tokenization_bertweet.py,sha256=Zgm2IY94qRnlptRVyc6ibyKCG-FptmGaRPrt8ehWmNM,26986
transformers/models/big_bird/__init__.py,sha256=rBiXDLcASar91jtVI6lzsJBIrydXU_CJUHdIkY49LQs,4390
transformers/models/big_bird/configuration_big_bird.py,sha256=1YAMtLXa4rK4fKUCGcj7AgM1faAoKC7O3UglBl-1EQI,7832
transformers/models/big_bird/convert_bigbird_original_tf_checkpoint_to_pytorch.py,sha256=qWXzJVxALd_0AYy1OETdK7LuVZFICtSiqv2vtv04zE0,2492
transformers/models/big_bird/modeling_big_bird.py,sha256=AiSuLtrt9iO-iGSFn127v1klPX7UDR2Y4ZtqNmJod88,141513
transformers/models/big_bird/modeling_flax_big_bird.py,sha256=ePVW-6VwD8sgJYIlX4eWv0EVNaInVosJW_CtqlyzpGs,109510
transformers/models/big_bird/tokenization_big_bird.py,sha256=gdDnyZbg5JcymJHvjRk7lgAelB4hm9hEM2loRL39n5o,14217
transformers/models/big_bird/tokenization_big_bird_fast.py,sha256=aHbMkEFSbFIz2DNd9xNJPOijyojQnIF1lASFJfbZMbM,10166
transformers/models/bigbird_pegasus/__init__.py,sha256=FdQfzkjpqYAf02mpcAIkFoGoN8m9Gwfgz4vb_wxYJHQ,2088
transformers/models/bigbird_pegasus/configuration_bigbird_pegasus.py,sha256=PMwMgvudJ2vCl9jUj1AexknnjGI8w5BdOdM_Or9mbZU,19215
transformers/models/bigbird_pegasus/convert_bigbird_pegasus_tf_to_pytorch.py,sha256=Wc7aoNvtzxt-DPi655Kl30CgDgq_hp08psISb8dWpLU,6288
transformers/models/bigbird_pegasus/modeling_bigbird_pegasus.py,sha256=5dQ6CceC4T5l8Sept2_Q8Zfj52rDNqnRTLylzRVk1bY,144490
transformers/models/biogpt/__init__.py,sha256=Gwpn8dB_Bc1wXlZq9s7gGBwA3DvQR6cx1GBYqPlTIMg,1882
transformers/models/biogpt/configuration_biogpt.py,sha256=08V2Gvyw0oQnuSXSLgZvt3GNDGZ4jnnH5V6eQWQqjUg,6178
transformers/models/biogpt/convert_biogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=5zNYzaEy7QPc99LCHTcofXSCI3tr0pzlIpFpwT1ZgN0,10578
transformers/models/biogpt/modeling_biogpt.py,sha256=vYlxI9I9pKvNmUA4qqw54Rc8tykXVbmqkY7q_yH_Oq4,47210
transformers/models/biogpt/tokenization_biogpt.py,sha256=SW4-KCJ8piZPhAZK75hX-NqkyAN9eYkVt4fghFfOTVA,13257
transformers/models/bit/__init__.py,sha256=chlptH4-lUvMRhJ1Hkf1s7BrMJckyhi8K3DghWWPj_k,2080
transformers/models/bit/configuration_bit.py,sha256=gBrTquEDWGjBOhwsF4O171YesHpqRsy4IrdSINcucK4,6269
transformers/models/bit/convert_bit_to_pytorch.py,sha256=fheumYRIVQwZoZCWcl0btZh7LhandaEeAoS6itUmkwk,5954
transformers/models/bit/image_processing_bit.py,sha256=X_pK8oGtRVvMoKO2om4GOL42r2RtHSGhCFcNTEyIGRU,15790
transformers/models/bit/modeling_bit.py,sha256=XwYaTZa09MtUlLOvGz9bs0zlYzmC5Jzm0kFPtHrLa8E,32199
transformers/models/blenderbot/__init__.py,sha256=RzwSoueJsJE2O3rQvrpWqrL2n-j4-HI4fG6iID2cDYI,3823
transformers/models/blenderbot/configuration_blenderbot.py,sha256=d9ammp_ehMYsLl3hw5Q8lK0vvHaZQK1ZVinFaLhi5uE,18781
transformers/models/blenderbot/convert_blenderbot_original_pytorch_checkpoint_to_pytorch.py,sha256=86QBWYTeyJvxMUOfxqmGHwpDneadfqbEGSujMYw3yuU,3702
transformers/models/blenderbot/modeling_blenderbot.py,sha256=KOwRz6-uvrWyHCnYmQMUti1liqWNayCu8TExHQRIuTY,73945
transformers/models/blenderbot/modeling_flax_blenderbot.py,sha256=cvS4Pzq8B61WHLKM_LmItSB5wY_jfNJgy6DVDhzofuk,64984
transformers/models/blenderbot/modeling_tf_blenderbot.py,sha256=8ANVm-AnugLGSGpaYJLQRybg-GnLxCih92FDEShlsjw,72694
transformers/models/blenderbot/tokenization_blenderbot.py,sha256=Ln4OCfpYoYY4I83CMAyli0wzUKpGh2PwEQcgFTZjU0g,18202
transformers/models/blenderbot/tokenization_blenderbot_fast.py,sha256=PKTS3ffaeYQsiq94CkXLZYNN4OiaISBjxsVsfgpGBsU,12890
transformers/models/blenderbot_small/__init__.py,sha256=PtsaXqUCp7b95CsjdWPFMJu6dy2ZQBKaU2T2k1E89M4,4031
transformers/models/blenderbot_small/configuration_blenderbot_small.py,sha256=on2JyBGUYPH5q1hTT7KAPnODclvuJ_w6NJfRQBwZ9zs,18213
transformers/models/blenderbot_small/modeling_blenderbot_small.py,sha256=EQEFWeBSGLXijkp9PwSAxrXwzUru0_4t6d1XlWdJZv8,71992
transformers/models/blenderbot_small/modeling_flax_blenderbot_small.py,sha256=kNoW_xxZluiAntCS5lokqBhyQNPe8a7UVEsADMAO1z0,65944
transformers/models/blenderbot_small/modeling_tf_blenderbot_small.py,sha256=ueMse3vwu4mqDf5pLpORjKNHpPZWfbMMJWwilJoiauM,71606
transformers/models/blenderbot_small/tokenization_blenderbot_small.py,sha256=duXcElBrek8orzd21aKFI3qHnNbIZfJaZyGZ27qT-jI,7923
transformers/models/blenderbot_small/tokenization_blenderbot_small_fast.py,sha256=g5-Ad7oGtoTcamB5HHRCMah9NmntiGsulI03djIX9GY,3322
transformers/models/blip/__init__.py,sha256=sqnXET78HA864F5e7zbXks3PmHhE88rsQ9qQuI4OLAc,3416
transformers/models/blip/configuration_blip.py,sha256=7QX-b9m2MtWIWmCzCuwM1N4xmmCTNbyw0XeP3VIO4PQ,16474
transformers/models/blip/convert_blip_original_pytorch_to_hf.py,sha256=7vL8HN4EsJMkUJt74p2nrFVvwRLmKmd43KJAxmEeGi0,6970
transformers/models/blip/image_processing_blip.py,sha256=Gwb7S1Vgl2iVLYFVNmBHUsLXMcnc2cz1EU2hCRoR9Wc,15225
transformers/models/blip/modeling_blip.py,sha256=jMPsLEALydUqZMOcf6mPAQ3QJQ7OLh6FtCaux2XHeRM,67463
transformers/models/blip/modeling_blip_text.py,sha256=2Ns8sSRsHOHtV-Ksixy68X-IWEvW215rUHDZ6v1Krug,44055
transformers/models/blip/modeling_tf_blip.py,sha256=owkHeUXz-Zx1FNN35vNOae-WlMqZO4MHKB4zidpBm-Q,71312
transformers/models/blip/modeling_tf_blip_text.py,sha256=iJiYcnZpqJhoNrfUcxPxtokT_qMJGgLyz1hAcAWZ-t4,49972
transformers/models/blip/processing_blip.py,sha256=vvAb4oTW6YRGFlCNXYRSgWCJKqRTHymB_AVqmrbLrKU,5867
transformers/models/blip_2/__init__.py,sha256=sA2DAmdnXYrw8_ST1FiPMd8omHU-V2AbIvHRAWOkCV0,2207
transformers/models/blip_2/configuration_blip_2.py,sha256=CuXoFgTLjEjJgauT8NK_RHzNzXkx17mUGizG1m5LKJk,17672
transformers/models/blip_2/convert_blip_2_original_to_pytorch.py,sha256=eJvvNEW6FEXZGs6W6OYtiFr0s51iVdar9Ax38F1F-ZU,16799
transformers/models/blip_2/modeling_blip_2.py,sha256=AVq4UOFJd_TgJBhJBoqDbZD7wpEZiDZgKNiEL5hqvd4,114167
transformers/models/blip_2/processing_blip_2.py,sha256=otLsdTjVmNolUE8TPAB0iMgG5IeNhRR33EBF-Kmm3eU,8745
transformers/models/bloom/__init__.py,sha256=nV2bxWJBx9x3oa5SidEJB9XAnRLwu1LDqi0bFfz7f4Q,2926
transformers/models/bloom/configuration_bloom.py,sha256=5BF8i4K8j3ACmkFnq5iGlsPnehhUxukn376O9AOgU30,10138
transformers/models/bloom/convert_bloom_original_checkpoint_to_pytorch.py,sha256=WtPFsgC47dhGDW6dHm5PC4SaZgZ6tF3umTFd8Qiw-1Q,10301
transformers/models/bloom/modeling_bloom.py,sha256=12tCjEmV6Vl5fd45KLxPxVl9y8gWmT5p0M3wkLFi18M,61793
transformers/models/bloom/modeling_flax_bloom.py,sha256=zBWwHZI6OBs9S1h9JSSAaEnskPKpa8jHn5AROhbLXpw,30092
transformers/models/bloom/tokenization_bloom_fast.py,sha256=qqPGNUC8MffBXuLxIeoUbjy5U8bR5Nih6CfQFuW4zUA,6249
transformers/models/bridgetower/__init__.py,sha256=FK_XyizrV1Qzgy7F_Qbh_olKkVi0KjEQ-VQCqS-9vJo,2652
transformers/models/bridgetower/configuration_bridgetower.py,sha256=b_oHdh7u1A0nWRUDnprSJojcjAdS_1gYw9Ut2zS1-kI,16209
transformers/models/bridgetower/image_processing_bridgetower.py,sha256=AtR6UGdXo4SwAsz4AeG_XRov_O504-Uy0ZulP8lMpSM,26291
transformers/models/bridgetower/modeling_bridgetower.py,sha256=TbUhZcV-cGMmLdoIeo9Qp907v_K7AgChBRuyurA-uFw,91387
transformers/models/bridgetower/processing_bridgetower.py,sha256=vfdK1Jx5xYUz3TGfvYE32MdhwhpDgInj5bKGet9PB8I,4400
transformers/models/bros/__init__.py,sha256=HwARvDde808WqrRVjT5CxnacGOQpyxpVZF1EJ7wsCUQ,2277
transformers/models/bros/configuration_bros.py,sha256=VTx0gJQUJQepZFmYP7HPiLaLx04Kdz3CyxgAqkKLLEs,6391
transformers/models/bros/convert_bros_to_pytorch.py,sha256=kxZDGzvIYxz9hbIzzJOfOj5tixji5efb2884rqwoY6A,4871
transformers/models/bros/modeling_bros.py,sha256=UBiX26UhzlfXlsfhhIxEl-nYkmZH8Ng_dtujCoTawsM,57838
transformers/models/bros/processing_bros.py,sha256=FQUu5czHHvQzZ1P5N9GhfjZu4cmZw_mYKuX0VNjrB54,4193
transformers/models/byt5/__init__.py,sha256=06YhQd8TFNbc9lU5qzERZUdcSWIFxOeBOaqQh6S4WC4,942
transformers/models/byt5/convert_byt5_original_tf_checkpoint_to_pytorch.py,sha256=LEibHPdlDdKdyB6XHB5s7pHRsqB5qQxUWN93H8G_q5k,2119
transformers/models/byt5/tokenization_byt5.py,sha256=MioRjnE5Bco3OgdbKcjfYZ7BMCm1ymu5ydwZUGrzC5U,10029
transformers/models/camembert/__init__.py,sha256=t1UUX2ZobpQSWOvBrjnshAEI0EVlDwngdxH-GIMyXTU,4145
transformers/models/camembert/configuration_camembert.py,sha256=GxEMDYRqz9PXWIl_z9WU0_sceBrIGINiKx6abd0pogo,7349
transformers/models/camembert/modeling_camembert.py,sha256=sF-c9RrR6QI5XOh8Fr2PkT9byi9yfhYeGPt_UUs6GWE,79165
transformers/models/camembert/modeling_tf_camembert.py,sha256=O3fafrTFS8DIuwinVZhxz4PisUQHjMHeP5Woe5WukdA,81530
transformers/models/camembert/tokenization_camembert.py,sha256=D_CDhvAXCbAQ-l2JQQ-H2LNjjnkggP5uvgsbMldZa0A,13976
transformers/models/camembert/tokenization_camembert_fast.py,sha256=g48XHTSlBEN99pWh8qyYh7wyJTkMJPd_x6xUdoyEzzA,8272
transformers/models/canine/__init__.py,sha256=O2uU4VpK9tqfnyM9-KsksCeIp46u9RcL5fJ1m4eNviM,2096
transformers/models/canine/configuration_canine.py,sha256=8oAa0oJcRE7vPVtQRsxvsXH0Celi3QMbJTrk6rQANb0,6555
transformers/models/canine/convert_canine_original_tf_checkpoint_to_pytorch.py,sha256=zJ6VDpE58I4-ntOXDUCKPYXPnnhWkuYXCfejDchr9jY,2116
transformers/models/canine/modeling_canine.py,sha256=Nsv1UzVH_B7ve5R08HOT6wkcutDJReznF3KR2PMb7m8,73394
transformers/models/canine/tokenization_canine.py,sha256=bLQPsvTpk8GFuH3rsUR6o0l6f9Ldvb4S3Os0H-oFQQc,9287
transformers/models/chameleon/__init__.py,sha256=b8WOpzMVLP34ESkZnCmvO4gc-kGFFL0YW-ERjZQBgfU,2440
transformers/models/chameleon/configuration_chameleon.py,sha256=Ev7K4eenCbDlZgXhD8t_ihnHVnkK8TU0cJeUIy50aGI,13149
transformers/models/chameleon/convert_chameleon_weights_to_hf.py,sha256=Rh9cpW_iOQgTxdhVGBVvMgNRRn4eVMdXCljz9W9bv5Y,20301
transformers/models/chameleon/image_processing_chameleon.py,sha256=dkfFkT91SSb_9AyL8y3EMCI-Jn0ujgGwjv-TYbxIrqY,17501
transformers/models/chameleon/modeling_chameleon.py,sha256=_EW1LJXfXZEcZW-IsfTybnXoNdlrsIe6EfOpA-2kL2Q,77666
transformers/models/chameleon/processing_chameleon.py,sha256=ghWuFgZLTPbpKOBQ-qm1O8JgmqneCPWVW7Fb6j4zuso,8198
transformers/models/chinese_clip/__init__.py,sha256=KQXPKJM-dsey8SwyxD7CRSaoOEEhuVMHWDet9xcDPGQ,2703
transformers/models/chinese_clip/configuration_chinese_clip.py,sha256=n5dF26ntwtSqzhseCEUzSixh09gnzFzC1P0mMM9N0r0,22329
transformers/models/chinese_clip/convert_chinese_clip_original_pytorch_to_hf.py,sha256=-0bnVcdXxStmygkyj6S1hIGCVbpEbe3cM7AoshHH5ZE,5069
transformers/models/chinese_clip/feature_extraction_chinese_clip.py,sha256=znduyOyJ-Qdx4MC5CPb6MFZ-Wrb5PLgHWRh0xfoULR0,1247
transformers/models/chinese_clip/image_processing_chinese_clip.py,sha256=FKtFCtL7j56eMR8cQ7wxwS7GpvrW2nXUTkQBACIM-TQ,15341
transformers/models/chinese_clip/modeling_chinese_clip.py,sha256=wee-8tK0OEWNxJiHseQgoPrPE8BQ18AYjwwkU7w6oRY,76354
transformers/models/chinese_clip/processing_chinese_clip.py,sha256=lsKLyqDJPLY530ARxt5JkJ3zwE07cZyOLumTLehSofU,7492
transformers/models/clap/__init__.py,sha256=MrwPO_4Wek_q0Lp735dzBv5HyOO43W8IeGk4cuu6HY4,2138
transformers/models/clap/configuration_clap.py,sha256=Ho885NS8ExbtZNoJ9dDsRUK-S5dEP_Ous1QqoEQ_EJI,20383
transformers/models/clap/convert_clap_original_pytorch_to_hf.py,sha256=FqHoVAYXIzfUY9342azwlm9zfSP7QdS8p-u9Q6RE_K4,5149
transformers/models/clap/feature_extraction_clap.py,sha256=8wvUSggjHicJBtnTDF56UsV-J2Nvelu16Pk7UMaim9o,18691
transformers/models/clap/modeling_clap.py,sha256=EQxL9MG1a61G57vDVlMhRaST8fl70v-0qgrvPFCAFK4,104796
transformers/models/clap/processing_clap.py,sha256=Ayo5mK5ZcvEvcEp9DhHOLFxaRJ9gwTNRhxi96WSHZdk,5678
transformers/models/clip/__init__.py,sha256=VVJWqRbDN0AUa7BrJMaexjKXJtJ3NgsCmRNQTAM4aM0,4909
transformers/models/clip/configuration_clip.py,sha256=NzuGoSFLSGu-70X4pIPvgEOwFbu2yiS6UXdRSffHUUo,20896
transformers/models/clip/convert_clip_original_pytorch_to_hf.py,sha256=EOdVlGGBObrXAIdRyND_Ko3G1ZWf0miyga1QcAH9Q8I,5570
transformers/models/clip/feature_extraction_clip.py,sha256=hgRfD-s9DoI7tzDLAJ0EW3rSbkY9dOiGqoGClOiRiBM,1172
transformers/models/clip/image_processing_clip.py,sha256=DTQtu9Hj2xkF4wLmWRuSIq4qqmkcMGfD7AhprdfG6MY,16768
transformers/models/clip/modeling_clip.py,sha256=7mw5lqpsJckFG7oZYSAOWPzcP_2n-DEg6D5jshlP88E,73879
transformers/models/clip/modeling_flax_clip.py,sha256=4uabm9t6i4bnqRR3DZrGk7X1NcaV78L6b6E6i0Gkl2U,50517
transformers/models/clip/modeling_tf_clip.py,sha256=TEom4goA3lt5lH8X4N6P6tnxhLbJHHiGZnm15zN5aGw,60358
transformers/models/clip/processing_clip.py,sha256=xXp4RfloqWH1K1dFCL81jGvaOowCNQ2s0CU1vz2ClP8,7148
transformers/models/clip/tokenization_clip.py,sha256=quguw9d8H-YnrtA08fouUdjTM_Ti16x5uMBKb095O7A,20576
transformers/models/clip/tokenization_clip_fast.py,sha256=IB9fydB2vYm5llPx-NmmpO5oIgFcxbu2FZubWrs16a8,6746
transformers/models/clipseg/__init__.py,sha256=C9quQq_rkeAfAm2P2CFj9VDG7ZMdH8Q_l0Wp8LYe4F0,1983
transformers/models/clipseg/configuration_clipseg.py,sha256=deTnGyDuir1SJXmiTa6QIiEUolPK4J5_1Rnkc4unXas,20930
transformers/models/clipseg/convert_clipseg_original_pytorch_to_hf.py,sha256=kYyPxdpdtt6nSxD65tXUTMbN0xPyyzjfTOOMbQ8OL0Y,11114
transformers/models/clipseg/modeling_clipseg.py,sha256=JCRg7oBYmPiWIXWcoo254_Z9DHrJf5RUHq3kpBFrJbk,66620
transformers/models/clipseg/processing_clipseg.py,sha256=dm7u-6S5Hg1ITAc0lYzXRJssiR92LOMkWbnR7p4eHzE,7790
transformers/models/clvp/__init__.py,sha256=uLsRO6PqkUJo5CfxII7wW4AN2G944EQwS_EonAoKwMc,2212
transformers/models/clvp/configuration_clvp.py,sha256=PmxrzTpXR1efn7Od-JJh29W27Br_ixO6lbVBJSb8S3k,20933
transformers/models/clvp/convert_clvp_to_hf.py,sha256=1WYf_vwj1CeQ_VU9iMqu7Grr_MmlAsaKEK1Lojk6yM4,9326
transformers/models/clvp/feature_extraction_clvp.py,sha256=eEYucyWwTCALx-FnR7hR9BGUww2tgS1Bi6yvuRM4g5Q,10947
transformers/models/clvp/modeling_clvp.py,sha256=W09PMF_WOVxWwACj0OuiVhJMahEtINTSBBOQ7CJLH-w,91304
transformers/models/clvp/number_normalizer.py,sha256=lW1MjRY8PDAWjWLA-S2Fk-LVWaqkmBVCACmF2765Vps,8856
transformers/models/clvp/processing_clvp.py,sha256=ui2qQtxtTBoJ72SclUqGLH14w6RNDfeEnrM5b2J2w4Q,3604
transformers/models/clvp/tokenization_clvp.py,sha256=dNbrXIhYcqum_vonAZ7xsxvKimu1to6CdDDu5T5-0XA,14800
transformers/models/code_llama/__init__.py,sha256=S1xpVZ6cLZxN1ADmRNp7dCsoKQKnb3-Tw-HkHjHcnBY,1882
transformers/models/code_llama/tokenization_code_llama.py,sha256=RKCfqzIJPLANf4urikgBPWOY4rBLJD2sqyYBNL68UJ0,19216
transformers/models/code_llama/tokenization_code_llama_fast.py,sha256=xewKbBBbC64eaM6P1J9CWo6pfIXx_qjR-jOwKGh7w8U,16015
transformers/models/codegen/__init__.py,sha256=sPF7DZrwSndm5qiuHISm2dE0WVS5TFJaFQwOYKTGzNE,2263
transformers/models/codegen/configuration_codegen.py,sha256=wcAkmUIRzGOD_nQxaFHpItclyi1F5nwui1eF93rnoVw,9492
transformers/models/codegen/modeling_codegen.py,sha256=c4ObynXhCfdbGQlt1uqGv4ykzkBSeTmStV6iqpTzwgw,36642
transformers/models/codegen/tokenization_codegen.py,sha256=yxDlGUZCpKKb4SzTOwA4gcsQ79aMfHGT3_dTDZ0H-o4,16530
transformers/models/codegen/tokenization_codegen_fast.py,sha256=pBxZDJdp3XcW5Mpxq50bc0zFBVCz5ZW77ay3vaPBWeA,11432
transformers/models/cohere/__init__.py,sha256=Ge4jqRB26DsPZxH_-GvXnHatLWrd249yf73l0QRGjXc,2136
transformers/models/cohere/configuration_cohere.py,sha256=9r94UEq7_kgZoPHQuQF2MDSZ1quPQ236kg02q7-GaE0,10544
transformers/models/cohere/modeling_cohere.py,sha256=CHDfUvuO8QklCKVK0O8_c7nBdHUTOJSeotUjGWjCRcU,55758
transformers/models/cohere/tokenization_cohere_fast.py,sha256=io1mO5P9ASG04e1gB8MqiyK0ImuDfjkXiVE4jiURWMk,28865
transformers/models/conditional_detr/__init__.py,sha256=E-BEmIVL_HIdLs7ZuFwAO1IUlVSfRpGeUDuTxWUz3Gk,2596
transformers/models/conditional_detr/configuration_conditional_detr.py,sha256=tVlh17C6pBbT8bx5vWiD-k4j8DAscC2lDUsDb1a4GjA,13285
transformers/models/conditional_detr/convert_conditional_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=pECO3PVooqZicWn5ycbfTg69C0oicbbSigv55fVCwIM,15929
transformers/models/conditional_detr/feature_extraction_conditional_detr.py,sha256=opHXZebd-6cMJnO6RbrAdmVYmnkNzK1up_fPlHTSLrk,1553
transformers/models/conditional_detr/image_processing_conditional_detr.py,sha256=P3xo8GgUI-AaPsIZIji6I611yI3lBPhSBYBlLQXZHng,85728
transformers/models/conditional_detr/modeling_conditional_detr.py,sha256=v63fRM4_EHvV9qfZQb-BstqUDNMQ_8lVi33m6I2f6YY,103216
transformers/models/convbert/__init__.py,sha256=o7dzZB2PPMospq0oIrNa52aP9Jztd2psXfTiCui6YGE,3777
transformers/models/convbert/configuration_convbert.py,sha256=afd7mH7LyoTnSVZXGIqFOdJj_zpBrFQiJ9fa5LWs5FU,6833
transformers/models/convbert/convert_convbert_original_tf1_checkpoint_to_pytorch_and_tf2.py,sha256=vTZyGhG9v7o4rDuP9-xM26gX1EzlCda7Sn_ELT9n3Gk,2108
transformers/models/convbert/modeling_convbert.py,sha256=ImBND8ccVRdlLi8drUFMipG1q16TiNGI6_-D5AD5WA8,58322
transformers/models/convbert/modeling_tf_convbert.py,sha256=EAsYjUQYSMAVR4-iidy1zpiwgyibbDF8YQSdMZ1lDXM,61363
transformers/models/convbert/tokenization_convbert.py,sha256=11IFY9P1iGNPiNjVfFbbAty2FPT4M23Fv9hekJGo0oM,21289
transformers/models/convbert/tokenization_convbert_fast.py,sha256=uUvyyeooIaO3AjG1sPC4v_CHt4XW2C7V-76hjNyv1xg,7781
transformers/models/convnext/__init__.py,sha256=m3Fbl4B0HNR-AUeLRDuiJOEFyz8yqQjIXXHwfP9qDcc,2960
transformers/models/convnext/configuration_convnext.py,sha256=Z9Lw8yJ4EQZ5dtt20KhEROqCLOwMxN4wOgoGQo1DNkY,6130
transformers/models/convnext/convert_convnext_to_pytorch.py,sha256=h8WJeh02GFWMYkq-9MdxyiBbsmWQJFSMhNzCHrxUI8o,10219
transformers/models/convnext/feature_extraction_convnext.py,sha256=TyFMochXYlN3vKH7Ud0nXagzxGhio2Bfma4ofceR_zA,1200
transformers/models/convnext/image_processing_convnext.py,sha256=oJxkQ2ZaLQkpoO16hQ7kIMBGoRGWcllzD4xem0l7PII,15827
transformers/models/convnext/modeling_convnext.py,sha256=R5X7-cOkZSWUd1Lhjoa39VMCsay-X1AVsjJpl9YQTfU,21823
transformers/models/convnext/modeling_tf_convnext.py,sha256=oUrL6zNxFR-4xnbKJ8yQ9VyQ1BM8uimkYIGC849A3VQ,27193
transformers/models/convnextv2/__init__.py,sha256=5pJ19hB5LGhgDHzdvKpVLCveyfAx8pIMvMGxntaxcZg,2596
transformers/models/convnextv2/configuration_convnextv2.py,sha256=LoG0_XmcRMS1XS79tFeSVoyNQZ9q1S-LDzP8-9H7VE8,5531
transformers/models/convnextv2/convert_convnextv2_to_pytorch.py,sha256=Yswl5UwLP0t0tC8O2b8wix2beNaMtPy7areKFCuEccg,12473
transformers/models/convnextv2/modeling_convnextv2.py,sha256=mo-BUNy1CkwQPUT1k9NmxYk7D9QI7uTS0684ZHQ1IqY,23595
transformers/models/convnextv2/modeling_tf_convnextv2.py,sha256=VCZrI9YX6ZgdlRVqGe2tQPLTgt41v2Qdh8b_1tY05ac,27605
transformers/models/cpm/__init__.py,sha256=9SmT0nL5DgGjXxmPaQFi9GGPXWuhFic2DX2GsF-BynQ,1816
transformers/models/cpm/tokenization_cpm.py,sha256=n3oSrIyZqxMmKxHvcunWyCdePRXj4Jf3Ne5_ZghwRp8,15027
transformers/models/cpm/tokenization_cpm_fast.py,sha256=kPBVfiQelkwPOIqffQfJhh-iJlQIky2HFjF1tjRcUQc,10426
transformers/models/cpmant/__init__.py,sha256=3_P7eR-tIaj1masTqk1CFS7s9o3e8ZP3c2DWxx6MO58,1941
transformers/models/cpmant/configuration_cpmant.py,sha256=dsHPnES62-rb9ULOwA12K3VYku_HdCx5EaY5mXBj4LQ,5116
transformers/models/cpmant/modeling_cpmant.py,sha256=Fc4JcEsiX31527YnMCYpX1ZYs8HaVvBq2-bQRCHkY7g,37038
transformers/models/cpmant/tokenization_cpmant.py,sha256=3oi4drqryfXUhM2Q4FcEP2sgCb7tyvmjlFXwwVTvJRc,9704
transformers/models/ctrl/__init__.py,sha256=s588iXEw4SSQ_tqqySa9FY8XsDN8T2Xz4b1SN-CO7t4,2420
transformers/models/ctrl/configuration_ctrl.py,sha256=yB5HxF8EKPMwxPneu7s8eTLdrBw_KviGNec0FB9GbwY,4657
transformers/models/ctrl/modeling_ctrl.py,sha256=l6oL9BiztNi-nbnjcxw2nXkqg3XmGVG-uaeEWGaKBew,35725
transformers/models/ctrl/modeling_tf_ctrl.py,sha256=cB5t6WgGFG1kkN7xWOFCDCfonge2qV37CJM_L76RxiY,39635
transformers/models/ctrl/tokenization_ctrl.py,sha256=xc_a4G1ejLqOnAdIajIL4XVbAjNgRxGFA2nnsbtYSrU,8057
transformers/models/cvt/__init__.py,sha256=y-aTzOJFVO4xGssvMp0glSPOABTl0d1p7nQLpB0ux3Q,2172
transformers/models/cvt/configuration_cvt.py,sha256=1hs8UwBMA1sT7tgj5ffgYd3iS4V5zNX2lqICMA8nQ4U,6658
transformers/models/cvt/convert_cvt_original_pytorch_checkpoint_to_pytorch.py,sha256=zoed0S0LFkqKv3Or-8O512mjeVBo4dZ7bgnOCaqOU4E,13578
transformers/models/cvt/modeling_cvt.py,sha256=8mHd9bUWbZYNTXgCJ4RB7zZQExSeIsnstyCfiYn-wlA,28704
transformers/models/cvt/modeling_tf_cvt.py,sha256=WhdxLoIK_kOsV_l5lBLEfDS78FOJMRAUJsYdw4Ja4H8,43463
transformers/models/dac/__init__.py,sha256=pribSIcTvl0CwJJUeY-tJI7vTqrfinQt8cHeGg8rhDc,1676
transformers/models/dac/configuration_dac.py,sha256=E022inUoIYw2NWjo_IOVMLfazHqJiky_LqHT0IOxkhQ,4555
transformers/models/dac/convert_dac_checkpoint.py,sha256=ab2XoJEE5VsurcClyiZqQ6T57SJc9v25DEU8e0BV70Q,9435
transformers/models/dac/feature_extraction_dac.py,sha256=hKi3z_umbI1pLLjObOGhqcti_w1Nt_hw6rMIbOTdALU,7911
transformers/models/dac/modeling_dac.py,sha256=KbyYS-PYbbX9YW2a3soQNhf4mjYtFe04ZapWArpjO4g,30266
transformers/models/data2vec/__init__.py,sha256=kWv5gOdSpA7i3lUunb4f2A41Svy_PEzLsQOshWE0srs,4277
transformers/models/data2vec/configuration_data2vec_audio.py,sha256=yTleiPDKyeN-0rL5ZqhYidr8ndaKuRCReSajgksSScI,16321
transformers/models/data2vec/configuration_data2vec_text.py,sha256=j2P1eGFb65GRlu0DkcS_57PjPnZMltntQ803pZk6JJ4,7275
transformers/models/data2vec/configuration_data2vec_vision.py,sha256=UXIX55iv3jJY617KZCKdRhXKqvzL0c5zIL7DXI6ZoM0,9240
transformers/models/data2vec/convert_data2vec_audio_original_pytorch_checkpoint_to_pytorch.py,sha256=dvcTq8C9Zl4axc0gYcqYaTWTqUxgwve1O7xhXMeWu8c,10881
transformers/models/data2vec/convert_data2vec_text_original_pytorch_checkpoint_to_pytorch.py,sha256=eryzP47_SwQ2keZGhuTodpoNS4WtFVU34XoG8dBafSw,9579
transformers/models/data2vec/convert_data2vec_vision_original_pytorch_checkpoint_to_pytorch.py,sha256=qKjV-jqIgL-6i17m4yQLW_93SbPpGxQnvHjuy1xVxQU,15340
transformers/models/data2vec/modeling_data2vec_audio.py,sha256=0O5JvdqXhRahfRlTVcd9xq_MZbnBLdA_UYn5D4nBFkk,78799
transformers/models/data2vec/modeling_data2vec_text.py,sha256=2-VURfTO6W21tYqrK8AyyvIS60FR_GBnAI2ZeLdaet0,70394
transformers/models/data2vec/modeling_data2vec_vision.py,sha256=sX1_Q7mGlK_bS9ak42CIvCzJJUV10WwPZOYb4vMaReQ,60330
transformers/models/data2vec/modeling_tf_data2vec_vision.py,sha256=_axJdTtCFsStQYMNqul2vZzhMPW74OQejWzX5U6r_xg,73348
transformers/models/dbrx/__init__.py,sha256=n36C-BWFjJ9wkgBAv764sGksJFOL_fkME1fe1cTm-sg,1513
transformers/models/dbrx/configuration_dbrx.py,sha256=W1nWuFHixQ6ZP4xx3Q4v1cKYC4T8005a8w4iJVIkTgA,11177
transformers/models/dbrx/modeling_dbrx.py,sha256=Oszi_xvSrqXoyTVsy6YXgjzUD6OLdrGsTe5SDD-zOyY,62683
transformers/models/deberta/__init__.py,sha256=6ewAfJ8yBFyXvqBfBp19QDEs3fTgGwp0m-cUHZOVWd8,3391
transformers/models/deberta/configuration_deberta.py,sha256=wJ10THNTuuRWIkTf-kkO2GlBPUsnZoOKMPYAfmysjgM,8685
transformers/models/deberta/modeling_deberta.py,sha256=jO6uNpVMONtDO8BlYGvqBM43zrWg8NWdhgVG2s0dMtU,57929
transformers/models/deberta/modeling_tf_deberta.py,sha256=oU-C2VJrA3uo59YNNs7987jxTr1WGc77W3XiMgSjBLQ,69027
transformers/models/deberta/tokenization_deberta.py,sha256=9bOtmoaHBPSpIlHd8fw-fY4WPYtqv6aRa90wS1xTdQw,17051
transformers/models/deberta/tokenization_deberta_fast.py,sha256=_iy08DJFzQsrrET1RJKAp6lGl2KxZFpQ7BLE4mXmaGI,10721
transformers/models/deberta_v2/__init__.py,sha256=IsGFqPGb_7oEEDNCQsO_IBCTa2tJl1OjKWojytY-XHw,3669
transformers/models/deberta_v2/configuration_deberta_v2.py,sha256=FbyiBXXpwXjICMTU2MQC0nQxxPGkXAxrK5IORKIjNlo,8621
transformers/models/deberta_v2/modeling_deberta_v2.py,sha256=b01brhZxe2SraHATbHSZdRg1QVwq_Rt4rGk1t_f_yLw,67501
transformers/models/deberta_v2/modeling_tf_deberta_v2.py,sha256=FKeDoqi2OxCExYuEjM-lfGe37VTLARBCw1LUvOgI4wI,81404
transformers/models/deberta_v2/tokenization_deberta_v2.py,sha256=aqeowwNFhBcI9sKcCCaFeBFmNkZ7LTxqkYHHD1g8KBE,20702
transformers/models/deberta_v2/tokenization_deberta_v2_fast.py,sha256=qwgQDjV0k_3d40RgzlUiIDBqwRO1v_dbqwtcf2WiZ-M,9758
transformers/models/decision_transformer/__init__.py,sha256=9MNMqQI1zNwuXzhI8TXALNHhWUuY2YOdoOx72l3io8M,1861
transformers/models/decision_transformer/configuration_decision_transformer.py,sha256=lLxwvAYUIMm4Ql3sxL4RMlBFzbtd02kxAvGuqNOzP_8,6986
transformers/models/decision_transformer/modeling_decision_transformer.py,sha256=LVE1vz3D1D86k_haThUqVQWAU3Om-4_40eLTtTeOUjk,42929
transformers/models/deformable_detr/__init__.py,sha256=m4YrIA0yACKjAqP0j5ngC4JwCqzofZ8kP2CdwulB6zo,2387
transformers/models/deformable_detr/configuration_deformable_detr.py,sha256=yBHZe0RsUZgspWv71znrkwJK3nceBYzga7HzYzNLlN0,14534
transformers/models/deformable_detr/convert_deformable_detr_to_pytorch.py,sha256=ytsMFj4rqS-8fxDL9wojqFN-PscjWJ79rVcVQQk_1s8,9460
transformers/models/deformable_detr/feature_extraction_deformable_detr.py,sha256=GwYaT6B6-Fu2Jbl8CALodb7Lz4gr9jSRfq01QfLQc7Y,1546
transformers/models/deformable_detr/image_processing_deformable_detr.py,sha256=Gr3QdyYRIr6nHX7I9Ou18EiNXuuRwEtYMJcUkWqS0fg,73187
transformers/models/deformable_detr/load_custom.py,sha256=GvDeH883HST8-vH5Xl5jcR9VS_e0GSzbDoImSLug9rA,1559
transformers/models/deformable_detr/modeling_deformable_detr.py,sha256=p6zRNE53xb1CzzGCYc1w8CYIETxXOeU2Vsl6jlFZQRs,100586
transformers/models/deit/__init__.py,sha256=vru6_KBdFt9--qknQVxBMEccT9ICPaNXD9_YW38FvZA,3218
transformers/models/deit/configuration_deit.py,sha256=9LopM7dLKd7jiKVojcybRE5KdYXHxbAZbYaGmjHwr2g,5695
transformers/models/deit/convert_deit_timm_to_pytorch.py,sha256=7wWjhmCpS972rYukIRZsfhVfklvbdl8nclFqiRwb82Y,9216
transformers/models/deit/feature_extraction_deit.py,sha256=1j_aV0oAZUofSYJGCEFRo0WNd_zVEXjj3SFlTQSuV1E,1172
transformers/models/deit/image_processing_deit.py,sha256=8Fuu1W1HWlAnIGSsU64AKpiqAS-6G8lJLjYaSvdeyoU,15144
transformers/models/deit/modeling_deit.py,sha256=TG0I-gGEMe_zHY1Xk-dVXQLZaKiA4WyvKDGvji32faA,43213
transformers/models/deit/modeling_tf_deit.py,sha256=X_VeI4xWzh4UMCQWlZWfqhmdkPsxDJ1MPFsfQyOzfCc,51569
transformers/models/deprecated/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/bort/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/bort/convert_bort_original_gluonnlp_checkpoint_to_pytorch.py,sha256=9aY4nB-A4-WeVVH6CzE9kxoHr9uqL5f5NW2_OirzWxo,14067
transformers/models/deprecated/deta/__init__.py,sha256=sRdhN6pSfT1G8VY04s6jNnZBKgyZrB4DsrBsAPs8Rw8,2038
transformers/models/deprecated/deta/configuration_deta.py,sha256=GTfTPOaP2JzdNKQa9zg3CQ52QPuQOkoxU9VLc-uZt2s,13948
transformers/models/deprecated/deta/convert_deta_resnet_to_pytorch.py,sha256=Eu3xpsuFwp-vkyOPHhAjWYIlN1zvm9TirpGMTq5GzGw,16799
transformers/models/deprecated/deta/convert_deta_swin_to_pytorch.py,sha256=RWx7DMt5PMBoOhQM3poOILmIAJPHEWhH9mY7DeZTVDg,18997
transformers/models/deprecated/deta/image_processing_deta.py,sha256=3KSMJGPTYhQeZDFDo8q1jD39OWyUppgpmvXbnx7QcBo,54891
transformers/models/deprecated/deta/modeling_deta.py,sha256=6Rd6LUDmEB5N1T-KnIL6fuIj5f0MpqH8fuUjK47V3VI,135663
transformers/models/deprecated/efficientformer/__init__.py,sha256=u4KA4byDgoRkQ9uuNGgkA2PtpIh0BpZVTObA0Vgil-E,3188
transformers/models/deprecated/efficientformer/configuration_efficientformer.py,sha256=QFiBTmFQU6P8VllghZ5jQpR1Dthnm9uylTgf7z3uHMc,7719
transformers/models/deprecated/efficientformer/convert_efficientformer_original_pytorch_checkpoint_to_pytorch.py,sha256=1ni0wyhRjTbF8U4BZ_FXU-_9Jzy43HMLKI3vGlyPjFc,9381
transformers/models/deprecated/efficientformer/image_processing_efficientformer.py,sha256=a9tDStbg9089nTXxCU3pjWBG6HB6Jbw2snlQzVWdDuY,15698
transformers/models/deprecated/efficientformer/modeling_efficientformer.py,sha256=029SXmbp69FUn13m9OdoTHivIAe-x-RkFUtk62yYLWk,33580
transformers/models/deprecated/efficientformer/modeling_tf_efficientformer.py,sha256=tRs9Ljxf8bf1B-6MwpUPuUinzWik24kzQH50Ke58cjw,49194
transformers/models/deprecated/ernie_m/__init__.py,sha256=V6C21iE8AKYSpDNW4Ffn3IGiKp69T4ro2LFcRXc0mq4,2458
transformers/models/deprecated/ernie_m/configuration_ernie_m.py,sha256=bGRUXTL8NdJEevZNBmDBh_aB_RwRNL8G1rfdNYMW69s,5885
transformers/models/deprecated/ernie_m/modeling_ernie_m.py,sha256=flc9_1HuMrChGoHcx4au3IlEbt9VaWDYY29yaSmG3Dc,47028
transformers/models/deprecated/ernie_m/tokenization_ernie_m.py,sha256=oGKdPntR5sjU3XrxbaRNySX76bQaSLlFWNTgLJfmXBI,16169
transformers/models/deprecated/gptsan_japanese/__init__.py,sha256=8a1T_PBkN2MKzJDSTVJan5kSknwon3cRtUicoKVt2SY,2083
transformers/models/deprecated/gptsan_japanese/configuration_gptsan_japanese.py,sha256=T8buHMjH3XFnx7BXXis6M5aTvWLwwnleTf-YDyySwNM,7124
transformers/models/deprecated/gptsan_japanese/convert_gptsan_tf_checkpoint_to_pytorch.py,sha256=syF4TCbLQByZhm5VqIFgXfzQ4zImmCua8UNjCYJP5t8,9793
transformers/models/deprecated/gptsan_japanese/modeling_gptsan_japanese.py,sha256=JbBnTAclyfHQHkWyRMfEVxlmK8I5IemxcPidnSMRyc8,64953
transformers/models/deprecated/gptsan_japanese/tokenization_gptsan_japanese.py,sha256=zI356SLqne5ZLBkp1sZBjp8MCOP3VZ__zVVsl5iyDbU,22619
transformers/models/deprecated/graphormer/__init__.py,sha256=ltRElMWou0jRd50T50NHoJoSUbvM5IrcT41EcFQ7mV0,1682
transformers/models/deprecated/graphormer/algos_graphormer.pyx,sha256=b_Qlm1hKCHnAqx6oOLGC9LkivAV0K_AZRGgXT9MmBas,3635
transformers/models/deprecated/graphormer/collating_graphormer.py,sha256=KRew-2p9_7heLTflAYA6dObor_Hxy47yIP8HFEgaj1U,6087
transformers/models/deprecated/graphormer/configuration_graphormer.py,sha256=ZzNCBEZj_G1S1lg3MouwutiSeO9G47yFob14WGXXN9g,10380
transformers/models/deprecated/graphormer/modeling_graphormer.py,sha256=Y3aYbgX5vIYB7FfM8jRkv2xZRLdoHZuS5aBtesLXqX8,37006
transformers/models/deprecated/jukebox/__init__.py,sha256=96yLuu-yOBcAHaz1zhvc4RWwIvqkjycikMa-GXFcWm8,1889
transformers/models/deprecated/jukebox/configuration_jukebox.py,sha256=-gLq4uKdqdjCWuV9ZbChsUiFGEI0a58st5oapPTixGI,26749
transformers/models/deprecated/jukebox/convert_jukebox.py,sha256=RBgOPbwIMv_42mUFJYxRv4IAGZn4cAzjTqjrMI7HtVg,11789
transformers/models/deprecated/jukebox/modeling_jukebox.py,sha256=O0xBJi3UyMF8Aj0TyXYbN_2wtouHjy9pIdMbUUdkiZQ,119471
transformers/models/deprecated/jukebox/tokenization_jukebox.py,sha256=r1YcKG2OkPWAKdriQ2BXgX-MBsQHbeyccoc5aKLCpac,17352
transformers/models/deprecated/mctct/__init__.py,sha256=aaM-CVsMyEUWqGHH5xAgnqUu6B5D730X_lTo7CMpo7o,1732
transformers/models/deprecated/mctct/configuration_mctct.py,sha256=OmrxkatPuycQORmuIQWznAHsi20nF9CM-HHtHWyh1gM,9073
transformers/models/deprecated/mctct/feature_extraction_mctct.py,sha256=JsaSE20NeqBX8Uw-07Y5HdUcQtbYZqCrTN18Wu2B4rI,13460
transformers/models/deprecated/mctct/modeling_mctct.py,sha256=YiUE1VOTKMH6oGk9vlqKf4Q8YxkQFJBQud9bJXgF6ug,32874
transformers/models/deprecated/mctct/processing_mctct.py,sha256=EkokdjeJPgzsSxriPNmAthZ6WgO_iQyFMpQKXDeS7Uo,5931
transformers/models/deprecated/mega/__init__.py,sha256=i_9dHqDl6RZJ1zebhj8pn3zPlgxRqEynwsM_mj9eWMs,1973
transformers/models/deprecated/mega/configuration_mega.py,sha256=0m3Fsv9KqcZECi7Dbgjdz7nidKqf8MQbdfMsYMlMF_4,12588
transformers/models/deprecated/mega/convert_mega_original_pytorch_checkpoint_to_pytorch.py,sha256=RqYrXvQNCa-mSlF9L0ayNvdrdaAayIsEIXpJ_j8c7FE,13155
transformers/models/deprecated/mega/modeling_mega.py,sha256=-fLQbigFtoljvUS0VisbT_Y7_q9qiyYEkXsM-mdFQbc,109519
transformers/models/deprecated/mmbt/__init__.py,sha256=0CCmesCwGIMNFlf2oDsL0gYaCSpsfAC1_bMOXRcAgF4,1480
transformers/models/deprecated/mmbt/configuration_mmbt.py,sha256=mVkSYHpXNnKbvGiJ_0MOF8V_lqwu0l4rdhwIDTWFu7o,1597
transformers/models/deprecated/mmbt/modeling_mmbt.py,sha256=ms_fa8G6Ww3kyk7jqLeAdba6k2E6VMBq82zMz5GvFKQ,18913
transformers/models/deprecated/nat/__init__.py,sha256=1KgeUYAs8Ypq1rZgA1tS_cq0GNjTINvjycdQR-m0P7s,1613
transformers/models/deprecated/nat/configuration_nat.py,sha256=o-nifDP9IvftvMzVeoXGGUycwUJ-wox1K6QVd4kpaik,6975
transformers/models/deprecated/nat/modeling_nat.py,sha256=G2ggfJ_RJYBIIAbjvJu2DaAdgEzf74chqVvaNenvcSQ,39728
transformers/models/deprecated/nezha/__init__.py,sha256=p4YuR6FmvGSeCniAaJaTWtL_9kqzMfnGeKAYskaWyeM,2062
transformers/models/deprecated/nezha/configuration_nezha.py,sha256=hfpG7tYqEHfddMFZ4Ni6h0DRAuG6UwNEsmlxmK562ew,4817
transformers/models/deprecated/nezha/modeling_nezha.py,sha256=37Yuhy26qkonrj7djUw9tgJTaQp5yF1fVx8ctByXSo4,73924
transformers/models/deprecated/open_llama/__init__.py,sha256=KJ11JLm0-ytx2jZjEVggJMC-BxjklPJqLVF2Fm1Okjw,2702
transformers/models/deprecated/open_llama/configuration_open_llama.py,sha256=5O8r3FXbzYE4gHGqnhYj7LtSYVujAXKR4ZKkAsTGKLM,7771
transformers/models/deprecated/open_llama/modeling_open_llama.py,sha256=ULLDJbRArLXssjlf_NZO4dtSKClF87u4fm082KpRW0U,43380
transformers/models/deprecated/qdqbert/__init__.py,sha256=xDttpfygkbkttNuKo3pW6e-Z0_MwTbj5uICeiXWgppw,2223
transformers/models/deprecated/qdqbert/configuration_qdqbert.py,sha256=qYx_V85qk4g_o3L-OxiLObqndD834k0j0bDjQUNcfT8,5689
transformers/models/deprecated/qdqbert/modeling_qdqbert.py,sha256=XjGtF8iB8I6lfvL0PQK6cVWRbY2-76Lp_RLi8F-y7Z8,77002
transformers/models/deprecated/realm/__init__.py,sha256=_xkblqSgmTTryPK_c0N_ugcMVYc871UxI2hOskvo_pw,2504
transformers/models/deprecated/realm/configuration_realm.py,sha256=kUxwVQ0A99hr2wEFALWfvgoDJKp0OpxGjls42Q-yVZU,7557
transformers/models/deprecated/realm/modeling_realm.py,sha256=NtHppLE8iK6rMwGjqeFRRMmGFLkm-Kc-54h1ijrvuGk,83476
transformers/models/deprecated/realm/retrieval_realm.py,sha256=cebNTe43Mb5VN1xUzR13ewbvkGnlZ5nlJjGSj0ewoWc,6372
transformers/models/deprecated/realm/tokenization_realm.py,sha256=sHOR4tnLFrZaiZXhXhqnibiZAaAQvtpwL18bJvWrj-c,23114
transformers/models/deprecated/realm/tokenization_realm_fast.py,sha256=eZ76_VPhjsqdEEHRr0_4Yt3HK25y0AfOlDohgPf7790,10953
transformers/models/deprecated/retribert/__init__.py,sha256=I5aMwp2FJw4zoL69qlsk2krQjpJMYIh436X47YxUMu8,2163
transformers/models/deprecated/retribert/configuration_retribert.py,sha256=7liSa4MonQVeLEz2VlxuapZglk6Z_CzyG5i8Nxi2MTM,5200
transformers/models/deprecated/retribert/modeling_retribert.py,sha256=tF4Sd2lY3_h0-DNqWQoiFXKxxbjfTqqsxPtJnKLEnd0,9297
transformers/models/deprecated/retribert/tokenization_retribert.py,sha256=NnQiqNw0brmkZojd8gc6uudCYSMjsDaiTni-PUXhsd8,20650
transformers/models/deprecated/retribert/tokenization_retribert_fast.py,sha256=hIkbxCjKbkfblfYAyEE6VOf-l7aEmgXO3myRQ917gho,7820
transformers/models/deprecated/speech_to_text_2/__init__.py,sha256=FrO5Wtn6Uznx5DzVHyfLpdo7iDkeBpFgXC4bHXXgxxo,1951
transformers/models/deprecated/speech_to_text_2/configuration_speech_to_text_2.py,sha256=HpJVunbFUp23kPZBCr9K5sIXKVp238icRyv8_YGcmCI,6001
transformers/models/deprecated/speech_to_text_2/modeling_speech_to_text_2.py,sha256=asJ2UlO6N-HsLjO1eg6rFCWtf2UbH-5NgB7lsPGB0u4,43880
transformers/models/deprecated/speech_to_text_2/processing_speech_to_text_2.py,sha256=7AWU3_OegyHwNxluEMSHjLzBGfYcg3m-TNHq9VHYJTo,4792
transformers/models/deprecated/speech_to_text_2/tokenization_speech_to_text_2.py,sha256=S7biDmProh43S6iAbA0cIJyCVqb6fG6itYkbsI2Ccfc,8405
transformers/models/deprecated/tapex/__init__.py,sha256=lQutKYtwbU8ztPva0tyRnnV-zOWw6rxkGyoOUSuvnUo,926
transformers/models/deprecated/tapex/tokenization_tapex.py,sha256=MPuB1JknrO9WY_j-Hgy8JWGNKvcowBDrjhFi-bCGALw,64347
transformers/models/deprecated/trajectory_transformer/__init__.py,sha256=XnXDCm4ePannQqnQnMn1Fpqvmq9-1L0_mTeoqObM8-0,1806
transformers/models/deprecated/trajectory_transformer/configuration_trajectory_transformer.py,sha256=qH3gf0InhrlutKUQNA4-OrqWp72n_Ha4B6jA_kZy55U,7061
transformers/models/deprecated/trajectory_transformer/convert_trajectory_transformer_original_pytorch_checkpoint_to_pytorch.py,sha256=sUPWNSvy46IYr8eFEyxqLraW90abuzy4Snrt3uKFW34,3138
transformers/models/deprecated/trajectory_transformer/modeling_trajectory_transformer.py,sha256=ts1LBqIjnC9gB53or9STcRJTAE7UaSdZnYHoh4jdtq4,25593
transformers/models/deprecated/transfo_xl/__init__.py,sha256=5IURzrZTTTFlLaPUn2R1ErF8SvQ9nF9QcNM8ENdntyg,2879
transformers/models/deprecated/transfo_xl/configuration_transfo_xl.py,sha256=U3zrDVAkNbmSdcyLRnGVvHEo6BCZMONZKYHhrKIMGi0,7874
transformers/models/deprecated/transfo_xl/convert_transfo_xl_original_tf_checkpoint_to_pytorch.py,sha256=pGQ5iNKdCDjPilFBI_dwOmBl6Ha4pOkt7Guq4mSShBo,4937
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl.py,sha256=ZTxGgzubBKUieotTK-Z71Tvt7KKO9S-CF5pmOtAWl_U,45905
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl_utilities.py,sha256=Dlv3ZzRduWFBnZZHn8RegbW45XeCecuYCzzzZC3bDXs,7633
transformers/models/deprecated/transfo_xl/modeling_transfo_xl.py,sha256=tqUEFzTSvwGe55FfIeQ3X5dJovQUyG03XhuA5PWArlw,55892
transformers/models/deprecated/transfo_xl/modeling_transfo_xl_utilities.py,sha256=L1l4K7sj8rwXzvhn7_-RK2UbOnYtfDUF0VdFr4L8nxA,10859
transformers/models/deprecated/transfo_xl/tokenization_transfo_xl.py,sha256=dXRuCZcqUdjc6cSFAvGa0bMZcGI7QVH7ulIWOlOnxwM,31947
transformers/models/deprecated/tvlt/__init__.py,sha256=Ryp_kcJdg3sqjFKgyRVZjXAMUp_Epg9CL2GLQAHD0k0,2520
transformers/models/deprecated/tvlt/configuration_tvlt.py,sha256=uGh6Ie-Nu-uf5987LLtFBvpEqd8rLEjFzzkMKIol6b4,8623
transformers/models/deprecated/tvlt/feature_extraction_tvlt.py,sha256=Mx7tuGJvK-1YnS7ggYL6j_emzolu8L8Hrce5ATPtPR0,10558
transformers/models/deprecated/tvlt/image_processing_tvlt.py,sha256=1r9oEs7iP1gMAxLekBzXZq6CNDJyf5J65UiaEOw5_kQ,20090
transformers/models/deprecated/tvlt/modeling_tvlt.py,sha256=hQ0PyxS8oOjiokuLtMAxY1jNSL9WoKmH7k6RBrm6scs,56698
transformers/models/deprecated/tvlt/processing_tvlt.py,sha256=pC3zQjapxdhkqrl1QdJ7mXkEOSGNooP7kTUEWKUr_nE,3507
transformers/models/deprecated/van/__init__.py,sha256=UGKlepGOpOuVmsb6Mmess6kPa2qP4rLzfzJ0dsdQDno,1564
transformers/models/deprecated/van/configuration_van.py,sha256=QpN-p2Hg0C59if2JSEG47j_zepx1f4KpCgIBYgLhCOY,4657
transformers/models/deprecated/van/convert_van_to_pytorch.py,sha256=UMaipjtB68OPj6OQzoSyquPlBkG-IbN4q1FZvs51Lxg,10373
transformers/models/deprecated/van/modeling_van.py,sha256=YwNP7YVzKpyURSQ3taijiOBDTlF086hEcT2fEGqQaTE,21130
transformers/models/deprecated/vit_hybrid/__init__.py,sha256=Ld0UOl3F4y-YaI42jk7ym_wvTFswFGJj_M6jpSeXU_E,2125
transformers/models/deprecated/vit_hybrid/configuration_vit_hybrid.py,sha256=VwJpgMa1l9rL-Rx9jF-POC_mz69y88tnHqgS431Qa10,8230
transformers/models/deprecated/vit_hybrid/convert_vit_hybrid_timm_to_pytorch.py,sha256=NT-72ZHHEaQbmmsHgh9_UI7gf_40Aykff8cW8XXspQQ,13412
transformers/models/deprecated/vit_hybrid/image_processing_vit_hybrid.py,sha256=hI1c4mMpNupokvrPyLXFtDEKpknU8MR0WdnUPpG0dmc,16219
transformers/models/deprecated/vit_hybrid/modeling_vit_hybrid.py,sha256=22N1IODYpHHj1cNaqZ-aCukh9bViJHt3bMNo2qJQ9VQ,32563
transformers/models/deprecated/xlm_prophetnet/__init__.py,sha256=OYkcL5jhbHEaLLvOAd0v-CppyQxbuRzNEzaSDBhXxKA,2408
transformers/models/deprecated/xlm_prophetnet/configuration_xlm_prophetnet.py,sha256=dfa6w3RvOEZLxS9GAdQOKaJF1vT6Gc-w06a8Eujg5Sk,8916
transformers/models/deprecated/xlm_prophetnet/modeling_xlm_prophetnet.py,sha256=EX5mzNPUYfWqcN5B-YquCXvM_SE7TfZcbtzslv1WPBI,115593
transformers/models/deprecated/xlm_prophetnet/tokenization_xlm_prophetnet.py,sha256=HpD7sKmAaelMUUDwNrAyHgBLhiWI2rnDaxn-yFFjdEY,13272
transformers/models/depth_anything/__init__.py,sha256=R2x2xhnw4yvOwjciXSpDKWmz9fBoGsb7oh3Wk5hvQYw,1644
transformers/models/depth_anything/configuration_depth_anything.py,sha256=u9BhRwLm3LZDR2xc9tHHOaNSq0hxZ0nCluSc6gyXYk4,7938
transformers/models/depth_anything/convert_depth_anything_to_hf.py,sha256=re3j1J_GBywWeFv0eMRe9FvNSWnNSb6-pJf852fZZYg,17818
transformers/models/depth_anything/modeling_depth_anything.py,sha256=SydF69zGDEDWRDbNLv1Dz_Rt7d4U_DTOvYoOq_YuxvY,18561
transformers/models/detr/__init__.py,sha256=wI3ikGDgqwYD5uSxF0sKPaATQjW9g_gyauIMRz7FlAg,2422
transformers/models/detr/configuration_detr.py,sha256=G0fL0UmbYPDum5mDWZEZIc-tXZ244MzekqOYjEfKS58,13486
transformers/models/detr/convert_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=k1F8poVxIjsqQUUJu7CJl2Bj3x1IuivlgTi54Y_miKk,13560
transformers/models/detr/convert_detr_to_pytorch.py,sha256=1Y_Tz2WSdybHscDL-iuF5sJG2Ni2o2-85unaJLtaykw,18992
transformers/models/detr/feature_extraction_detr.py,sha256=gMyG16pNJKoimImXOyqi589hGj37OYGWb7ZoTx84d5I,1474
transformers/models/detr/image_processing_detr.py,sha256=c_aoZY6ni7Kbe4_J6b2XSCMVcMm0WtWBdUZ30KpHHcA,93999
transformers/models/detr/image_processing_detr_fast.py,sha256=AjKDeu5jwk8Ctp3BPpoBeLvgx-QGOHE5DKCtAvcr42E,74404
transformers/models/detr/modeling_detr.py,sha256=2Y6fCJjvHQDYPgOT39L4HHYNB2JiVg0-iNaeqwcOg3E,88173
transformers/models/dialogpt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dialogpt/convert_dialogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=Zp59TmLBKEs-x1-quZZeqARhpS3cTnnmgT4nCI0zsHY,1537
transformers/models/dinat/__init__.py,sha256=wgLyGigRXX4o4Wh3zpZLGv0NnurzO4G4t2scbEWAvUE,1640
transformers/models/dinat/configuration_dinat.py,sha256=n4DS9D5u7yK_VsvwNm3opDn6tl4iPoiVELMZrsP5n9Q,7328
transformers/models/dinat/modeling_dinat.py,sha256=Xy0F64jRBN45WyHBhmy4nBwEo9YS3V54FR3geJ-DDOc,40324
transformers/models/dinov2/__init__.py,sha256=bNMjmmjz3UwsFKzAlaGg2Ykkd8PL5bFLnIK02vFOhlA,2365
transformers/models/dinov2/configuration_dinov2.py,sha256=9vrpyOD3dppEy5UhdLe5PP9ISMox8sIPffxkVo-1B2I,8041
transformers/models/dinov2/convert_dinov2_to_hf.py,sha256=UpXqLbLJ8sF9LUFJ2TgU_5DIZe6ViZmIXdWEmDhVUL0,11863
transformers/models/dinov2/modeling_dinov2.py,sha256=iQuMTvki0to8RAvJ5OuBRgCfBOotjeXfX0Z-kDP8CkE,38684
transformers/models/dinov2/modeling_flax_dinov2.py,sha256=PwIKpFL8Eu6Jjewtm9HV4AsvdYh-YRaoSnep9iMC-G0,30688
transformers/models/distilbert/__init__.py,sha256=ZXSxjAeG8GvUcX7j-euPmZ2YJFqWCdeaub0oa2v1eUE,4847
transformers/models/distilbert/configuration_distilbert.py,sha256=KuM3O1gMdZMxfgWvKWANMpzuXW4gIu7EuJFzQXT_j0s,5989
transformers/models/distilbert/modeling_distilbert.py,sha256=V2QMBk5mE7QCge9lfM3MRUA5F_a6yb6VR39qt5kXjSg,60202
transformers/models/distilbert/modeling_flax_distilbert.py,sha256=fYKrPGoXQa5k7KRQYqQ8Bnop_5dupCiRaAQLAacdKko,32629
transformers/models/distilbert/modeling_tf_distilbert.py,sha256=CLK_eoeq4qJc9q3H2fzSF_Wut9SUQ4MJLC48T1zt8P8,48845
transformers/models/distilbert/tokenization_distilbert.py,sha256=erwZxWDwWfVzM3K8b66z4xwNoptSRmOMhDZ0pvJc_UA,22224
transformers/models/distilbert/tokenization_distilbert_fast.py,sha256=oO1CanamHXMuG6eJH8LOwRBEgK2yCSe80lF2fDGz5zo,8037
transformers/models/dit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dit/convert_dit_unilm_to_pytorch.py,sha256=GEMnwc1iMEobjwXLOxLJZarjSqvABOIkUAfgSp9Gnyw,9419
transformers/models/donut/__init__.py,sha256=KB3xtqV9O8j-xyrwIR2ujn9fDMKUVtjVAAqsZyzUEvs,2263
transformers/models/donut/configuration_donut_swin.py,sha256=rjGgevf-z_sZ7BrVI7CD0-8I0h3ZixjxBfsigmlwj_M,5753
transformers/models/donut/convert_donut_to_pytorch.py,sha256=lIIm3j7oyJYa2xy-piZ3bvUEHOSP9mQ0x_MZhqPiBOY,9337
transformers/models/donut/feature_extraction_donut.py,sha256=jBSpDfoiCg_IWr4gcphIcxs7DA760JnH6V6hAfaoYPM,1179
transformers/models/donut/image_processing_donut.py,sha256=VX0Wvm6ktN4i-l9AzrOp0RMWxIYlW-8RTq6oDyUrp2k,21759
transformers/models/donut/modeling_donut_swin.py,sha256=hkysb26GWELAVgpOgokMLfAjGv3t8SchTuCZ0vy0eSs,45837
transformers/models/donut/processing_donut.py,sha256=EGvhMkU0X1ZsYcJnyzkJOLJbSRbCx8YN3Nf9JI3v3TY,8798
transformers/models/dpr/__init__.py,sha256=8dPmc9s-P5LDxICyD2E8jWZdQgRBKpaxxCsvIhKIec8,3733
transformers/models/dpr/configuration_dpr.py,sha256=dY4rToJ-PPTeDESOfwkYaNZvcgfgYTz35oPRcfUDShs,6390
transformers/models/dpr/convert_dpr_original_checkpoint_to_pytorch.py,sha256=XsxG5FBg46-EHlDsMq4w21C9W4wl8RZ6GZvx5coBmfk,6132
transformers/models/dpr/modeling_dpr.py,sha256=Ms-XpQBlIT4-Ma5vOtgednsL9kKCJ8F18t-iiI8xnAA,28334
transformers/models/dpr/modeling_tf_dpr.py,sha256=Gf2Naxx1LocjSvhaLxSkORR17RbQGbZ7JveuYhVEECc,33635
transformers/models/dpr/tokenization_dpr.py,sha256=d4zRo3bnaI5Gfpao-SJ7RemQPQHAEE3nJt9CDwUHaac,15725
transformers/models/dpr/tokenization_dpr_fast.py,sha256=wswsSm3NV2dmbQOdLnpkkVEbm-EuIVm6nUHZyIvAnuU,16111
transformers/models/dpt/__init__.py,sha256=AFD_wxuPhcC9KLJa9e6lDAR8l-iKhwOcU1bA0QIl_9s,2280
transformers/models/dpt/configuration_dpt.py,sha256=R553pt-IRpm21vtt4eaj0GTirzG3Bcc5CZsIZjVH_TU,14042
transformers/models/dpt/convert_dinov2_depth_to_hf.py,sha256=RSRtp1_C6NG_jX4V-9esAMhKXXKwy5eIhMTS7YehCaY,16926
transformers/models/dpt/convert_dpt_beit_to_hf.py,sha256=Xs1RAcAQitVKUyjyEYTxYgbY2udQyyFbnOtnDhEN64k,14346
transformers/models/dpt/convert_dpt_hybrid_to_pytorch.py,sha256=SLxsxRpr5dZXe-Qzv6xsmXBZIg8IyhToooqjS5ptjBY,12995
transformers/models/dpt/convert_dpt_swinv2_to_hf.py,sha256=LH0aOWueCHqVl8Huz_MLdoQqso5C8Tgh7_ArUxxvGYg,15175
transformers/models/dpt/convert_dpt_to_pytorch.py,sha256=ezSKskhntUX7V-Y396PA7a5XhmdYfJDQPechPiy_Lr4,11877
transformers/models/dpt/feature_extraction_dpt.py,sha256=ZgBcSKNDX0_Fstv94sp1r9jpr9zvXCLPwvIek76Fkso,1165
transformers/models/dpt/image_processing_dpt.py,sha256=Hg1yyAeBaPIdTeGMgJfi1XBlOicNoi--29c5ZSaDE1M,24383
transformers/models/dpt/modeling_dpt.py,sha256=YlC1LBSkVGUQy9Bmh_tD-ASrBNV8jAEIhT97vTQkApA,57207
transformers/models/efficientnet/__init__.py,sha256=ZE1lrYitMe2LaWqpWSWF7l87JzMl5Y2brMoh8U3WFWU,2454
transformers/models/efficientnet/configuration_efficientnet.py,sha256=srZrPZPIa8h2ALPjhZ-nOlDFkL4aSONuxX3WCgdvoZk,7596
transformers/models/efficientnet/convert_efficientnet_to_pytorch.py,sha256=e2Na1xvNc7z9XvvI7v6v1V2uFWr88MSTN3JPKR5GstM,12756
transformers/models/efficientnet/image_processing_efficientnet.py,sha256=Oxr3CxZ778bZGpGhBn4IFz3-bfS2mH3RAuvHu8zgghc,18299
transformers/models/efficientnet/modeling_efficientnet.py,sha256=85JtUWn3jHhiXuElYcB0Z56AybLNS9WDU6IDur27m5M,23946
transformers/models/electra/__init__.py,sha256=oGxwD4U-g4EcdBcHfXM5rPU0kqFEgZ7ZfSTtcQ20iYE,4971
transformers/models/electra/configuration_electra.py,sha256=1YBlbWbul0b043xyZmAjErD2HVi6RllrZxxgkwmvlbE,9094
transformers/models/electra/convert_electra_original_tf_checkpoint_to_pytorch.py,sha256=8G7yTj4TTecFVf1OPFeqFuPSuyzronf3gSeiQQeEMG4,2861
transformers/models/electra/modeling_electra.py,sha256=JU45jdncy4IotSc0eRY4rje9cnaxp-cpT8OIT25ZE3g,74821
transformers/models/electra/modeling_flax_electra.py,sha256=S5TkUbjF-9GNOxeiGfXTjc3tnINV18R8CLLFf30A9zU,62268
transformers/models/electra/modeling_tf_electra.py,sha256=XEePvRRTHB1N2BfahChTAjdbtyJ84W3SYxsy8CPmAq4,78333
transformers/models/electra/tokenization_electra.py,sha256=r9B7f9ELO0tu7-_eojvgKmJZRCCvbDqJaItdjE9FU3c,21227
transformers/models/electra/tokenization_electra_fast.py,sha256=zPqzst_6dX5eiFgR2iVsZuzYIS-KTe8BKDkh3fsPTQo,7685
transformers/models/encodec/__init__.py,sha256=ZL-u3DPRBfztDjGggKvhnUSZiLc16s-WJHLvGlaKlwE,1699
transformers/models/encodec/configuration_encodec.py,sha256=awVFWJyNfS4nRnrdtkkiw3l2kF_UDTZefrfwcBwC6XQ,8495
transformers/models/encodec/convert_encodec_checkpoint_to_pytorch.py,sha256=7IthG56LeBwH_WFzT5Cvwcfsgy1vIheurQHWpnMS318,15253
transformers/models/encodec/feature_extraction_encodec.py,sha256=luYd1uGvvQC_mDYlUsnMtSBn_S0dhbazYJ9zYGuQ1Kc,9873
transformers/models/encodec/modeling_encodec.py,sha256=ojztjYORfxjek_8JVM4FNXWFfXJ0QjLS7lQCHBb1KoM,33774
transformers/models/encoder_decoder/__init__.py,sha256=bR1yPbuqKHUYXaxI_QuDz6ccBSWpCr0THhPBM3lnttA,2451
transformers/models/encoder_decoder/configuration_encoder_decoder.py,sha256=8TqDqvbLjWw7kGt9kQu7Tznn_bUL2yB_2lb0GCxw4vs,4512
transformers/models/encoder_decoder/modeling_encoder_decoder.py,sha256=fiySfW6xOOV4Xb5iYmGA6-nbJ-7RlIRmRxBL3TU1s4k,35473
transformers/models/encoder_decoder/modeling_flax_encoder_decoder.py,sha256=MDrdjzKTc3rNKfI0Yed7Lo5tgGiFnfmMK74IgI9cEr8,43527
transformers/models/encoder_decoder/modeling_tf_encoder_decoder.py,sha256=y8wz5f_5cl-t8bbE0hF7vBkNof47FuvqwrOO-sAM4Jk,34306
transformers/models/ernie/__init__.py,sha256=hBeUclYWth7qZpSPBK5cXngWMYnRz2ptK0kL4Zn2NNY,2159
transformers/models/ernie/configuration_ernie.py,sha256=kOlh5l_oR5zz5x5lrNdIt5Ofg3CQm1xMlDdOZnUxA5c,7647
transformers/models/ernie/modeling_ernie.py,sha256=bdWDaixYTQnDuzbur-lLeS3HzvHIhPlgIvuoj322Su8,83048
transformers/models/esm/__init__.py,sha256=EVmrmVPfknDF1TKnqInKZt1dPJUJaRTf9RZRwW3bfZk,2716
transformers/models/esm/configuration_esm.py,sha256=BBItEALaPj2mm3XfsMgouwwuoAihq_uE1jeZ08p_o7k,14359
transformers/models/esm/convert_esm.py,sha256=x7Bz5lkBXMQJ_vYZ0-L3_z61lcTRQ9clIyC5WEkX2Kg,18469
transformers/models/esm/modeling_esm.py,sha256=A-N4TUCgSAIWT2LMxhG4Bm9_7nHPOmYcZYLxiJrGjB0,55569
transformers/models/esm/modeling_esmfold.py,sha256=Pr7VdkUSJkUpz8l5zcXpektsKw4iCVRaq5BLUZ2eNHU,86908
transformers/models/esm/modeling_tf_esm.py,sha256=QVBzxTq3Vc6-5d7Aa44zCExSmCaWcJRhiHMA54D4BnI,68965
transformers/models/esm/tokenization_esm.py,sha256=HD6JXwd33oSwMAvusgaAKpb-kFPbI0pPXz3TlLySB3s,5356
transformers/models/esm/openfold_utils/__init__.py,sha256=Xy2uqvFsLC8Ax-OOce5PgoBDiZgEJgJPqs__p5SBWUY,446
transformers/models/esm/openfold_utils/chunk_utils.py,sha256=co29vXYCaTh3g6PPSsvb_5GyePXVudMkISVHkARDT38,14390
transformers/models/esm/openfold_utils/data_transforms.py,sha256=F4wGANRhKLd6MLHrwg2IxpqCxCJEx8aFSxqAdsXsBMo,3764
transformers/models/esm/openfold_utils/feats.py,sha256=RHH65TclSlcI-fuGP16f6xr_QolV0aGRXEWUq-0boIU,8368
transformers/models/esm/openfold_utils/loss.py,sha256=wY2ONqbuRvWMomjkpfPwfoa7dqCO2vFkM-kmNfhjivo,3705
transformers/models/esm/openfold_utils/protein.py,sha256=R7diEvvIOtJY28B-_6TSMZdWmLFY4NOwaMzQmAg0x_w,11491
transformers/models/esm/openfold_utils/residue_constants.py,sha256=FtPlWVweacknPfmi4XCrR66kFr4EuYXywvx0IEY8KAs,37992
transformers/models/esm/openfold_utils/rigid_utils.py,sha256=J-xQV4KrkBNwHR4TSHBwT85pOYKf-nJ78Os4JtiJbxE,41130
transformers/models/esm/openfold_utils/tensor_utils.py,sha256=cySnhhaYbdq4SqyWyAF3qGeUWPfWKsuTYWRnX-h21sE,4781
transformers/models/falcon/__init__.py,sha256=-fQlTGZokpudg-zyBQzFtOEcHfHSuBe_2lR8umLnouU,1891
transformers/models/falcon/configuration_falcon.py,sha256=Ou1_dmNo-nrANOUs5BgViTKs6Q1aeM6G80ziee7msRs,10888
transformers/models/falcon/convert_custom_code_checkpoint.py,sha256=XPJ1owRjRno_Y1AD5UeoPE4oo6a-SeQR9w9u-EIUktE,3061
transformers/models/falcon/modeling_falcon.py,sha256=LW_ogh9X5sk9SNm4k6iaUsJ3zxFxpg_TN_L5FF_hkVo,77453
transformers/models/falcon_mamba/__init__.py,sha256=T9-sztUOVguT4mAKfXZjrTfckw1D0eracJzq2uWiDw0,1666
transformers/models/falcon_mamba/configuration_falcon_mamba.py,sha256=z5uGx6r26fDuIwPJUuZLIabg8Dbxr2b5-Xg4dQ2GDco,7728
transformers/models/falcon_mamba/modeling_falcon_mamba.py,sha256=pHg1skt1wy_0EfnwE6BmWu6lC7Sm6YjQMTuVUEil9lo,40557
transformers/models/fastspeech2_conformer/__init__.py,sha256=JCdE6cdG8sTDdpGXf74zZiLRa62mzZtlwy5xAwOFqzA,2228
transformers/models/fastspeech2_conformer/configuration_fastspeech2_conformer.py,sha256=org-i9kez6BwbLLuLRUUZLtn0I4rdvP7ljQxK_IhGeU,24344
transformers/models/fastspeech2_conformer/convert_fastspeech2_conformer_original_pytorch_checkpoint_to_pytorch.py,sha256=-ToJHpwI-xoLLMzLYdqFrBL6j6nsSPlNbkQ3pfTgJ6Y,8939
transformers/models/fastspeech2_conformer/convert_hifigan.py,sha256=RC1PaVnl1cLx8c2LdYycNti7iYRhUM7_KrX2mF5WyCM,5431
transformers/models/fastspeech2_conformer/convert_model_with_hifigan.py,sha256=wT4pQGgEHVFoWI1Lb71L7_i6ujfNrSMDGYuDGb4oeh8,3471
transformers/models/fastspeech2_conformer/modeling_fastspeech2_conformer.py,sha256=n9iDNFkU14uBo_7TDuJb2xv1en-33VvmM3a5QLlamtE,77857
transformers/models/fastspeech2_conformer/tokenization_fastspeech2_conformer.py,sha256=mpruo8WwH8Y6fDseUyVFut6EhOMNU7hcHEQt3DjM4Ts,6219
transformers/models/flaubert/__init__.py,sha256=x8RYQ-yMENmOOnKrjRqkfy4wvg5ouoM0-wHev1l6fs4,3196
transformers/models/flaubert/configuration_flaubert.py,sha256=GRxy8bDdliFToCO_4DLylbWdg-k_IwzjiDFb8MuAHuc,11188
transformers/models/flaubert/modeling_flaubert.py,sha256=LPzJQEYQcS1RLknpjP99_FG32RGJThvEE37hzqxe0q0,57602
transformers/models/flaubert/modeling_tf_flaubert.py,sha256=qsVYT7DRjUHPNEra75BHhfDU7odwDazmUblicyFt8WQ,57079
transformers/models/flaubert/tokenization_flaubert.py,sha256=7VRBuxhpy6D-nSfb1gpstsebanBp97lsc7zd7QBmIx8,22174
transformers/models/flava/__init__.py,sha256=o7tPGlidhlsAcX2A8qNW1cg434KxyHDgxNkRZXz9Ho0,2842
transformers/models/flava/configuration_flava.py,sha256=zLpaJb1iK_Doqe6JkjsSFIc6PEua_CgWEKRuqyMUf1c,37152
transformers/models/flava/convert_dalle_to_flava_codebook.py,sha256=iEJM9W_cKk3HK0gKS6i2ygEMeyymWCMl18LDaQXRAhY,3428
transformers/models/flava/convert_flava_original_pytorch_to_hf.py,sha256=LilQpbe6qeN2P_uXljae6zEPx_KoepoRv4uvCEAo0QA,4372
transformers/models/flava/feature_extraction_flava.py,sha256=mA1uAn29yv9PV7gYXauz0VTAJDgcpl9DPHvH99Ed__s,1201
transformers/models/flava/image_processing_flava.py,sha256=hJxSECaDM4RJGand3vOdHp7m4KQNtcRsjPEg2NoIbHM,37401
transformers/models/flava/modeling_flava.py,sha256=RmekFxy3sxGZD2exsNZx6M3-XQWoA4ZBv96sAPIxr1I,96512
transformers/models/flava/processing_flava.py,sha256=fj9uFlMerVGFnB9hV1XJ61c3q82qstjPwmWUdMiL46U,6832
transformers/models/fnet/__init__.py,sha256=Id1nhNi9gK--czTIMMMfP-Ex_cotBwChJrEN3O3Y57E,3011
transformers/models/fnet/configuration_fnet.py,sha256=vGw3_fqJyOObEAMIscbpGEZAkXQMxXMduMgoV4uLIRM,5540
transformers/models/fnet/convert_fnet_original_flax_checkpoint_to_pytorch.py,sha256=s2hJZxxZnljY-aQE5S5CT6tL2yYbwH28KxCMKrzzHMY,6911
transformers/models/fnet/modeling_fnet.py,sha256=Do8wLT5nPajb9y996Pr0ljMRJ84y7HM7AnG64IbFmRQ,49263
transformers/models/fnet/tokenization_fnet.py,sha256=P-4pz_Sbk-ZNOS6Pm9j8F_nwmX5O6yL6LC6LwR5oydY,14548
transformers/models/fnet/tokenization_fnet_fast.py,sha256=CPT2yPJLBf2ropYgcC1HCOyK5EyPnHpAlgkqISeT9T4,8062
transformers/models/focalnet/__init__.py,sha256=iq_lDemgRSttskuPWZ8ynH7hYj0RAsVsgW1oEqrpSKg,1805
transformers/models/focalnet/configuration_focalnet.py,sha256=xEsO6ZaSfSoc9ABEuwwIPbvTl1kb4UjUNcMvY_54Jxs,8026
transformers/models/focalnet/convert_focalnet_to_hf_format.py,sha256=xBoop7K4unfPawCbmlv7BTQHpbJkaUWasrwsw8dW_KI,9450
transformers/models/focalnet/modeling_focalnet.py,sha256=ViZOenMEuyg3oazS9BZJILvulGAb9VSKejFBxvPClpE,43126
transformers/models/fsmt/__init__.py,sha256=pgbhoHULNweTt72ejl57_M5p5dPFn-Hy0rmBrt39yxw,1601
transformers/models/fsmt/configuration_fsmt.py,sha256=cwwkeXb4iqqVUxQVBTMh_zcO3nBy4TL-BA_nqd6Gfmw,10063
transformers/models/fsmt/convert_fsmt_original_pytorch_checkpoint_to_pytorch.py,sha256=BWtn90XQAuWGp8k9zns5St9On_os395ESNgkaXy6y2g,11264
transformers/models/fsmt/modeling_fsmt.py,sha256=O7JhuKO9ykChv_EYwz-EF_qRbuthkVbmLLO_ObGSHrc,57717
transformers/models/fsmt/tokenization_fsmt.py,sha256=bdkwFgl9kUloobCmnpXpQN6bsUmH5xlFt7Je3AYeRXE,19233
transformers/models/funnel/__init__.py,sha256=Ne8K6BFtuQ9o4xhZd6Um8ErszGxzcmQp1S28yxG105w,3846
transformers/models/funnel/configuration_funnel.py,sha256=nHVPG52luFHXMx5TWb0jIWKFMvh4_WUdWJEvH0fco6g,7651
transformers/models/funnel/convert_funnel_original_tf_checkpoint_to_pytorch.py,sha256=d4-Yv59rKaIrXIyXpW4S3LIP_nEjPvptO4izNiuQFKE,2334
transformers/models/funnel/modeling_funnel.py,sha256=z1rMNNMUL8_EgMKFNxTzjRhQQOVCHbpRe6EOy8uDs9Y,69450
transformers/models/funnel/modeling_tf_funnel.py,sha256=B2wzV1dufK_PtBYaVCTSVmNtPg4l6s3-Ug92yRg6UMo,80164
transformers/models/funnel/tokenization_funnel.py,sha256=Fert0Syart7HsNwJGqaXc5EQq2rbYpHzO8qnxMmBPUU,22686
transformers/models/funnel/tokenization_funnel_fast.py,sha256=6orKsz7fGZjcgW5LJXo8QwCL4-ceaVS0FrgmqnaiNvE,8643
transformers/models/fuyu/__init__.py,sha256=W6x4OjGc1H2KkVqkoLdUdizZjUYWSaXGMqoBzlw_qbg,2110
transformers/models/fuyu/configuration_fuyu.py,sha256=tJCtTV1iWl6sGvLc3ufDgDKq2d_bSEHliTGhELuFLPI,9958
transformers/models/fuyu/convert_fuyu_model_weights_to_hf.py,sha256=c8A4qiUY47MfPeEG518qofxFdzut0me3EtFNizEHv6Q,4847
transformers/models/fuyu/image_processing_fuyu.py,sha256=yiH-AUl3WBdYPmrNHavfXwHKhUz1GjLU6dHYF2LkXc4,33416
transformers/models/fuyu/modeling_fuyu.py,sha256=olDYh0c19VYbtJZHj18AFXeAJgQV0QFjvPsP5fOiJiY,18484
transformers/models/fuyu/processing_fuyu.py,sha256=uMNn2LQzf80pbvWuefwanXTWFz3rN6U04PrliLqQZDM,31731
transformers/models/gemma/__init__.py,sha256=k9d4O_4d3dgILjsnNPwrCz2lmgryfXzBJvkNWIBqRrA,3343
transformers/models/gemma/configuration_gemma.py,sha256=dgm0BAe3X1PCfuWlw2OJsSxhtTqAwg7NJY9m49_HBSM,7788
transformers/models/gemma/convert_gemma_weights_to_hf.py,sha256=UCoyJd4wVYKlikKMK0-9GRFAa-Cm3OtLt7oSJjXOuPA,7366
transformers/models/gemma/modeling_flax_gemma.py,sha256=YnBPw6MsDvNt9QXpPWhlXRJMZ6lOV-HXax_tI3vpqwI,32333
transformers/models/gemma/modeling_gemma.py,sha256=goDEwTqWxYE0MhQNc-v14HMZOLdxijMrqS5n3Wr4AhY,59803
transformers/models/gemma/modular_gemma.py,sha256=H3UmAkX-p0DJVvduktIuKuSW19NWSEObFUxNH8RZK1s,47120
transformers/models/gemma/tokenization_gemma.py,sha256=9HYXyjug9NoLJQ0abi24Hzc-TqtzeqggMWTFHGkQ938,14160
transformers/models/gemma/tokenization_gemma_fast.py,sha256=bTIi46E_PXDmRwD8hQG0AI6HRlj-03Y7itWFA6tclQE,8279
transformers/models/gemma2/__init__.py,sha256=37KwSviwoiPmxwyXiBLr9hRQAVpCHOds0cRBsR8M7t4,1776
transformers/models/gemma2/configuration_gemma2.py,sha256=r3vsqT0vc70G5co-Ua2stgXhGldmfd6N9IHEfSwE3VE,8718
transformers/models/gemma2/convert_gemma2_weights_to_hf.py,sha256=Dp4GAX_zZ5GCs8bLWmUuc7Pbw4pNOUIfFKOZ3KMYi8A,8543
transformers/models/gemma2/modeling_gemma2.py,sha256=v1M8syVleLkr4PpadyNgusu40ekegIPsDUJ6enbFhVU,62551
transformers/models/gemma2/modular_gemma2.py,sha256=Gm-lhbnZFGQhxqJ6I8KSrFrFPL83zwMaFm5WT-oUfSQ,41463
transformers/models/git/__init__.py,sha256=2S18LwDgxXkRd4kszbkAVbEFqfnGFIhiARQOOVRdoGM,1724
transformers/models/git/configuration_git.py,sha256=6OgVliEpJxVvVoG7ty-HeSeX_xS7S3dFsL0sX5_Y_lM,11214
transformers/models/git/convert_git_to_pytorch.py,sha256=V5Mp-Bzy49DDkULMWEFBFUYv9XBK4p7DSEDrsnqDd9M,23105
transformers/models/git/modeling_git.py,sha256=w5yBmjsDn1HCkO17KKD5V-4C1dWah8aTmRu0LMtzXVM,73427
transformers/models/git/processing_git.py,sha256=RmSv1auXvBgCrnFTn5EWQ0TUiwBwKDUin2QnNMcsw8A,6120
transformers/models/glm/__init__.py,sha256=fIafw6FAflbbeG_nEM_VPJyMJHnu_NbWHTHjECIAvIs,987
transformers/models/glm/configuration_glm.py,sha256=oCn6aqz65lhGwBfzJxrblIvd-ubUcd3fDoNGODyI3Mw,6655
transformers/models/glm/convert_glm_weights_to_hf.py,sha256=X_J-V_d1_kAoynzZcBynE9Kilj1DZLaS5N--XKs6iRs,6294
transformers/models/glm/modeling_glm.py,sha256=j3wyNUrpz9xlqmC1TGhWg1-mLVsY4Ly6P0e2Tp8W7bY,58583
transformers/models/glm/modular_glm.py,sha256=sM44ThHTXCqyTjDZy4PGcubd18PWfbrCM2xYdgsM_WU,6084
transformers/models/glpn/__init__.py,sha256=1uX_pAqV1QHOi0d0L428UupMAcFBY6OWSDE6VXugxOE,2216
transformers/models/glpn/configuration_glpn.py,sha256=fyIPU3XI3ip18MTyoha9pbGqs4cqfRv1ZO7mztj3TnI,5971
transformers/models/glpn/convert_glpn_to_pytorch.py,sha256=i-_gJfqz7ethjzLpEz82rajkhSvpXULlI443jFdczsM,8557
transformers/models/glpn/feature_extraction_glpn.py,sha256=S263LFeHVRym_jKt8KkTOjjtA1_BqARnUgbSFExgPN4,1172
transformers/models/glpn/image_processing_glpn.py,sha256=J0onOC45RwwSp26duP7gcu0lwU5UrfBynLN8JrsLd1c,10667
transformers/models/glpn/modeling_glpn.py,sha256=vr8a7Zd9MZmlPRbeWLb6DHjIYcrVYZUfeVQGTe1UCjU,31431
transformers/models/gpt2/__init__.py,sha256=G--pddL9u3r2dd5kVlk_w9W5HCthhUt2w4nQyS6kejc,4406
transformers/models/gpt2/configuration_gpt2.py,sha256=Nonqto0eILMcr_OJdvn3sSSEeRMh2EpP9715GyBcrQE,11983
transformers/models/gpt2/convert_gpt2_original_tf_checkpoint_to_pytorch.py,sha256=3qlC-RgCP9x3U-mXQlUD7OEF3q4-b-EF3njeqp7X-wM,2531
transformers/models/gpt2/modeling_flax_gpt2.py,sha256=6vAeL1SwHlYUxTwHmfHXEYLuvTJoLRq5zl_GwUm5PiE,32014
transformers/models/gpt2/modeling_gpt2.py,sha256=z9x_h4uYhz2q9EKxq6a0bew4uOeAmZmLYOzXI-JpYY0,85481
transformers/models/gpt2/modeling_tf_gpt2.py,sha256=fMFJ5OUH4ZQjp9XhXbNeDv-VYGJnp7TkHBj7yiWmRKw,56596
transformers/models/gpt2/tokenization_gpt2.py,sha256=5-E97FVckxXYlPuzGwhMbvI8VRogjNboVuxkE76P6D0,13139
transformers/models/gpt2/tokenization_gpt2_fast.py,sha256=F6Lkyhvva9EQANA9rdq6VofjBk_jjzaaGFVDGf-NLUs,5751
transformers/models/gpt2/tokenization_gpt2_tf.py,sha256=Ptg01f1bV0fAvI1JK6v-FE4lVKUPIiXrxxPrf8M7kgU,3833
transformers/models/gpt_bigcode/__init__.py,sha256=lyCKNUBPoNKvoI_J7Q6Bdwk81Iu8aiuEJEpoOXQJN9k,1841
transformers/models/gpt_bigcode/configuration_gpt_bigcode.py,sha256=9NbDfger_JmNbVLgzv7RCMQj5JhZQD75lEVPYYVT0VM,6278
transformers/models/gpt_bigcode/modeling_gpt_bigcode.py,sha256=YFWUEEBhFVa64I-tkMHdhcISJAsiGjWFXeKkq1u6kA0,65857
transformers/models/gpt_neo/__init__.py,sha256=BrgSXDoRNyH8RJzscHXLBvWPB5fTe5EmEWL-8a2ZNxs,2538
transformers/models/gpt_neo/configuration_gpt_neo.py,sha256=0Lm6Ye2G-NP5cPkaKpV8s9Ta4FuxgilWQ8WeBbAnT8c,11831
transformers/models/gpt_neo/convert_gpt_neo_mesh_tf_to_pytorch.py,sha256=qerp3UqKhts8pfHtNNBJ30YpyX_Qsjfc33ubRabrRkU,2588
transformers/models/gpt_neo/modeling_flax_gpt_neo.py,sha256=xgwE5UixFan9wDb9ScOd8DcEH-o1Iu-AX1bNkMWQFEA,28074
transformers/models/gpt_neo/modeling_gpt_neo.py,sha256=e1zq57D_N4EQL77BLImi1Ob1G_1rONBWQmN4YkhLNX8,59185
transformers/models/gpt_neox/__init__.py,sha256=soLYUXlaucUIDwxsGAQc5xc7XN702tpm3mLHg4FgN3A,2411
transformers/models/gpt_neox/configuration_gpt_neox.py,sha256=1WfT3HaUxuuhELTQyQwlxhuUMNTYMMUmwSJ3Bge5Flg,10420
transformers/models/gpt_neox/modeling_gpt_neox.py,sha256=CpD-ap9OhwZ57lRpIhWW0lt91B195TWlzsmrqqlm_fE,69156
transformers/models/gpt_neox/tokenization_gpt_neox_fast.py,sha256=C84l648NkZHbl7SPyCxCOI2akjMi0D_h-1PQxOAhrf0,9429
transformers/models/gpt_neox_japanese/__init__.py,sha256=6JL1l12MGqBc3mWdwXkFAOjBwJnvI4Pay8tKAvtvHxo,1934
transformers/models/gpt_neox_japanese/configuration_gpt_neox_japanese.py,sha256=yQe5dzgPDyvYrkHQmHLq-LBF10Hg9qkMPxb5XHCKe44,9084
transformers/models/gpt_neox_japanese/modeling_gpt_neox_japanese.py,sha256=AcbMW5CEfha2LLnIGf4BzubaWI7mGB4VzUw9Jo3eL3k,42288
transformers/models/gpt_neox_japanese/tokenization_gpt_neox_japanese.py,sha256=-2DogJLsggKw10OlUkDaWU-0t2pljTxolIMjq-UPPJk,16209
transformers/models/gpt_sw3/__init__.py,sha256=qJj7vF8ES37BwsKbJE1zV2rPUdmM3vx8mckIFuWrJSU,1361
transformers/models/gpt_sw3/convert_megatron_to_pytorch.py,sha256=CBBDQ0Kb94o1VGiJ2XbdZ4vrPiFkwnaASQXxlGVWwRc,8155
transformers/models/gpt_sw3/tokenization_gpt_sw3.py,sha256=OiRC3dg9VjzuPfkNjWuHbvk0T2zT794xK4M04i9dszU,12470
transformers/models/gptj/__init__.py,sha256=x39m5y1hikryeIRrGQt9e9exsjZp2FQlyQxmJ2y6haU,3112
transformers/models/gptj/configuration_gptj.py,sha256=5UHoRlDFy7aPnVObM2A1txoJXmIswU_h9XAffkxRpQk,8784
transformers/models/gptj/modeling_flax_gptj.py,sha256=VaYTrxQosqkIqHcbKcDFinT_z3aofwdJLasWAqxjRlM,28525
transformers/models/gptj/modeling_gptj.py,sha256=hLjjywbWiiLwtXFjMRB3QmjE1dFUlfJphYBh2_aEkXo,62412
transformers/models/gptj/modeling_tf_gptj.py,sha256=qF0_MGa2117_wVtli_uuIZqB6GIIEFkVU2txpwVUnHQ,48087
transformers/models/granite/__init__.py,sha256=GJDr9klPSZcxXbXjAdwKMM8nB96VAvxVGIP2N_FeTJQ,1633
transformers/models/granite/configuration_granite.py,sha256=IBUbz8YAlBDUExZwOC3IWsLDsuRjoYhCzAmpFk4PsbQ,8664
transformers/models/granite/modeling_granite.py,sha256=NVnln4e3Wwlw-d6ST4qoGYRhXeVQ5RfrsoTLI7ClzHk,53892
transformers/models/granitemoe/__init__.py,sha256=-lyGZuE9VnFc9Sl_i6U45t7I7EFZXU2H_g8IF-3nAdk,1669
transformers/models/granitemoe/configuration_granitemoe.py,sha256=VR6pZzWVaE2N33RLhg7JV-dBUdXkmzu_NAxcfi9XdcE,9367
transformers/models/granitemoe/modeling_granitemoe.py,sha256=24eMuY7f-nruAs-jUNXb6dYhuUSiv0jzvn0aydAuh1U,65246
transformers/models/grounding_dino/__init__.py,sha256=IFa_5LTMMYXVAjNYxHnjSaqMczCg4SONylcFap272nI,2331
transformers/models/grounding_dino/configuration_grounding_dino.py,sha256=FU7rFbSjt643c__xQ50lg5_HeKM5YxSIKKfl-EulIRg,14782
transformers/models/grounding_dino/convert_grounding_dino_to_hf.py,sha256=U3T2-FjtYv9unK36_iA17Ifww7kWWYWpPbVC6u6-unQ,25445
transformers/models/grounding_dino/image_processing_grounding_dino.py,sha256=NB2t45NN-VComePx1OvZUf0yF1wSRTAe7cb7tR9ig3g,70793
transformers/models/grounding_dino/modeling_grounding_dino.py,sha256=rUhxSkx_uRuheC5RElC0yoQkGqoLfb5zDmG5ejJhmEI,133573
transformers/models/grounding_dino/processing_grounding_dino.py,sha256=lzFh7mCqf7TDzSV2c_Rd4QYH__JmiX-u9VfH02iDowQ,9492
transformers/models/groupvit/__init__.py,sha256=Dcf-QxcWYp2gVxoLrKPdvzHUZPi-XR1Cvni0i9dU2GA,2567
transformers/models/groupvit/configuration_groupvit.py,sha256=3k3xxy0YagQHmOgcNgnaBsHb7eOr5UJRXw46sZmhKxM,20696
transformers/models/groupvit/convert_groupvit_nvlab_to_hf.py,sha256=9gQxkcjVNCP5lvV54SbbSsOjkKCHORcoiwq2gcczYCM,9775
transformers/models/groupvit/modeling_groupvit.py,sha256=AhPiw3HRRzL9ITs87-HEKEjYr5TFFcH5O_UGWyv7RYY,67835
transformers/models/groupvit/modeling_tf_groupvit.py,sha256=Mv4b1Oh0p0sWbW0ueAL2LihpEIaU1sX_-jWooJ8jDXc,90069
transformers/models/herbert/__init__.py,sha256=Sp9gQIqlUhZHausuaL2MFYDqJW4vvsVGLbVryR-kNl0,1472
transformers/models/herbert/tokenization_herbert.py,sha256=jpN_3Sv-mID8u1R0d2tsDtOWO_k1YDzhQreDphL5blU,25034
transformers/models/herbert/tokenization_herbert_fast.py,sha256=QITcJycMNQuUIumL_an1mNz55fJADkW9S6jdBmO99KM,5926
transformers/models/hiera/__init__.py,sha256=5roInXycWWxhrz_rALK_Sh7-Ic9gyUKZX3AH7vt-AFI,1720
transformers/models/hiera/configuration_hiera.py,sha256=IIcOJUBI7oYesrr86F1aD35D9fH7XLhcQtYl15Rf7BM,9291
transformers/models/hiera/convert_hiera_to_hf.py,sha256=mFswCFkaqq_0sJJFOGZ2eQEAnF4YijmFJexrY6W9G3c,16620
transformers/models/hiera/modeling_hiera.py,sha256=UJVz42yf4sSFXycxnQOu_SxPft_Zcowkn8donRgydFw,69662
transformers/models/hubert/__init__.py,sha256=6A-uKqqjcmhIYBpcLhnYjAmSGzWTGzN67scpN5p9ruk,2256
transformers/models/hubert/configuration_hubert.py,sha256=q2Z23TAXdS-GmQq_-RPtO8WZoMUTekXvB6z9goVZ-9c,14675
transformers/models/hubert/convert_distilhubert_original_s3prl_checkpoint_to_pytorch.py,sha256=4EOE_E4BIIbVesPmNCL4bVO5A91wxMpAhfAdOMpTDzg,8941
transformers/models/hubert/convert_hubert_original_pytorch_checkpoint_to_pytorch.py,sha256=0uu-lG5QooIoF0GCZ4Dr0gaTjo6idRqdlVZntMh80-E,10379
transformers/models/hubert/convert_hubert_original_s3prl_checkpoint_to_pytorch.py,sha256=QuwvhsVyqcAXCPQN-peyGc-1C6Gjk6d-ha4Kt5TYF3w,2894
transformers/models/hubert/modeling_hubert.py,sha256=4TwmCB8EntlXUffKh8WmoA7I2a6fbviK4szXNVAFNJc,74149
transformers/models/hubert/modeling_tf_hubert.py,sha256=MFIb8UpsfeuVetYKTfu66dNIA7eJrM-1QGd_sOjcw6s,70700
transformers/models/ibert/__init__.py,sha256=V63S09fVvVVCjY4lMvVL8tHlW6KjrOtyCthRjsLxehI,1914
transformers/models/ibert/configuration_ibert.py,sha256=fFf0tHcXK3Cbp6BYOc96slvvxSPfKn7PaRrYodG-hQI,7047
transformers/models/ibert/modeling_ibert.py,sha256=dzcxR-5BqBR1-4WzuO3zHbUdgUlHsB5lP0rw7wJ1bk4,56883
transformers/models/ibert/quant_modules.py,sha256=ItU76CIx0XcZCPOR21dz99J9k5rK2fzffQz0jJCuNmM,30072
transformers/models/idefics/__init__.py,sha256=Xr43c6otTmkh8HlSFETZFaEO21Ocjkw_hc3JZlw4Kng,2834
transformers/models/idefics/configuration_idefics.py,sha256=IF6AVtYGNxQqAguSi2qTWMR9kIt85-3EuL1rcEujtIE,15366
transformers/models/idefics/image_processing_idefics.py,sha256=0_fj1Kf8WNyWohSeuCIJHP6jddHaCfzb2LIlExbOjvU,7764
transformers/models/idefics/modeling_idefics.py,sha256=3Xg1ieER7_HsG9wVezcWZP0vOEC_QlLVdHbxsp2w9tQ,81328
transformers/models/idefics/modeling_tf_idefics.py,sha256=jQZxhqTtEXfwn8yZPiHIMqzz9QlkcmrASXtIK_jtt_w,80221
transformers/models/idefics/perceiver.py,sha256=uGv8FH2wZ-NO1EIaFclI1nkwUqaTA7i0PS9XxY7ivn0,9433
transformers/models/idefics/perceiver_tf.py,sha256=rYqXv9j6bmr4NyZLAV1MhVMiiIMV7RZ9CafybPtYc9I,10006
transformers/models/idefics/processing_idefics.py,sha256=qAa2SUoz6ZDqIhQVgNftlPuvpl-1lSK8wJ-KxcLmhAk,23445
transformers/models/idefics/vision.py,sha256=EVQ5lOtdV00gK_3TAuLI4zUeHbw4zV1RdZNXZqUXXiQ,22493
transformers/models/idefics/vision_tf.py,sha256=Kf_PenRY1vhlBA62PvjdvUDyQTKIi30XqB_bMBN1Mrw,26010
transformers/models/idefics2/__init__.py,sha256=qQHmSTElW4-t5lQVlzJJUO-m9Sr2RWfsRTp6V0YewAk,2213
transformers/models/idefics2/configuration_idefics2.py,sha256=rbUJCyApmTzykO2KraekwvJMS9dLR3Pt32xcwYHCmB4,12715
transformers/models/idefics2/convert_idefics2_weights_to_hf.py,sha256=3nd_V1qNTv7DehZZQLLAeKh0086xvjECNuWBvJmFbNM,6669
transformers/models/idefics2/image_processing_idefics2.py,sha256=RAJpeMN3iNaL4YJtV_6ATVYlva5h_pVFR_bRgHL3MWY,27422
transformers/models/idefics2/modeling_idefics2.py,sha256=qt0npFHB4M1iRIQdtAyECmBWh1aP4s5r9iq24wcmgnc,84412
transformers/models/idefics2/processing_idefics2.py,sha256=_DXVHxWggTa66KlB6ns0Fdyy_5Je0Ww6yt9TVxlFhXA,11593
transformers/models/idefics3/__init__.py,sha256=TsRnOyBFKAmPE6iZFXJyuAx7rTbMNm2A-504P4xq5z8,2213
transformers/models/idefics3/configuration_idefics3.py,sha256=fyih2Rgm8_8a9rH3crGkNKgJe2iqV8sQ0Qlw8SQrfC8,9305
transformers/models/idefics3/convert_idefics3_weights_to_hf.py,sha256=x-Ve2iDf4TL9jXcpmBKJBMPDVgqrfMEcMrcIFp_RWDA,7373
transformers/models/idefics3/image_processing_idefics3.py,sha256=rQBosHz_dc4qsSLnt5H1hn-xbj0t-R_ficnY1H4vlpo,42844
transformers/models/idefics3/modeling_idefics3.py,sha256=Xr_ZwQ3DYKM07dLhqYj9mktGRDLjiZkNbxXmqspOvfs,64754
transformers/models/idefics3/processing_idefics3.py,sha256=s4EYWH47y4BhCv-JmwzQCMIMaaVNU7T0x-75pwBn2ls,15184
transformers/models/imagegpt/__init__.py,sha256=QdFa7OkvADEoPElX5MqjuRX1wzKmGpPdsHp-E6Gn3Lw,2468
transformers/models/imagegpt/configuration_imagegpt.py,sha256=eAa9tRieq02IuqLO3O2uGsttkU_hdXVBf6oG2tkA56A,8719
transformers/models/imagegpt/convert_imagegpt_original_tf2_to_pytorch.py,sha256=BMqKNGn1Jv4rntP9fG5C0yG_lF1MY_0h9yv0Qt4rjpM,2690
transformers/models/imagegpt/feature_extraction_imagegpt.py,sha256=iCpQ4tU3Vml44KgO43kYJvv-RcZVxe8tc794gxUktuU,1200
transformers/models/imagegpt/image_processing_imagegpt.py,sha256=apmTF5c7APiULhjBTCUhQU6yV6vFaZUxF7sKFO482Ec,14305
transformers/models/imagegpt/modeling_imagegpt.py,sha256=Nj0u_63566xknToxTSTB-Y-ZzadK3gPhrLHXiVVJl7o,52006
transformers/models/informer/__init__.py,sha256=v8NQJEtKbrVWXQZa7urA_8qDWVoGSn5bJNhIPYOxYNQ,1650
transformers/models/informer/configuration_informer.py,sha256=yWIqTlCyX0QcQRNuqktsfoSr2EHss9fe4Vf6yM7zOvg,12412
transformers/models/informer/modeling_informer.py,sha256=3X4IPVrOJohAPshisSSCukjPQryYSIjb2Sic1KV-paI,101496
transformers/models/instructblip/__init__.py,sha256=HjvAHdrlmHFfBe-bHP_UOOgegMc4BsHLw5SNu8hFLtQ,2063
transformers/models/instructblip/configuration_instructblip.py,sha256=D8_vmEEYxnbo6LCulkxM_U_wIS8xLmtp5JJXXVcIUbo,17402
transformers/models/instructblip/convert_instructblip_original_to_pytorch.py,sha256=iustpBsjHHzjQzbAhPJvhI7ZBSXCDoa9njtK9m_gm_I,13399
transformers/models/instructblip/modeling_instructblip.py,sha256=p0YEizTzE22KIKZXhJvAvH-9hiJHgKrLyWHjIQnYbgY,75966
transformers/models/instructblip/processing_instructblip.py,sha256=qBuWxz-WWYcQqtv00ItCxLRvCAfuIq6GRKS6czVlI60,10278
transformers/models/instructblipvideo/__init__.py,sha256=sTOPrPaq8f-igvxw5Bd9Tu9_bPMDDg2fmHk9sjjLpw0,2688
transformers/models/instructblipvideo/configuration_instructblipvideo.py,sha256=-_IprLMnFPNxF_cIhri4hvTgagowubGfvZvaSjor6aI,18504
transformers/models/instructblipvideo/convert_instructblipvideo_original_to_pytorch.py,sha256=F69HaLiFYx7EyneqAjLG7q5jiPEy0bMTF8Q1qF2h7d4,13508
transformers/models/instructblipvideo/image_processing_instructblipvideo.py,sha256=FtXan0x5d9fAZHuarA8SOQvULzvT4zrhVUuLgcfU6gI,17364
transformers/models/instructblipvideo/modeling_instructblipvideo.py,sha256=Gko9OudcZaTQNLDNRkU1Z1HUxGw4sPw20U1dGZ2aihc,77814
transformers/models/instructblipvideo/modular_instructblipvideo.py,sha256=1Lhv6ndxpzZsdD-3GJgsosO27CDC2M_D7Vg8u6IkB2E,23510
transformers/models/instructblipvideo/processing_instructblipvideo.py,sha256=bLseV8Y1yf0pERYiNaI-pxiF5B0IDFP43tuG3znmOYY,10983
transformers/models/jamba/__init__.py,sha256=aD1sOCM0Rjk2I3Zh_fEh3xeot9EVYE3X3nKi8rP-KyI,1661
transformers/models/jamba/configuration_jamba.py,sha256=gEuig5F1qj4kD5kV2Tve-Q3ec9o8Hm9l0oIYcOqlOaU,11715
transformers/models/jamba/modeling_jamba.py,sha256=w0BdRPXrFMMevwQANxEkf_Koxd9j1XNHTceXRCrqMc4,80441
transformers/models/jetmoe/__init__.py,sha256=PQ_qABkj9KXL5ALjWoHuYScccKDv_a05cEP0aN-vEM0,1692
transformers/models/jetmoe/configuration_jetmoe.py,sha256=xIqVx4vO25zQkBb5h77BSRvwG2jx0oTwDvEKeIaEUiw,6775
transformers/models/jetmoe/modeling_jetmoe.py,sha256=32_nh8BbO5sGmMkQ6BOhq32UjEEmW6DMY7dA9umkL8I,65541
transformers/models/kosmos2/__init__.py,sha256=l-fM--wymMrVutXFv4V5xF7CKmLkiGFccADN9bhcsLU,1787
transformers/models/kosmos2/configuration_kosmos2.py,sha256=AczOUgyNRdGvUympqJQK4GyBJjac-4xZU8B-kD9LyuI,13219
transformers/models/kosmos2/convert_kosmos2_original_pytorch_checkpoint_to_pytorch.py,sha256=3ejv6hUd6irzFnmSuFVI6Eu1NVWmtJf3_ql2h9P4AHk,2724
transformers/models/kosmos2/modeling_kosmos2.py,sha256=R32tqYE27eeQPHBCjLAbPl_e2Gjzf8zwBJQ76zt_1n4,98037
transformers/models/kosmos2/processing_kosmos2.py,sha256=sN2ml3b1s-KfqSQPD6zUNEmTt4IDhJgXA1LNblWYzmM,30788
transformers/models/layoutlm/__init__.py,sha256=ToNTDJFtx8GiUufvjVOwBGLBVszbtuPwQRVw0GyZz7o,3495
transformers/models/layoutlm/configuration_layoutlm.py,sha256=Lk-F-JN7G4cGWl4LOjED8-5qauxEmqvQqNtLq_kXsn4,9081
transformers/models/layoutlm/modeling_layoutlm.py,sha256=J4pDOQtDslQ6b6w6tz5gxQF4DUeKXWvsqEAruPPPdD4,61013
transformers/models/layoutlm/modeling_tf_layoutlm.py,sha256=skvosKy_iLHtFzQf5-NK3ONSZs3C8TJJ2xpVxV2cyiI,73116
transformers/models/layoutlm/tokenization_layoutlm.py,sha256=-BlnfhXazZLOaECmPFeviGft4gUqbNtqkCJ7yvLocM0,21260
transformers/models/layoutlm/tokenization_layoutlm_fast.py,sha256=eUfhfNdwyBP6YvV0Jm9UVsHP-6PAVzFavgeCOYqdgTY,7786
transformers/models/layoutlmv2/__init__.py,sha256=HbfFYtc7N47btSqz6mHBI8ztlHUtiF2g3L5AR4ECAVk,3247
transformers/models/layoutlmv2/configuration_layoutlmv2.py,sha256=0aIGCavEpz0r9tGScL4ocX2FbZ5T7NQqYGrjRITA_js,10881
transformers/models/layoutlmv2/feature_extraction_layoutlmv2.py,sha256=M9bDCpKBLI5paxor4ioa2JjEDhSH9Np-PTbgHh2V9KI,1195
transformers/models/layoutlmv2/image_processing_layoutlmv2.py,sha256=utD59Est4JvyZR86C3rwW4iKfWxrOwUcLrqp0hruZ2Y,13455
transformers/models/layoutlmv2/modeling_layoutlmv2.py,sha256=bIEnl8Z0mpNHd2m611Shnu0uF1PsdS87rBb0LtLz-0g,61911
transformers/models/layoutlmv2/processing_layoutlmv2.py,sha256=xyhBq9pYYmNYOfK2c13gA-f1cWzu1fp0kO6FC7J9DfI,9292
transformers/models/layoutlmv2/tokenization_layoutlmv2.py,sha256=Pt4TbsqfLQ76UoxVXBN2qCee6mb6GmOw8aKiklzs-P0,73183
transformers/models/layoutlmv2/tokenization_layoutlmv2_fast.py,sha256=vcMFr-Q8VfOkjxDafRoKGy4e0bBqKdPXim4GaPWt4vo,38100
transformers/models/layoutlmv3/__init__.py,sha256=7LXVZdSdhLshkjwfXi0cNhsgrhFsenwN44DLzWZUq3Q,4192
transformers/models/layoutlmv3/configuration_layoutlmv3.py,sha256=nMiSp7O0bCmu6dKdZsd2ITIsaf8nFGRTMJOZcXN--J8,13204
transformers/models/layoutlmv3/feature_extraction_layoutlmv3.py,sha256=jWsmsi2mym0meek1lHWqfqxlJgMJdY3cgfQ_4ASEbto,1195
transformers/models/layoutlmv3/image_processing_layoutlmv3.py,sha256=-qeBbGdgJNt-5qKtXHLOSFYzE82buHVBe03MvQ1dth8,18324
transformers/models/layoutlmv3/modeling_layoutlmv3.py,sha256=Njg7PzstKbLybpn7eYTgN0pkoVRVp_QuI8rclzpME5o,60391
transformers/models/layoutlmv3/modeling_tf_layoutlmv3.py,sha256=S1z_MxVYQMuj_IxTf0_45fIoYnTEDhbXaP7gx3OnCYs,76775
transformers/models/layoutlmv3/processing_layoutlmv3.py,sha256=ShtvBmZjGHbprdB14v2QsIgVir-74gEnTGHzvL31vCI,9143
transformers/models/layoutlmv3/tokenization_layoutlmv3.py,sha256=6SPu8YxvdVKDruKvpmtUaAFKsswCplBD7B6stN_SJAQ,73191
transformers/models/layoutlmv3/tokenization_layoutlmv3_fast.py,sha256=0HF89ONx9TnKFclfCdoODYP5-E0myAqNgryp6TbTBlg,40348
transformers/models/layoutxlm/__init__.py,sha256=AIvjzuqRPFXFuWXxnOlp9pBXaIT5Zzx7fwtg2KKVETA,2037
transformers/models/layoutxlm/processing_layoutxlm.py,sha256=BCF0b56oTulabhz45Z8wXWl5EXVRbeI-ypoE49PYz7k,9243
transformers/models/layoutxlm/tokenization_layoutxlm.py,sha256=QZDf_MigIyIjUJYZ0OEBcII66qAO20UXO0SW-RIQeuI,58201
transformers/models/layoutxlm/tokenization_layoutxlm_fast.py,sha256=lQFJByikJ8g7e0KFQhZUiqj4som6hDGDRmCDw-v_f4c,40581
transformers/models/led/__init__.py,sha256=6B96iGVauRaXeU-4rEOxQVdFcNHoTKm0Zqk_BjTzDl0,2844
transformers/models/led/configuration_led.py,sha256=_YJsNOVeRtKJSAffbitjohX6ZdLiXirBLRmZOT2XKDM,7419
transformers/models/led/modeling_led.py,sha256=23JPlY3PVkieDXxcRpt6WOc8vA1EHFhtZWzYz_66C5o,138017
transformers/models/led/modeling_tf_led.py,sha256=MZjMSGc0K7qe1DatVfwzpxynUxzwW3kNvcWVVs5yUvA,123070
transformers/models/led/tokenization_led.py,sha256=E-a-6cFaziW-2EC_xPTDD9bMceF56W-LWN8NDZeRHv4,19836
transformers/models/led/tokenization_led_fast.py,sha256=y2gT48lzwTeJ09A9NlY23nvuJGKjX3nO3f-3WCb31ew,14626
transformers/models/levit/__init__.py,sha256=LNIUEn_GOEZQo0sP4HZFMu2s2ZRnbXLfq8Q2Reqhdt8,2336
transformers/models/levit/configuration_levit.py,sha256=EnmjmpzPJ22bPbKqI1kGvtk_R7lVE7rxz-r152Txlq4,5716
transformers/models/levit/convert_levit_timm_to_pytorch.py,sha256=TN87M03CQV4LRb0jH0SCNTtfoZ8rGPvbLgykEC14kLE,6257
transformers/models/levit/feature_extraction_levit.py,sha256=l2RHbrbg9MzRqKr_ErOo_AuiSv93Gj-Oq6w0v2p-Izw,1204
transformers/models/levit/image_processing_levit.py,sha256=Cjs4r5sMBfGF9un95Se_JIlmvT_yR08ohxy-SybRE_M,16566
transformers/models/levit/modeling_levit.py,sha256=gAyS5fxB2MW1APxIFbfmE42kKSDpLspHBVNUcnwShDs,29365
transformers/models/lilt/__init__.py,sha256=qGFJxFObaHb-jxb0fwz0GBNczbCUlO4kQh1u2huUW-Q,1741
transformers/models/lilt/configuration_lilt.py,sha256=BCiHtpFR6NmEOJxZ8wWIvqzD9XE-vwaiDuc16Qw4guQ,6694
transformers/models/lilt/modeling_lilt.py,sha256=zinA1GM1fu9wYIsxvIiu7244fN7VfQ_UypCgmnp4aes,52704
transformers/models/llama/__init__.py,sha256=DYt0fKALZE7Q6l5HUhOp0FRWxtjU8DGNDIDIHmYY-CI,3353
transformers/models/llama/configuration_llama.py,sha256=3iJcf348UtLYSIBcd3vq55y2Fk9QSEqDiU_GuLU-oOk,11302
transformers/models/llama/convert_llama_weights_to_hf.py,sha256=0RoiDNp8Z1xH9E9eQD8yYADSbPxShddTEgZ8hNqGtf0,24137
transformers/models/llama/modeling_flax_llama.py,sha256=KVrY_9RbgXnfjjzcBOMJ3WkjvkGR5CUyCZmcdSjNgO0,30569
transformers/models/llama/modeling_llama.py,sha256=cz52Jf7Fq8-kFqubhmy5GHXUEAfy4xw-kNKuARGumPc,70038
transformers/models/llama/tokenization_llama.py,sha256=53lo4kzPxOkq-eMyIbeQR3tNJky4ibrrvz603HyTVtM,18629
transformers/models/llama/tokenization_llama_fast.py,sha256=Uux-EKBR5bubfzmvLrylx-f14TgEO0WOulYz_H807B4,11147
transformers/models/llava/__init__.py,sha256=1KBPz09g-qQ9VaNmfhS_sYps51avbl42MEmbsgcacCg,1625
transformers/models/llava/configuration_llava.py,sha256=KOORxb1XhiVE5_CK0O11vAIeR2Uy9FpwoYIjGT8eiok,5454
transformers/models/llava/convert_llava_weights_to_hf.py,sha256=4HhNT46O2sR0XO_QHQXM821hT_hU4Ol485rQluG7INg,7675
transformers/models/llava/modeling_llava.py,sha256=k1WLplBA5Ko60I7CKKzWy7rIhhbvta8SsQKTaN9NXdU,32246
transformers/models/llava/processing_llava.py,sha256=fzfEGmISn_uqfsmdJeqiUzIV1KhKirjfCYxHFVRYQhA,9418
transformers/models/llava_next/__init__.py,sha256=t1b-q3rCJUMLT_V3kdOw8_kcpmYfV4LFncLAzIuV574,2171
transformers/models/llava_next/configuration_llava_next.py,sha256=fI07cA2_QUaf0P1jE-U8aTNlKU3Sk6KPIvcqw6q7Pbc,6467
transformers/models/llava_next/convert_llava_next_weights_to_hf.py,sha256=WpC-jvY4TtlZGpW9MCI82HUIcQl9lpsUZT_cmzfJqfU,20785
transformers/models/llava_next/image_processing_llava_next.py,sha256=1efTVqecnn-uWVjEM6qtIPHRKwo4BT9xoGEAfx05CsA,36483
transformers/models/llava_next/modeling_llava_next.py,sha256=uVnPta6gBKSkCib93R3p7THqFVXQFRKvsydnEtdUi8I,52687
transformers/models/llava_next/processing_llava_next.py,sha256=QySzF6Ymhq-dEFiHnJ2yJ29bAMlGGlr9irs5nlgkcwQ,11445
transformers/models/llava_next_video/__init__.py,sha256=kx1mBbGJWuj8mrHXgCiLuSK4soIoVH2hDE2qFINonqg,2267
transformers/models/llava_next_video/configuration_llava_next_video.py,sha256=mBu8a7U-t_CMMtKPueqLpRA7_16k-qAH1zkP4N3tkWA,7905
transformers/models/llava_next_video/convert_llava_next_video_weights_to_hf.py,sha256=-2knTp91EFTMB3vNXPGoaIW9u1s7yAXv1XySmGVTpJo,10511
transformers/models/llava_next_video/image_processing_llava_next_video.py,sha256=cpin8M8u7nnVKdIqiirPktoO0pU31z9s5_5JrBClQn8,21425
transformers/models/llava_next_video/modeling_llava_next_video.py,sha256=s-6Hy3CVUC_IgMbYsIZLpzt04lvCUzTP9H5zftejL1o,61649
transformers/models/llava_next_video/modular_llava_next_video.py,sha256=wSG1PFoOaWyZee7ryOLbTnOYdZWJB8mQ1zDpLi90ynU,32107
transformers/models/llava_next_video/processing_llava_next_video.py,sha256=sHQGuPKPWUbcs913Z4Hy-XF7HHcVIzDtZSEIuRmhMdg,14956
transformers/models/llava_onevision/__init__.py,sha256=8NaJ-OFTNaJsWxCl1qpOzSMV46nlsXeX3K2-nBnnVp0,2435
transformers/models/llava_onevision/configuration_llava_onevision.py,sha256=NZJ-ft-Ij7RJ-QkKXVPTpZkT-9s-l7aim-LU5BmhIRo,7520
transformers/models/llava_onevision/convert_llava_onevision_weights_to_hf.py,sha256=Z7tFP1ARgWDWWUvdznw2o3r0LtwMJoM9ydmwiXBVgwQ,19878
transformers/models/llava_onevision/image_processing_llava_onevision.py,sha256=2NJZBI-AlF0YRuzN3y3V8Ho5eOSDhFtBJyul36--DZM,34195
transformers/models/llava_onevision/modeling_llava_onevision.py,sha256=nnpKjxJhipsKNWls1VOxPwgcii3k7PecghpizSGxmxY,41481
transformers/models/llava_onevision/processing_llava_onevision.py,sha256=QMQUUPPrKOTEGeFi39uYY9_7LlF49UCCreQyPRNkrA0,15154
transformers/models/llava_onevision/video_processing_llava_onevision.py,sha256=NHs-4Sh7hwgzoY65ADGQ2vQ-tZBy_qSwfYOorevt2fg,16901
transformers/models/longformer/__init__.py,sha256=1lCNrBs3duWbx51kKZTB4afLXTiF1QTxK0a2wXiHkB4,3876
transformers/models/longformer/configuration_longformer.py,sha256=s_l49e6imnOlCPkqcqiJMFabklp-O0pGlIYtcfdssqA,8765
transformers/models/longformer/convert_longformer_original_pytorch_lightning_to_pytorch.py,sha256=bt-0zsqIGsD8WONirTLHw44ZjIJf-NvMXlLwIg5rqJk,3025
transformers/models/longformer/modeling_longformer.py,sha256=G6vZKyjb-Bu-BUW1KKu_z9S_CMty9XAkhBd2dxBD3s4,113828
transformers/models/longformer/modeling_tf_longformer.py,sha256=MlZVpQCdEG6IYX1AtNDAmQprWrVWzfWKL5w-KvXEYhI,129344
transformers/models/longformer/tokenization_longformer.py,sha256=t64oV7_8WJMD8b2uRr1SH9OaBPddZXBxAKsWbHpGCrk,16797
transformers/models/longformer/tokenization_longformer_fast.py,sha256=PS7I9o5WPEyTCANiRk0WHUcOhKDyc5woV-GBgiX9Lec,11672
transformers/models/longt5/__init__.py,sha256=XovR-KglV-9y3YQZ2uPJmU3TXgcCkAweVSAtg6E0zXE,2370
transformers/models/longt5/configuration_longt5.py,sha256=VCtM_QzSqPxc1_qnonu_NQ0Rpg1_y15k_0uYSkJbaBw,8058
transformers/models/longt5/convert_longt5x_checkpoint_to_flax.py,sha256=att5rZjP3_mlIRv1icJO3wxQxuwFYEvCz4bFr2seW4k,11091
transformers/models/longt5/modeling_flax_longt5.py,sha256=7vhDWHBGIt2v5x9waY_4gV84TxkqFhKOmYjBy8Wij7c,105670
transformers/models/longt5/modeling_longt5.py,sha256=BghXhw7Gn8WUDCBxsnFQ9zfa5dlvM9-XMAY8RvEaZmE,112183
transformers/models/luke/__init__.py,sha256=S3a0nDUUxdNNm_hpHGmseMc2r25-8WTlCQPsyCYjLeA,2215
transformers/models/luke/configuration_luke.py,sha256=D1UhPDtRfrXnbLTJFGb6iamTjvFYMLEdUfVK03SuQiM,6593
transformers/models/luke/convert_luke_original_pytorch_checkpoint_to_pytorch.py,sha256=pfnDfBvJDRyCLBLdcsalZaKV01aEz0W1og2Z364hTDs,7467
transformers/models/luke/modeling_luke.py,sha256=tbTMuJxuxQoWVFpsru4lrNl3L0Csa7V_VcxQODwM8k0,103762
transformers/models/luke/tokenization_luke.py,sha256=yFcYBt0DtOc8zdL239kckC-KguDqry0NXG0yN0IfMbQ,85648
transformers/models/lxmert/__init__.py,sha256=PXJlmgyBlzdmyVm2O6-kTfcFuUNYIog7i2bIl5yNp_k,3214
transformers/models/lxmert/configuration_lxmert.py,sha256=Oiv-8r6fTsvXG2pnGP-zs-XhJhfN63mjqQNT4gBpwXk,8905
transformers/models/lxmert/convert_lxmert_original_tf_checkpoint_to_pytorch.py,sha256=Ps5iNo91Yj3XLkEuXrP2KFLSjWhYJyP-1vtqMyt1Lqk,2108
transformers/models/lxmert/modeling_lxmert.py,sha256=YLR9O8ruS-R1f2UlIqgoEnRIvIOgb8PVy8nt28xIdS4,65842
transformers/models/lxmert/modeling_tf_lxmert.py,sha256=XGMaLwj3fGRBrxbthJsFVYFrNOOxbMPuxHoHHmaFLRc,72618
transformers/models/lxmert/tokenization_lxmert.py,sha256=BYnYg7A1KvM89rAWzZpHdKaRU-4fs6-64rtQ_yVRMoQ,21284
transformers/models/lxmert/tokenization_lxmert_fast.py,sha256=-mHMI4WCLJoXt8nYvJRibyOYfJKByPW_a6nQV_L11PM,7720
transformers/models/m2m_100/__init__.py,sha256=64Jndx6My4y-BYuQtMonbig8Rc3iFC777z--DgHXITw,1812
transformers/models/m2m_100/configuration_m2m_100.py,sha256=oxz9d5PZiwRdOrswXdjANddnOaaQwKUAM9ByOP96cfE,13362
transformers/models/m2m_100/convert_m2m100_original_checkpoint_to_pytorch.py,sha256=xNG8NE20odOve8Z1zKPDHJr5Ev8jM30N-mJsJqfsXtM,3159
transformers/models/m2m_100/modeling_m2m_100.py,sha256=MvOoL4kGUYGP5bXLY40_gjwHhBeX3PP-s623rFwfc54,79234
transformers/models/m2m_100/tokenization_m2m_100.py,sha256=CTRnlsxgTpS3KVUDFH_D4C4UsgG2cuQfie2lHzvvGrk,16321
transformers/models/mamba/__init__.py,sha256=xue7XUej0GVyK7yZrP58iMYuJYYUTvmTAu_pAQIqn2U,1626
transformers/models/mamba/configuration_mamba.py,sha256=TEkcOBRKZxKgEQb3PQe1pqPKyx3jRLt0aW03c-TzqDA,7404
transformers/models/mamba/convert_mamba_ssm_checkpoint_to_pytorch.py,sha256=BK6M1tYEwLwoM7NE3fdm0BRpZH8LvCTGvdvsee9taZA,6454
transformers/models/mamba/modeling_mamba.py,sha256=mHxGRJhoLn7ceahjMOdUADPu9TnzKzSdT_B9sRSxyaU,37990
transformers/models/mamba2/__init__.py,sha256=LWVmldgBBoIomkMmAzIkBdDk6_awOdyzi3Q2hg3446c,1640
transformers/models/mamba2/configuration_mamba2.py,sha256=LIekVdtSEXipEGICUqEB_ZlYS0wM_NcC0itBQV9UgnA,7889
transformers/models/mamba2/convert_mamba2_ssm_checkpoint_to_pytorch.py,sha256=Pa2GsK7tP3N1uPYzDvq9A39BqFIWNgIAcFkQ_9EiBUs,7515
transformers/models/mamba2/modeling_mamba2.py,sha256=Rn1v35Jujrxy66FFmNuN_xxJC152iu1XvcIV_IHV2zc,50946
transformers/models/marian/__init__.py,sha256=Exac7OwEPmDA-_t37rrhbP3XT2fkL-P2rysKMKgvP3U,3268
transformers/models/marian/configuration_marian.py,sha256=ugov-OB0oSAHCFgmrJFF8eqKEkoP8j6LHG-MR3napJs,18328
transformers/models/marian/convert_marian_tatoeba_to_pytorch.py,sha256=KXL31oNi5WuLN25fwurL1ujDwItoQYn-FmTOF2PXTUM,36375
transformers/models/marian/convert_marian_to_pytorch.py,sha256=7xWRLSnFcqxnErnWHoOkwhjEvgrer1JgNU6bhDwYXtY,27124
transformers/models/marian/modeling_flax_marian.py,sha256=sMJwqGgBMHQyTf9TcFZKI7jXRf-gEt_t0PdC1oDzWLg,64261
transformers/models/marian/modeling_marian.py,sha256=QMRMLR5TALvk6CjBRQoXm-UC2fgW9QdYN4P5kzhqnQU,79485
transformers/models/marian/modeling_tf_marian.py,sha256=vwYLwoS-U-3iSfWmakWF3Do9YT0QfmxxZ-hzpW9osXQ,72680
transformers/models/marian/tokenization_marian.py,sha256=wo7Hy2uRzFP_hX84DhoZTvWUeXtWhqG5-BxmcFAsg_0,16812
transformers/models/markuplm/__init__.py,sha256=cK07sEFFcaJXQ6tsst5H19AQWvnsAx33OFj9qsDA9Qo,2622
transformers/models/markuplm/configuration_markuplm.py,sha256=Iy6p9K9Cf0bgtNIWHfpGLp3av2MBTd1IhlIR3Nqcw1E,7311
transformers/models/markuplm/feature_extraction_markuplm.py,sha256=oYreO9SpFwLZMCLO_gROQthx_Di2b7Gi3E4RoPQPJME,6408
transformers/models/markuplm/modeling_markuplm.py,sha256=ifbmTsdMAJAmVoywfKoaQvPhYkHNSEp9v1AcLYj_qDE,57127
transformers/models/markuplm/processing_markuplm.py,sha256=ePyVpFNW7t6oFkcxbEAG4y-_ht7WzWWLmlTmeFFIoJ4,6349
transformers/models/markuplm/tokenization_markuplm.py,sha256=W5_mCluD7C7sMxuamzfSWbKXgQzCTPjA_xLMghdsKE8,70108
transformers/models/markuplm/tokenization_markuplm_fast.py,sha256=ZAueLHCJqwoe_jOZ0CMbR3K-JlFBEFHorA0JT8E91Tk,43755
transformers/models/mask2former/__init__.py,sha256=0IxG5aY2Tg_zZxw_lEVYd2q4nXILDY3gda4jhGQ_78s,2138
transformers/models/mask2former/configuration_mask2former.py,sha256=tpaioPloLCu7uU95tLpAZSjhe8dnErn29x_ZtPbhnn8,12340
transformers/models/mask2former/convert_mask2former_original_pytorch_checkpoint_to_pytorch.py,sha256=v4a-VTdnEHxZLAykOn5AgqLXZ9yFZzhY4CUu4c3XHUE,45688
transformers/models/mask2former/image_processing_mask2former.py,sha256=KaidvBCCdBKKT2f8cADITb2b4zMUu3v1uhiHMPXThKg,57003
transformers/models/mask2former/modeling_mask2former.py,sha256=Kmdg5qad76Yi0D_UBNpa1jR0Yk4JE-rJjBOFvE34dX0,121151
transformers/models/maskformer/__init__.py,sha256=VyRyI2kk-wqsxPvIf5XocifIE8g0uSa0F144s79fEzI,2753
transformers/models/maskformer/configuration_maskformer.py,sha256=C09gAgP3eH9aFJB1FGIscfG5foqumfovJ7IWGi5VVck,10259
transformers/models/maskformer/configuration_maskformer_swin.py,sha256=ER7k7WSqJXvglLLr0kh1emetNU0kOuzrrYlcUs8Xl_U,7216
transformers/models/maskformer/convert_maskformer_original_pytorch_checkpoint_to_pytorch.py,sha256=-qpfICbzwlGqhMNaS36phSUp-aM_qkWO3uw6Md6ntk0,32341
transformers/models/maskformer/convert_maskformer_resnet_to_pytorch.py,sha256=LhKIHvwqSPTV6_UegT-_G-dxsSpmwmX9x_Dvfa9GnRc,20740
transformers/models/maskformer/convert_maskformer_swin_to_pytorch.py,sha256=ylQYD_1uSmCZLz-PtxIc7I2wCSkC-XDgbfMafM7Vs44,20329
transformers/models/maskformer/feature_extraction_maskformer.py,sha256=MMPQuQY2EnK4vixDve-I-PIFqCDWQNYYeVdAYvIY8HY,1214
transformers/models/maskformer/image_processing_maskformer.py,sha256=rPNRsOu2wKNwnWPHs0f2WfS1cYgzG-jadsGPGYh0K98,57898
transformers/models/maskformer/modeling_maskformer.py,sha256=izrZsBuU_tZsJ4RQm_RXjRUwZ24nw7M2S_H2dVf7Egw,90752
transformers/models/maskformer/modeling_maskformer_swin.py,sha256=hpN1M4YYGMlwy_Ork1rJierKb2Z6mqWiOEqXaoTLaQc,42946
transformers/models/mbart/__init__.py,sha256=YjI533IlrLIES5x6U7S41gDRjPlGtpynh3xu96hIj1g,4231
transformers/models/mbart/configuration_mbart.py,sha256=t1QPTFbSv5_269wlbS6Fb0BnkSFj1jTnossAzVmj6Hg,18162
transformers/models/mbart/convert_mbart_original_checkpoint_to_pytorch.py,sha256=xVW9Mj-jd7X_MImJCgS52Aok1CGPf-E6u8ptvG1hK8o,3035
transformers/models/mbart/modeling_flax_mbart.py,sha256=mOHZTQHHSkceXpTfQCk0sTKJgLY8qAiPTi25z1nGGsw,75097
transformers/models/mbart/modeling_mbart.py,sha256=HSXOEaXZciVmAEN1wu1oaBsQqFYgg8HlySM8oc8EuX8,101734
transformers/models/mbart/modeling_tf_mbart.py,sha256=OEy5AgJLUrB2jTPMBIhDsw-MLuRelamEItyXStJayPs,74193
transformers/models/mbart/tokenization_mbart.py,sha256=cyxJpDRR-_GxBmUqaxwXzWC5SOmgvlSSIsDdtF8N8xo,14106
transformers/models/mbart/tokenization_mbart_fast.py,sha256=1ieIvKkfDtKZe_hHOaZNbSt6fzVPylKoYOtNI3T6rpw,10997
transformers/models/mbart50/__init__.py,sha256=5ekQCS9OkL3_5UJXnu7Z5cVeCi76pVgAxHkC8qQ8XKk,1847
transformers/models/mbart50/tokenization_mbart50.py,sha256=INTdGnO_YBeB7mWdpBgkz8PH-prQOKd1dP92qbBsKDE,16307
transformers/models/mbart50/tokenization_mbart50_fast.py,sha256=4XQPT5nXMLElCwfHfy4uTolWe2VmD1HcXdVJH0jQ3oA,11594
transformers/models/megatron_bert/__init__.py,sha256=syriQ2RlmrQuszQMRpmUaD_i2EK_TdlY4t_qdivZehU,2302
transformers/models/megatron_bert/configuration_megatron_bert.py,sha256=iewFczA_jEjpgax1zQ9T0SVF-sL0p3hFwtIHZQVm9M8,6466
transformers/models/megatron_bert/convert_megatron_bert_checkpoint.py,sha256=VAMD1MFdVG8w9cQkRfmlZCEvaMgoo-lyFI9deunD5OA,13686
transformers/models/megatron_bert/modeling_megatron_bert.py,sha256=azrF4kIFi4yyir-5UbcGIDVun9YViTadj866r0Z74XY,82504
transformers/models/megatron_gpt2/__init__.py,sha256=WycFl9cUevoXIBhB76qKtnNRIPMk2LoTDkmkfAfOy9M,630
transformers/models/megatron_gpt2/checkpoint_reshaping_and_interoperability.py,sha256=V7gxvReranfsvdIs9g0Q-mAEk5kFhW1AYX0Pvh69II8,37040
transformers/models/megatron_gpt2/convert_megatron_gpt2_checkpoint.py,sha256=UPLXCjF4Fixnw_gy6kzxTK64ioxo_EIxwSVO6oKCqqQ,13661
transformers/models/mgp_str/__init__.py,sha256=2wNKbfWxSFX5xHRL8pE6jWJ3oPFmHT30cCFp1iPS_v4,1984
transformers/models/mgp_str/configuration_mgp_str.py,sha256=UxvaYkn_jG5igokA3dk-QrtUyvATGbTvg6wzB8nYpRY,5781
transformers/models/mgp_str/modeling_mgp_str.py,sha256=dZovmq21WcD5FykXJnCUfiXf6CnfEG_NYRRO4YPRoY4,21924
transformers/models/mgp_str/processing_mgp_str.py,sha256=dh1MJ17yNZdoorG_Mi31Q7waqTnyRock-s4c2k_g0DQ,9298
transformers/models/mgp_str/tokenization_mgp_str.py,sha256=CIz9yrKh2VPsckVtYJ0pynFgPhwYY9XuyJasmKD9mKo,3776
transformers/models/mimi/__init__.py,sha256=utN7O_ZgC-5pFc_SRL-ToRKt29yjYUGe5ZmpZHLqUrw,1538
transformers/models/mimi/configuration_mimi.py,sha256=btFBzdvvP_fM5BHNyfuBvE4pw-RCwI1Ir6ue5Emmx2Q,11894
transformers/models/mimi/convert_mimi_checkpoint_to_pytorch.py,sha256=aeCILFdZTaapdCj-vUfwKf__fxCC9quZRj1j1LbaQz8,6797
transformers/models/mimi/modeling_mimi.py,sha256=xNPipaUiECxQFPu_aXolayvajOWvtRzfQLxDJFABuhU,80862
transformers/models/mistral/__init__.py,sha256=Gd3l8JZ-Oxe8fvqYKH1BW_GI4Pvc4sYy89_5h9hosFI,3248
transformers/models/mistral/configuration_mistral.py,sha256=S6dBidOaV0yR9LkW_qAHdLKulYU-MhIE3dxr848lLEM,7111
transformers/models/mistral/convert_mistral_weights_to_hf.py,sha256=d9o13aFoduxGYFQwvD5Gw-Ghfltq_555uS3gH1VQg48,11297
transformers/models/mistral/modeling_flax_mistral.py,sha256=M2Fio6tl63E0fATg077-CwLoT1iNV7pZWmquxNvOTAI,31682
transformers/models/mistral/modeling_mistral.py,sha256=32SixYy14x8ji6QGIJ9gEG7hA7NtlI8xlupaDaWd2zE,63810
transformers/models/mistral/modeling_tf_mistral.py,sha256=wEjTFfDOFp2z3-5Fb53D8XTKVsLfTaabBvvRXdSpjKo,45264
transformers/models/mixtral/__init__.py,sha256=K-r1Mh5wzRlbIfzTr9PSpvZqZPUwIgdXMCEBK7WOPn4,1890
transformers/models/mixtral/configuration_mixtral.py,sha256=wEpokmFxB_r_oGvnKZNZ3WInSy6fPBjcWKYC_pqiMR4,8106
transformers/models/mixtral/convert_mixtral_weights_to_hf.py,sha256=WExicalIwkZccqWyRjUU2LBvbL6cM6yiOG_Oby6t3Ok,9156
transformers/models/mixtral/modeling_mixtral.py,sha256=JiMZAHmF43eGd-qqoFIWEXjUVSGk3nSOjimsVCpQKDI,74630
transformers/models/mllama/__init__.py,sha256=mWrsviTkwlwVSsTTv2cmLPJSGbFRm20hjvbYvogeoqM,2330
transformers/models/mllama/configuration_mllama.py,sha256=KsETfL_0T7fvX-Hksgb5oLZaepPA1lrizMjl50qr82U,19611
transformers/models/mllama/convert_mllama_weights_to_hf.py,sha256=zAKItYBSJxC41OIX73O1bJFHosNxk4T0ZsfXhnrfZtI,29660
transformers/models/mllama/image_processing_mllama.py,sha256=Cf-RtZYVeOIlZZyaD7J465zUAA6Pq-p8wKIsQtP4_WM,39176
transformers/models/mllama/modeling_mllama.py,sha256=nvlORs5V_C5FA1aKQSTwbigLq18iK6uU9q2_YNcTou8,105136
transformers/models/mllama/processing_mllama.py,sha256=ZxZQGjMx-oqinD2nv55RJ0_1pMV3znV3fTHjLXsP7iU,15469
transformers/models/mluke/__init__.py,sha256=Pj0GBjIU6vYdhEzO7M8O35c5Jj4ivIIGAiLABhN4K7U,1356
transformers/models/mluke/convert_mluke_original_pytorch_checkpoint_to_pytorch.py,sha256=G6Z94-1_AiilSTU96PSjX_pdgFIx-b_bk8xlMKX5TuE,10185
transformers/models/mluke/tokenization_mluke.py,sha256=adjJd1GQmRqsslBJRMaLncDS72CTa9SB3EJuMDCG5Xg,82073
transformers/models/mobilebert/__init__.py,sha256=1GUULZMxdkLK0h0aPRB1MTzkWPqTd4AIls0lD44WOPE,4284
transformers/models/mobilebert/configuration_mobilebert.py,sha256=zfPgqqwx8_L5I0DHN0s-0B_SWT3LE28CbLy-ei2bhvw,8217
transformers/models/mobilebert/convert_mobilebert_original_tf_checkpoint_to_pytorch.py,sha256=MRW9sorswIo4RiWq7PVVmaZsYm4wJEc1-DhcLzssDRU,2200
transformers/models/mobilebert/modeling_mobilebert.py,sha256=A6zMrtP5lsWA7SiPNvmzv4B7Odzq9clbgnTq3ZmRTac,70581
transformers/models/mobilebert/modeling_tf_mobilebert.py,sha256=f5tdUOv2rwB7eFUqfdb_CZtU14bCoHK5mskp_2KN5ic,83717
transformers/models/mobilebert/tokenization_mobilebert.py,sha256=mCnz-SWkZOVK-kYiyyyHVV_kg8jZPNIwhlMW4KSZxHk,21268
transformers/models/mobilebert/tokenization_mobilebert_fast.py,sha256=Yp5FcPJNWWdqk41D6xDu44gN8OWd0jj104BdDbGaqdg,7798
transformers/models/mobilenet_v1/__init__.py,sha256=3W-E87nKaX8p2BLUS_7gJB29idu5-LYPBXdETzYe7a0,2519
transformers/models/mobilenet_v1/configuration_mobilenet_v1.py,sha256=6QZfS8Adb_wz-OOqaxjRtw2C5WhTp7TVdQFvKmBsLb8,4871
transformers/models/mobilenet_v1/convert_original_tf_checkpoint_to_pytorch.py,sha256=kT9UyoJ0ZfpG2-oVhG8TNs18R3cTgXUnK1CcC-57IYM,4931
transformers/models/mobilenet_v1/feature_extraction_mobilenet_v1.py,sha256=goR0AC-IhWMrQlvzSK_0Zej42JYN-oswSGNQWnIOENU,1222
transformers/models/mobilenet_v1/image_processing_mobilenet_v1.py,sha256=w2DIJdUboR-YJAnZmi_wnVAUzxwDQ45GhHOiN_hbUu8,15238
transformers/models/mobilenet_v1/modeling_mobilenet_v1.py,sha256=N9j5QZL-6akG4Rf4MJkzsMxziXPZQsvTRwrM2OvLiuI,18594
transformers/models/mobilenet_v2/__init__.py,sha256=lJEJ0CiUrI6abdoZwCY_RgSdL1pZqYvj-PQ7xINBHqI,2614
transformers/models/mobilenet_v2/configuration_mobilenet_v2.py,sha256=0JTuxeBnxwVuI_gZvTM8BMHiAAEq_58CXoV9882mnqw,6767
transformers/models/mobilenet_v2/convert_original_tf_checkpoint_to_pytorch.py,sha256=mJVhzYCMvutwkrULewE2GGsVOxOsW4wjjE8XzTWlWIk,6401
transformers/models/mobilenet_v2/feature_extraction_mobilenet_v2.py,sha256=_IUVvyoMBsqymCoh-CVmoswZ4nOBpqFJlaoUfD8WQ3E,1222
transformers/models/mobilenet_v2/image_processing_mobilenet_v2.py,sha256=ZLwsSuaDMoTEGKL2a7Rn3TwxL1mkolPwIn3Dww9wsIc,17592
transformers/models/mobilenet_v2/modeling_mobilenet_v2.py,sha256=ajJbZ5iT8hysB6-L5xNu8ZkEutYDwwt00-0JkX6SFeE,34481
transformers/models/mobilevit/__init__.py,sha256=28c3SWBGbR5MekJFDEjiAeiU5beF376M0gxHhI0vMvM,3194
transformers/models/mobilevit/configuration_mobilevit.py,sha256=0vrPrSgS3AgNixlQW9dE2mR3I3DMqROwlHSpJfyvf38,7532
transformers/models/mobilevit/convert_mlcvnets_to_pytorch.py,sha256=nphj4DM8G43EUUWhQIL6TYHWB855GmZI9KPk8mLXX8E,12401
transformers/models/mobilevit/feature_extraction_mobilevit.py,sha256=na2H01bKIhQsyCHayPaVase5HRGRmmO7zVDDuY76Uj0,1207
transformers/models/mobilevit/image_processing_mobilevit.py,sha256=AVWhplHYMszPY-x8pZKHFMDldi-_JgWPlaEbe-l8btU,21472
transformers/models/mobilevit/modeling_mobilevit.py,sha256=Mhp93dli1hspLXmb2qK4EcAh1x_FsFUyCs_p3rpIyXk,40130
transformers/models/mobilevit/modeling_tf_mobilevit.py,sha256=Ugu6rl5CD9JI2TgpNp6IWl6WE1eSgw21dND9CgK5uOQ,54676
transformers/models/mobilevitv2/__init__.py,sha256=48cbQxsCyX2oNyTy5-aYHIFf1O25lmbHgyYmjTJ0pCg,1899
transformers/models/mobilevitv2/configuration_mobilevitv2.py,sha256=BanCv_BeSMLx8d_Oj777JBqhljevKIeTJ8ZgmbeqMyU,7091
transformers/models/mobilevitv2/convert_mlcvnets_to_pytorch.py,sha256=zl8FGQnMDhoN5PgtZLtHvLWDFrL2wg6AauROmo7zicc,12681
transformers/models/mobilevitv2/modeling_mobilevitv2.py,sha256=fEJg_XTYQ2FEPRvUI9ILH3_IGPfYazVhyyfkFOyjZC4,38205
transformers/models/moshi/__init__.py,sha256=uW4oqTKZdbmURZaC_xwwHXnYEMyLJrMEJAlfbUzSWO8,991
transformers/models/moshi/configuration_moshi.py,sha256=CsOV2b60tgFgahf2eSyRcTHgcDzkEWNKIkqi6XBCmO8,16021
transformers/models/moshi/convert_moshi_transformers.py,sha256=42sbLhLNI2hWRWrBSJbFKH7DGHwjxoRn7DT1woaheio,11554
transformers/models/moshi/modeling_moshi.py,sha256=hV9jMwmhjGP5DtLUl_KdNVPiKHgrny5Z6FmGdW0AK5Y,134736
transformers/models/mpnet/__init__.py,sha256=ywHSRm-Qy-kCoL99sLHEOjHRhpO3JEFC9J2GwZaMDlQ,3601
transformers/models/mpnet/configuration_mpnet.py,sha256=pV8B4TLaMqc9yI6YaK4V_ZpXP9-ZaEtpRbbPTppBWxs,5299
transformers/models/mpnet/modeling_mpnet.py,sha256=h9xfUV2oSpZfBNN_iQM2k3MvgB5ToRgpCqd4Oexd8f4,42601
transformers/models/mpnet/modeling_tf_mpnet.py,sha256=dHLMAG_b7V-s88gl4nQZBxtf2mS1DOnAbj8W6h_ylY4,55463
transformers/models/mpnet/tokenization_mpnet.py,sha256=5q7N67qxsmaktZXeNVaOIBHDpttPN8E2JE0iAfFTdtY,22444
transformers/models/mpnet/tokenization_mpnet_fast.py,sha256=DUx1wGlRfhxppFPHfvT68EFY9MBLdcyzhBebBdcT5lQ,9158
transformers/models/mpt/__init__.py,sha256=meTcujvVHkqcgK-Ppw4UA8LO7-n8m9AmkZQ6z0deoSI,1813
transformers/models/mpt/configuration_mpt.py,sha256=a2arJuhKMsjPVf8m6SD3cYdI1KZF0rWUyqiPPTV7bkw,11233
transformers/models/mpt/modeling_mpt.py,sha256=zeRM2P1MEykMkMwmG3Rk0Vaqggt0YXdqrKXA4NFRPUQ,39487
transformers/models/mra/__init__.py,sha256=lDPumVYp9ej4hsyIVltTxw6UbaouFH_aMwkk7Ufoz7U,2090
transformers/models/mra/configuration_mra.py,sha256=6Re8q99pn6otoT8q3mYcISJHhVhX_mpeLvIu7N-d4Ac,6510
transformers/models/mra/convert_mra_pytorch_to_pytorch.py,sha256=LhaVlQ4q88gtewg-geRYZ748xQ3brLLhyDIo-OGWSdI,4247
transformers/models/mra/modeling_mra.py,sha256=psubuZxkjXzgNvt4SU2Yav0x8wAG1NoHVYVJedAV64E,61975
transformers/models/mt5/__init__.py,sha256=q5f0AWvlyU1eQjk0OXCpMZ4OM3qNDq35Pv6RuxrWQeI,3597
transformers/models/mt5/configuration_mt5.py,sha256=MEeCxAZVZYvIJY-NCyIZo_kYMSrTvxgQrj74FkdqWlE,7959
transformers/models/mt5/modeling_flax_mt5.py,sha256=nbooo3tWzSe943NzLlZC7r4Sfk0oV5WBN0aN7TxcKXk,4242
transformers/models/mt5/modeling_mt5.py,sha256=DcWFDBoibdpM_w7qhrgzXgEedp2XUBTEQ1kJWlEU6sk,119254
transformers/models/mt5/modeling_tf_mt5.py,sha256=NTS87VyUcufilfS17a2VN4uCx0dvYswLBNATv6gBRsQ,3325
transformers/models/musicgen/__init__.py,sha256=_B93S_nGM8Syv750EV9xwR0ACv9GPAB-G-Uwsox0TLM,1899
transformers/models/musicgen/configuration_musicgen.py,sha256=jyGcy9lsgRqV9ExtSgVG-y-5QOQZBi3yiJcAnbOm5s4,10640
transformers/models/musicgen/convert_musicgen_transformers.py,sha256=ky4nceHZ-78GAp0L4vY-cFXsZ6lGvkYzX9k8MGUZ2kQ,9384
transformers/models/musicgen/modeling_musicgen.py,sha256=yqMGdBzo9xcL5_yypbS_Ko7GpRngCqTuTG3bbhuWE0Y,136018
transformers/models/musicgen/processing_musicgen.py,sha256=A4008UqJ3Wm4nixG-GIR6b5Ul1ZcV2Nnq4ZB22VsMDQ,5667
transformers/models/musicgen_melody/__init__.py,sha256=v3FVLsoE2TEh_eAaYKcb8v114HPo9RZN-p5TSS4eD_I,2594
transformers/models/musicgen_melody/configuration_musicgen_melody.py,sha256=nQVnr1IaTK17GMlPsRt3c0KvZ6CNIVQBgfKIIIFVuLg,11745
transformers/models/musicgen_melody/convert_musicgen_melody_transformers.py,sha256=uaWtwMCLmSCXc7c0PK9TinKJeBsGjDfRwmXVHH_Mxig,11356
transformers/models/musicgen_melody/feature_extraction_musicgen_melody.py,sha256=0-gMjuGhG4JMeM-44wa3aTo3Nph-_cjZs7k3nhc6cfE,15227
transformers/models/musicgen_melody/modeling_musicgen_melody.py,sha256=haYMS7uLnokbBCtttjxmd7XxENmLbWMY0v5Qz5Widwk,129643
transformers/models/musicgen_melody/processing_musicgen_melody.py,sha256=W2dEpemPPhX10YgTwm0T3zvy71Z8QxJ_LSoz6Z00UAI,8634
transformers/models/mvp/__init__.py,sha256=GjxZ0S34sqQUeirY-0gnhXgr9R8r2oUV4kIg7CjrwGQ,2372
transformers/models/mvp/configuration_mvp.py,sha256=Q8sbJx-7VBNufbcWUTOzdcp4ylzXemiOEwaf2S2VNQs,8409
transformers/models/mvp/modeling_mvp.py,sha256=KeKnsipbLpeN0a9ar1gxiitE5lNY0kA7jUIBi5BCpS0,90258
transformers/models/mvp/tokenization_mvp.py,sha256=JlR6l5XT5U4eU_20FRb69tmEXvexJa1d88moJ3jxj3E,16192
transformers/models/mvp/tokenization_mvp_fast.py,sha256=Pa8ZaGtDrfhrWLnB9FPsO2OGU131E1l5HEAS25Nv6bc,12268
transformers/models/myt5/__init__.py,sha256=oqrGQ5GvHvGjF66Q-d4IQepsJa-aEjKgYI2I4s9-FM8,943
transformers/models/myt5/convert_myt5_original_tf_checkpoint_to_pytorch.py,sha256=m_BWHivhSbIAP43xOVX82eNxd1z8uOElA17OSs77u08,2240
transformers/models/myt5/tokenization_myt5.py,sha256=Qjj_Kh0IRF5Fsa04gx0KRty3hi1manEQKZFFkXz_8Rc,15525
transformers/models/nemotron/__init__.py,sha256=noDZ8A2YAVe7E0_Ij4ym24WcPsWNv3uftfmB0U_sGgU,1988
transformers/models/nemotron/configuration_nemotron.py,sha256=9-M6a5-ra-UVSk9f6G_qLC3bSh3zxoM20rYoH8rC3aA,7362
transformers/models/nemotron/convert_nemotron_nemo_to_hf.py,sha256=Bd9yShFuis_LrphDApfrfU_mc6S8OiLwclR083GoXr4,15645
transformers/models/nemotron/modeling_nemotron.py,sha256=Bg69xNmfyPqClCffvji-im7Gaq4-bEYczaTJjOnUuj0,63617
transformers/models/nllb/__init__.py,sha256=tM7_FdmE7zOQm68GoRQiRt1jbYfPea9kC24QJSSMgIE,1868
transformers/models/nllb/tokenization_nllb.py,sha256=MOHPuxJpEvUpzuUntOced_qH77DGWlYTY59GF2TQwN8,19065
transformers/models/nllb/tokenization_nllb_fast.py,sha256=H-ohJEWhMSGNmGqaKdvRK_feqkxrekNVaEbeiu08KH8,15940
transformers/models/nllb_moe/__init__.py,sha256=BjASPLRDJ-mU8cNXB-T9KzLyqeqoYEqinlKYrjUyt5o,1757
transformers/models/nllb_moe/configuration_nllb_moe.py,sha256=bys0xdiIRKz8W6NXWmgUOh4V9lcet_3KFZfN-cxjP-4,11168
transformers/models/nllb_moe/convert_nllb_moe_sharded_original_checkpoint_to_pytorch.py,sha256=c9Zab9qVzNESk0U2exJNaoDwUQo_Q7ZpcZHViZjqTQQ,6477
transformers/models/nllb_moe/modeling_nllb_moe.py,sha256=o1rXm6HXneJT2oWqBARlB4gzkB6CzIS_VrNs8Halre4,84434
transformers/models/nougat/__init__.py,sha256=2cSw40yf-T81USela2GvWs-NSXWHkOa6zJ_3BO7QSCY,1914
transformers/models/nougat/convert_nougat_to_hf.py,sha256=3KHG9mTikCDX88hKbRB8_aVQzKdlheP1_TiOydwQoIw,10949
transformers/models/nougat/image_processing_nougat.py,sha256=KUkCV72V6i0BhVxsUVj3z1NXEr6ZMd8uZg_SPGqP1Sg,23702
transformers/models/nougat/processing_nougat.py,sha256=65OZ7-XvFeiEwFjEi69ZDY931w6NvHTHGo9EixCVxKU,6731
transformers/models/nougat/tokenization_nougat_fast.py,sha256=gPZ7-ekhASu3KKPoPUOqrvxL5y7bYDRVnTNOuOgcwFU,24703
transformers/models/nystromformer/__init__.py,sha256=qp6ceFtyZJeQie59gdO4cMoIX8yQhxdzuqkm7H9iVmg,2133
transformers/models/nystromformer/configuration_nystromformer.py,sha256=PSwSfYNJRM_svcUtyQStJma5Ub7Uvt5SxtqFsE7DcaM,6366
transformers/models/nystromformer/convert_nystromformer_original_pytorch_checkpoint_to_pytorch.py,sha256=8K5IGFosME-LAljFLuTc09oce1IwxZDcxw1KPHsamqc,4197
transformers/models/nystromformer/modeling_nystromformer.py,sha256=NU1olBZgN_kTzOVO8-Pcx74GeF7OWcwLgzIa6cgjdg0,48758
transformers/models/olmo/__init__.py,sha256=_dNlQLxAlwk4Yt9djxtrLXy90ben8LUx4LtD8wZR5hU,1658
transformers/models/olmo/configuration_olmo.py,sha256=qqY8AF6TOEYKn6q-KKKuFB9WE_I6649dyvWBp-FAPh4,8810
transformers/models/olmo/convert_olmo_weights_to_hf.py,sha256=SI91Kn_B_m0oel2kuJ2LUMGqfaNZL4Q4sT2ydqNYZlE,9413
transformers/models/olmo/modeling_olmo.py,sha256=S07wgrsnTL9GMSqzgN2Cv3QeJjuZ_d_oVSQhPyADf4M,53272
transformers/models/olmoe/__init__.py,sha256=BRIovaEpquoYKrCwX-KYEzOlsD5Q7i1syI8HC5nClMM,1527
transformers/models/olmoe/configuration_olmoe.py,sha256=171WfPIuDQcI5Nuoysh9-TRSpwQkr2HrTng7ue6W4WA,9036
transformers/models/olmoe/convert_olmoe_weights_to_hf.py,sha256=vD0pnyDVd7oyF7nIqdWDa5MJZQzgUwtH29NaQSnri_g,13029
transformers/models/olmoe/modeling_olmoe.py,sha256=ym-_2BtVmoMOuJEdqs9SXkUd6rpxIBbXPzzT7fZnspc,61895
transformers/models/omdet_turbo/__init__.py,sha256=KaM_jC5f6IMaZS6hHNw5g8Ha7zEoIlU1Bqw4Anw_V14,1748
transformers/models/omdet_turbo/configuration_omdet_turbo.py,sha256=MjOiBmDw-vsuS0tblpdLj-hW8ZcQmT7yAwVBZdqe0fg,14446
transformers/models/omdet_turbo/convert_omdet_turbo_to_hf.py,sha256=yedQ9B-dADbQo_ZFXLb9nhAGC4SuW6424wn_rqG2wWk,17553
transformers/models/omdet_turbo/modeling_omdet_turbo.py,sha256=wPFjDqAY11m4HVXBDcRrmeD3avVKJirxUeGG8FejzCk,81043
transformers/models/omdet_turbo/processing_omdet_turbo.py,sha256=0jYn25HrGLXnChc1RKcbgWPH4nCjMqGpJ2J8ZSBiiHg,15465
transformers/models/oneformer/__init__.py,sha256=1HGB0SzVFLYt8u0pwJgEan3swBlh0TFVns6ZlcD3kP0,2214
transformers/models/oneformer/configuration_oneformer.py,sha256=IRKqjxjrS3yRjnGL6QDzz6F7B3zBmgkLawrvRkc7gF8,13435
transformers/models/oneformer/convert_to_hf_oneformer.py,sha256=yBWS0SE1sGS9UqCzX2EdbhAiIWvBCumSBwutJ8VQFF4,50691
transformers/models/oneformer/image_processing_oneformer.py,sha256=ao3PUvQv1BO05bZKJ8_QhOK823VUTK3ePmz8h6SyqFM,61226
transformers/models/oneformer/modeling_oneformer.py,sha256=mHGS1BYkNUawo0ZLwwFOU8Q9GCvZ43EiN355SH68w5I,143559
transformers/models/oneformer/processing_oneformer.py,sha256=ahso8fGMLGb078QfY8T5o1bDj5OaptoMbIxiTIJGM7c,9377
transformers/models/openai/__init__.py,sha256=OIO2mLuPtZemDpjaDro864qLEfR3dpHRBm7C96gA8bE,3354
transformers/models/openai/configuration_openai.py,sha256=a2yPfNdQ-cQxCpnWgJ5SykpLZ6aS5D-1BWcfUWoO10g,7077
transformers/models/openai/convert_openai_original_tf_checkpoint_to_pytorch.py,sha256=WVyibwB1gaKbNqJubia_mbh-N8Qy4a77W7XAroTw0yA,2665
transformers/models/openai/modeling_openai.py,sha256=oDjRfhsi-rD-F7p4hOr9CyuJciyu25wYGQF8uhOZ5io,38364
transformers/models/openai/modeling_tf_openai.py,sha256=jrIefNuYXxKJhQm1jwDHWOHHguugSTd5MDZizTvyeZk,41056
transformers/models/openai/tokenization_openai.py,sha256=oycr4NX6120g2DC8DGuoY_6o9yd_ESXHWkjwwMRNxNk,15152
transformers/models/openai/tokenization_openai_fast.py,sha256=SBwvthsGHgtEOs3nnAVuQ0KIlkDzD7WG9oXrA1hYtHg,2521
transformers/models/opt/__init__.py,sha256=_dgUsKmKmgcZ_Nz2395ycZOeHLCSHwpofYpYT8RsZKY,2813
transformers/models/opt/configuration_opt.py,sha256=ePwJEkN9jNy45Q3iFMiR9TMzNTNX-3PYorRhTbf1KKk,6660
transformers/models/opt/convert_opt_original_pytorch_checkpoint_to_pytorch.py,sha256=NIAQgoFpeWZyZCQ6zN4EylZdojsIQm2wtvOIuMZKj64,3857
transformers/models/opt/modeling_flax_opt.py,sha256=EYnpmtEQrhf6vLT4JyyWd8V76NzKxNiFn0Mw22gxXeY,31540
transformers/models/opt/modeling_opt.py,sha256=yxqKUU6XI_2bN27IMIq-HG2oUk8ulBmb1hHfOr60LP0,69802
transformers/models/opt/modeling_tf_opt.py,sha256=mYrf_6x5FAZ4HCyP1cdCvTg71H2lkzPAnudp_FdKXMc,49552
transformers/models/owlv2/__init__.py,sha256=YqVTBiltWupkgF5jfNjjVzeo8AhMXFYed6EpgWNseXk,2418
transformers/models/owlv2/configuration_owlv2.py,sha256=Zs25z_nUwnpgSP4FssJgRzd65UQJ1_lGG6fiZaHZHy8,15470
transformers/models/owlv2/convert_owlv2_to_hf.py,sha256=rF02k9XWTswf4P4ZZ76ekB3be6pRsFJLtbuWaJpyx3Y,22018
transformers/models/owlv2/image_processing_owlv2.py,sha256=8VspQth2_QDaB-SsJ3OQEcEbAE8jz0UfiMclzo7wCH4,26798
transformers/models/owlv2/modeling_owlv2.py,sha256=Osx8TRSbau9LzOpmSMI9frd2a7PT6x-mPLVygVpQwqk,80252
transformers/models/owlv2/processing_owlv2.py,sha256=WUAZC5nLIqVLseH1odt8F32mHZV2R2iaGe1eWq-9dMY,10046
transformers/models/owlvit/__init__.py,sha256=kT4pypCGo36tZhYV4moH0R0FgqeHuFH4BAq-kc6_PDg,2723
transformers/models/owlvit/configuration_owlvit.py,sha256=k18t7qjhCJ4PyiwrjRLT6Lh1NbIPj48PyZGsZ-z9hqs,16660
transformers/models/owlvit/convert_owlvit_original_flax_to_hf.py,sha256=tofzNZcVROwfYoV7pV6u50Am3TFm-XmuJEAGwNvRT9o,13988
transformers/models/owlvit/feature_extraction_owlvit.py,sha256=yPO8FbUw3YabKbsV_ozKpIr6JixO9knVw1eMIHeiCtY,1186
transformers/models/owlvit/image_processing_owlvit.py,sha256=z3RTEGy5ZxEoFkd5_y4Yr4YwbsDVPM4q5qGlDFl7eXA,28423
transformers/models/owlvit/modeling_owlvit.py,sha256=WLrEQ4wHaui_ZdguIZq2G9un8zn1VafXgZRztY77CJw,75689
transformers/models/owlvit/processing_owlvit.py,sha256=0nSZZV8HtYmywaCfUqMCWYadqAO3QtMi8S-Jt_y8ai0,11042
transformers/models/paligemma/__init__.py,sha256=ZIdi9pt3rI2ONhbv1Rix7e66R94-K3aST57W4GPGAJ0,1698
transformers/models/paligemma/configuration_paligemma.py,sha256=cA3ZB8N5KvPemYeWwJjN7ywGSjaR6w4tb0gAFXYmYXA,5917
transformers/models/paligemma/convert_paligemma_weights_to_hf.py,sha256=lRp8Fi7CwaeuySEoRWlcdCAt2QgPJ5cIjoBbm8mUbbk,16896
transformers/models/paligemma/modeling_paligemma.py,sha256=fhqi6q9z2ck_IGSE_dczOT0ph-jEOge3NFrK82lJVzY,30694
transformers/models/paligemma/processing_paligemma.py,sha256=C1--_iuIYiwHaMNVieGqouuXAC0xcBRCcFcAMp2m-CQ,15504
transformers/models/patchtsmixer/__init__.py,sha256=lrY8Epa2mGlbzcJUxAdLW72aCHKl17PzJzMTaCSVvU4,1973
transformers/models/patchtsmixer/configuration_patchtsmixer.py,sha256=M1SuKLfqjuXcBSFB8gcvi_owPZkfW1xpkn_nt3E6DkM,12531
transformers/models/patchtsmixer/modeling_patchtsmixer.py,sha256=lAEgVqjueJKb59Tg6iHyEV60AjG9OM56w1_7gl3nHtA,87695
transformers/models/patchtst/__init__.py,sha256=a7SZ6h8LEpH8FTberrHpDnWQTpkDDra342dbj9EfKYk,1864
transformers/models/patchtst/configuration_patchtst.py,sha256=1naWgHGmNeEvBDfHmouAZeNdb0sQ-7taUXPLSW6fX2g,12284
transformers/models/patchtst/modeling_patchtst.py,sha256=rxFpaGipKkKcmSR3XbXSPpdSJ1Ctg09GMl4kdxmTDfI,91725
transformers/models/pegasus/__init__.py,sha256=j7jvv7ORuLEB9kCOedIadaT-NaSFS0M1-mFFjK7InL8,3931
transformers/models/pegasus/configuration_pegasus.py,sha256=vtVKqwlk0hgnPlYWuUn4sylNykdIBptmjePd-e5owxk,7471
transformers/models/pegasus/convert_pegasus_tf_to_pytorch.py,sha256=9geJowNAukZc9FE2OEq0pXQi6ynw9k-2NFtlmISxpUg,5359
transformers/models/pegasus/modeling_flax_pegasus.py,sha256=L12HwH0GbiOY4lrJXXrKj8knEaX2B3EvAZe1ZK8oIAs,65972
transformers/models/pegasus/modeling_pegasus.py,sha256=DsF-gYvsSDWjB37vaszG-_9M21gKXnQVKGnrmXiEqe0,78106
transformers/models/pegasus/modeling_tf_pegasus.py,sha256=3Y-rYRgfpKlDckfmVkwZu8tcnak6Xl5PeNrzSIE0uys,74200
transformers/models/pegasus/tokenization_pegasus.py,sha256=zRyVOMqZunsKvEp0Hq4ZIdP8fwlMASOB_bTKk_TNaPg,13125
transformers/models/pegasus/tokenization_pegasus_fast.py,sha256=7gt2LpNZCzqprN0pwxmYis0d1LBKjqgb2O_l3QWCkbk,9940
transformers/models/pegasus_x/__init__.py,sha256=EJwH0GJ5O6nR5iWGcB6kZfh4tluB0S7DTHAHXzmW6Z4,1640
transformers/models/pegasus_x/configuration_pegasus_x.py,sha256=4aDNEccBs7KEXmS6LxvEYMsSh0CEnjltLa_E7i9quxU,8085
transformers/models/pegasus_x/modeling_pegasus_x.py,sha256=s0fPwVRTfctcFe9SShtcafV_gTnpOB8_FamOGIzViq0,75526
transformers/models/perceiver/__init__.py,sha256=l4oTV7LUBhR5m0sY7ifLdjbj4Z3wZqbxtOfsHPSrUi4,3105
transformers/models/perceiver/configuration_perceiver.py,sha256=4OevU9EjFFptCRygu9-1eVC76YXY_PSLNKyd8FHgKu8,12154
transformers/models/perceiver/convert_perceiver_haiku_to_pytorch.py,sha256=r9nb8tWR170S786U85dagjFFbgwL37vmpdOqU-DPenQ,21285
transformers/models/perceiver/feature_extraction_perceiver.py,sha256=0lW_qh5ONtUwr0ARM9RB9hizA76wL6fmeofDrhbIsXI,1207
transformers/models/perceiver/image_processing_perceiver.py,sha256=XKzXXXOVfvkzB-Cfk2z7dgMBWXoep1DJoGpKFxUEOGA,17448
transformers/models/perceiver/modeling_perceiver.py,sha256=DAuGs7yg66MIpTgAfyA_sADLhG6kIEyNRtCq71xgtYY,148918
transformers/models/perceiver/tokenization_perceiver.py,sha256=ghYlQHviP1Pjdxuin3oR1VkcH5Ov9wHI8DEodLQHI2w,8018
transformers/models/persimmon/__init__.py,sha256=xrG3P_nWFWNov6mzHxKyRE4M7zZsXu__4h948LOV5_M,1839
transformers/models/persimmon/configuration_persimmon.py,sha256=iCJh2bW9i3JBSZ-H_7l0wVx7NAahOp_4rl39te1Mv6k,9117
transformers/models/persimmon/convert_persimmon_weights_to_hf.py,sha256=F3NFcbCWD-UxFwgp2h-Nv78_M0p0LELPq4re30ZNIjU,4644
transformers/models/persimmon/modeling_persimmon.py,sha256=ac7OftgjJfN3DNT-o7nMcIb0GXG80d5rDhkwL4jCLIk,54342
transformers/models/phi/__init__.py,sha256=qMWyJRn1PnnyX647VO4xrJbR7hlTiwvtEkyQVDEKHxw,1807
transformers/models/phi/configuration_phi.py,sha256=HdVVz0WMZD_zYgffGZimHreVvcdSCgmVEvUMf5Nbg_g,10537
transformers/models/phi/convert_phi_weights_to_hf.py,sha256=XrjgtZm6GZQx01rZ0q52g6e4ajyZhl8n02QNchAD6BQ,7685
transformers/models/phi/modeling_phi.py,sha256=xgX8tYwqU0cB5zq7Ec6Z75JxaSQkLlQCM1T0SyN4ERk,67457
transformers/models/phi3/__init__.py,sha256=NdSAgnhYHRU5kcPOw8sulyJozgiAP1MIfPfCLj1sypU,1823
transformers/models/phi3/configuration_phi3.py,sha256=qh8ZzVWfLIzP6RpVTuUuRFn1I2lI0Y5QXAWAf4dl9Oo,10609
transformers/models/phi3/modeling_phi3.py,sha256=X-BUQRVb3ShfP9Y9ZCM_DMylvqrixgsnhjJ47_-sbmg,71295
transformers/models/phimoe/__init__.py,sha256=wGasPysu0EH_q0QGaZmXqQL57GxfZn8NTsvB2I6U2ro,1013
transformers/models/phimoe/configuration_phimoe.py,sha256=ChXe5y5wBQvk4nbo9m4cqLp_n8xlhzpB6ZG6ru71cN0,10273
transformers/models/phimoe/modeling_phimoe.py,sha256=gHhKLVLXw36DgjFXVsOKAKCeeqe4sCZvSkP8Zj86aP0,73144
transformers/models/phobert/__init__.py,sha256=JDAAoG6FOpN1o5kgFBbHkoko9NsiioFi-ZAeAgR79nY,955
transformers/models/phobert/tokenization_phobert.py,sha256=nAFBv2fjPBCZ1r8z1Cle5WrhOeTGyvykavOim8Hat70,13091
transformers/models/pix2struct/__init__.py,sha256=XWzS791vtHNvjfyvFmES5k4t3017GzvIN_6GPwG2_Xw,2493
transformers/models/pix2struct/configuration_pix2struct.py,sha256=EYHO8znXYMwxsJFEA5aVdsB5C6-CD6vHpn0JXo4pzK0,17288
transformers/models/pix2struct/convert_pix2struct_original_pytorch_to_hf.py,sha256=m_S-9oxyN4PQafRbWQIP-G0NUDrTqxOmr8IwiHNCOuU,5886
transformers/models/pix2struct/image_processing_pix2struct.py,sha256=JGjJuxIAYDk-02MYLT2ZZIk4HW095Wdzmdi96yQHoyE,19728
transformers/models/pix2struct/modeling_pix2struct.py,sha256=hQsHnlUzQow3-V2cZQAMW7HjXzOb8Mva-9eLlfok8eA,88777
transformers/models/pix2struct/processing_pix2struct.py,sha256=n9ngZMecIha5k15JvhYDlFK4XTSuU7I9ZQHiUSv2R4I,5815
transformers/models/pixtral/__init__.py,sha256=V54hVnC8xyX72bnz7Fg8D_mqMtzZZ0dGFOYTszNJWjk,2112
transformers/models/pixtral/configuration_pixtral.py,sha256=2-9I_PHJPEUtGoOEVh1ARxyE9kG6ZkPC2CaHudkJfLg,4201
transformers/models/pixtral/convert_pixtral_weights_to_hf.py,sha256=51d9ms_n_S2RND729ZjmyjVvNg1iCD2_g-u6AejnpRY,11326
transformers/models/pixtral/image_processing_pixtral.py,sha256=9f2gLOV1XFjS1FDSkVj_BEQg3HIyLiRAK2uODZfb8N4,23776
transformers/models/pixtral/modeling_pixtral.py,sha256=vHpnJqX2z9x5mGzttbYzdbYczcgH5Ovl-4JCkDRlhO8,21977
transformers/models/pixtral/processing_pixtral.py,sha256=fEi9Np0unrsazn5OsUR4aj2RrJYTam7aafzkwsJqCIA,13117
transformers/models/plbart/__init__.py,sha256=yXgEZtshXavQ8--GOfh3sRPoYU8US_qdRxia8FhJ4H0,2253
transformers/models/plbart/configuration_plbart.py,sha256=C6cS2yNel6oJy4U9y682ZAmcDqcC2IGeZj3i5Kj_PIo,8503
transformers/models/plbart/convert_plbart_original_checkpoint_to_torch.py,sha256=BOXNudNSr1xevmHnvNpa_4ya3Q89m6J4lndQhCWSLB8,3553
transformers/models/plbart/modeling_plbart.py,sha256=7avEJ6Gw7Z4XhDkRLhz8A4i1RiXeCXn-D1nohriF990,82322
transformers/models/plbart/tokenization_plbart.py,sha256=wKnsSLWw4EhjfqpEXd3PXT-EkCjIcbVvsfqlW9gdjmU,18860
transformers/models/poolformer/__init__.py,sha256=EL5af3hvwpyXCzs_rNTnADz448Naku5CRiLaE-qWW94,2378
transformers/models/poolformer/configuration_poolformer.py,sha256=4AgFouKSNFulZLh-0Wbgdd4bHV8y4Jlfh29HbFnb6Ys,5575
transformers/models/poolformer/convert_poolformer_original_to_pytorch.py,sha256=Vvlp7ju7kr2sg1NdXKma6vYGABjs4sVhPKhgFKPJRpk,7947
transformers/models/poolformer/feature_extraction_poolformer.py,sha256=KDL4tg7hxwzQKYmGc6jMZfzeD9UCTb00oNfbejIjzmk,1214
transformers/models/poolformer/image_processing_poolformer.py,sha256=ptXH3CGQk8lOFINhO9kN3hAVx3VWdPwb7_bcgTQTdb4,17809
transformers/models/poolformer/modeling_poolformer.py,sha256=Nzw2Ao80Jz9q8XQzqWgdOubHCV8kmsxlbk1mZXpmn1M,17780
transformers/models/pop2piano/__init__.py,sha256=6mtBPEtxQ047_pZaH3xnfbRiry7VRzw-GkjTy6u-aTg,3631
transformers/models/pop2piano/configuration_pop2piano.py,sha256=CSk7dDvdmcNog_5xwUIbSgcSW_ZOy5bAMTPt4h2H3mo,5927
transformers/models/pop2piano/convert_pop2piano_weights_to_hf.py,sha256=5B4ARCF8lCWeW2fsgNe0lgy5nmYvLmNPQFyg5O3kj-A,8624
transformers/models/pop2piano/feature_extraction_pop2piano.py,sha256=eHyA7sjP-tkY4QXHTGYl83RCnCEoNgcQ9Tf6rSd9Pb8,19838
transformers/models/pop2piano/modeling_pop2piano.py,sha256=hvCK665NReg-oxu5WpWkxwIOdNaa_L1YSancNI0Y1i4,72001
transformers/models/pop2piano/processing_pop2piano.py,sha256=QmobKR_Z3Ro_6t12TXMaileqUH1lAjGVY6n2wOevzwY,5524
transformers/models/pop2piano/tokenization_pop2piano.py,sha256=Y3grUs2_4YvgUDxDAhe4hBBJe0RyAZq_ofx11jw1M5A,32677
transformers/models/prophetnet/__init__.py,sha256=5w4pH7Xx3gp9dCSy4OWgfoR66vUEGXilZ2VMnNMNHas,1965
transformers/models/prophetnet/configuration_prophetnet.py,sha256=dSnqTxvccVfqBf2HtCA8e-m0nJ9q2i8gRXynaZmlvwA,8870
transformers/models/prophetnet/convert_prophetnet_original_pytorch_checkpoint_to_pytorch.py,sha256=NsV4OQ2M5Qmg-RzBOhW588PP2zMJADXwcE1vFIA9FPE,7054
transformers/models/prophetnet/modeling_prophetnet.py,sha256=S_HDFda61rThjKLGgaUY_8YOHYBvVYqoG3bMT5p2Tic,114497
transformers/models/prophetnet/tokenization_prophetnet.py,sha256=JyUIsrQsbrSS1rM63Vfi_CIYiyUnI4s-VJVT8jv6QcA,21200
transformers/models/pvt/__init__.py,sha256=ncmEQQxrm_BMJIy2avwU_aL4OEHetwJVa3jUzn18X9c,2220
transformers/models/pvt/configuration_pvt.py,sha256=xrEwEjEP775TfGbVVs3OhEI-PwHbbjHsLHTSgSoT00o,6919
transformers/models/pvt/convert_pvt_to_pytorch.py,sha256=BLoYbECmvvKnWQQqMjM3zlm8lMjYc6L8xrcYwionges,9737
transformers/models/pvt/image_processing_pvt.py,sha256=hq-5dHAf-teOqRgy_oCJEtlDv_n2Y8zWoFYGmmYtFPc,13830
transformers/models/pvt/modeling_pvt.py,sha256=YKN_yAXtCUbTmBoBdc35jiBh-jG2aUqwm0OBMXt3IdU,28415
transformers/models/pvt_v2/__init__.py,sha256=juUzRcgqzQAI5MHUbyhwB3lIeQeTk05FR9n3YQFWAQo,1832
transformers/models/pvt_v2/configuration_pvt_v2.py,sha256=-vu-7Yg-hVJ6S5FSyqKTqX8M7w0cDO_S8k2UEKYQy6c,7963
transformers/models/pvt_v2/convert_pvt_v2_to_pytorch.py,sha256=OqYTYB1bssEh4C-AwCFG0VDDcEWZa1Su5kUkrn_UcOo,12077
transformers/models/pvt_v2/modeling_pvt_v2.py,sha256=iQL_48n_xGDak3-7A2TDLixYEa8t-HCdzLkuBhVnlrw,29417
transformers/models/qwen2/__init__.py,sha256=qoTTnT8A-pEg5kXdtnX0NgkIszex-35xul2PvJ3ab48,2434
transformers/models/qwen2/configuration_qwen2.py,sha256=pNpzWrV1w_c6UOX0k-NV0asnH_Pyf6s4QpT2zZkuoD8,10100
transformers/models/qwen2/modeling_qwen2.py,sha256=RWjViLRd0oFS2Xw3MApeInWJvHC5_vUiSvKZd-z2q9w,69662
transformers/models/qwen2/tokenization_qwen2.py,sha256=y9hRJ6oYYRa_4UyoQUPU_BlsrnTPKoEByiCQ3zelSmE,13913
transformers/models/qwen2/tokenization_qwen2_fast.py,sha256=NL0QjEs36hiJUo0yu6X3-kp74LAjioKyoJeqnxhdsY8,5182
transformers/models/qwen2_audio/__init__.py,sha256=IteACA3V6E9ReJWvLmnZTiofUE36DBDVvsX2b3YV8OY,1813
transformers/models/qwen2_audio/configuration_qwen2_audio.py,sha256=DEtdxOwELmbuiY5-GGrxVdoQtoIWUM3C7l3FzqUBTPI,8530
transformers/models/qwen2_audio/modeling_qwen2_audio.py,sha256=Rq9ZxMdxCkN_LtGXxvcg1KExkel_pbV7yniUy9z8ROU,68722
transformers/models/qwen2_audio/processing_qwen2_audio.py,sha256=e8YnvvXIlIblndyiGUWJxSbNYeBzECzPLtbPPjrfIbM,8633
transformers/models/qwen2_moe/__init__.py,sha256=TTWQj9UkujwmMLt8cDgW8ovgH0UprUnoaDNLvd_9dxw,1915
transformers/models/qwen2_moe/configuration_qwen2_moe.py,sha256=wTpIX9UW6xpYa1ESMExEKApx5b6KPv73z4UwLtsrdio,12317
transformers/models/qwen2_moe/modeling_qwen2_moe.py,sha256=y-UZy6eDpPPykJOWxmaHtwOh-Ix-xPwxkJY5q8o4I5E,78417
transformers/models/qwen2_vl/__init__.py,sha256=rFF8KxYp3na3fOz7WVMQ_X1MOlJLCf5dp9_V-gUEIG0,2208
transformers/models/qwen2_vl/configuration_qwen2_vl.py,sha256=v0OKxHbuePbDICZ8lb5HNSOiVX-HobasNNhsnU9HpM0,12492
transformers/models/qwen2_vl/image_processing_qwen2_vl.py,sha256=8cCH8-oZAwXGKkBfZBW_7nXYRk6NItn9ilC0TlEu_gc,22249
transformers/models/qwen2_vl/modeling_qwen2_vl.py,sha256=EKNiKzKC5vghuRtfQrzBTHZuxOKeo9wBhPxlkAXoJ_A,88525
transformers/models/qwen2_vl/processing_qwen2_vl.py,sha256=vQumH8BIVTWBPw7FWIln8LkVn1sea13-Bsm-5gDiUbE,8632
transformers/models/rag/__init__.py,sha256=omMwtpcTWBHYKZvt8NIxbACHhICmYWfeTgiC7O4U88g,2426
transformers/models/rag/configuration_rag.py,sha256=nZuKmYc9WxOKguf3iq0YEl22snoPRlIiU7gEp0ZR51E,8487
transformers/models/rag/modeling_rag.py,sha256=zXdG8aVn0Qpv7qj2RcWZ77pOdvhdl8Uro_3HSFqWZo4,86276
transformers/models/rag/modeling_tf_rag.py,sha256=kJQykgXldHdzzXh2VKvup72EiEn7_503W__JaQL_yX4,88809
transformers/models/rag/retrieval_rag.py,sha256=uLbdUiLB0uLPDnDG-1hRLZN8PbZvlpDtIteyAFg-sQg,29922
transformers/models/rag/tokenization_rag.py,sha256=DHvekTpL1KW_wItW_Tksiub5nHFn7qTwcDySpfyHmeE,4577
transformers/models/recurrent_gemma/__init__.py,sha256=gUE-KRPGeD_b-aklMGn9oDbnSdZ8twDOQXUxL2zWkIo,1708
transformers/models/recurrent_gemma/configuration_recurrent_gemma.py,sha256=8EjkIeTbmy7LMDtl1g0lqhfqzCTTahRMerPJfQzfVzQ,7713
transformers/models/recurrent_gemma/convert_recurrent_gemma_to_hf.py,sha256=jZGkZ2FmNFWsZXz37gf86NjLRFbgLTK6C-ZO6-JChks,7965
transformers/models/recurrent_gemma/modeling_recurrent_gemma.py,sha256=fZEuFDyehyttFrwNIzT5WTM7iDvRQpVHhF7AbL9fL7k,41755
transformers/models/reformer/__init__.py,sha256=dlDgyR3wG2nCJYg4S8x6lHZCxFUeGdtx0LK2KoQ5ydI,2955
transformers/models/reformer/configuration_reformer.py,sha256=hYsqMbCaFDu7ZdPiQsi_IdTaKK5eXn2j45PSBWVZZvk,13165
transformers/models/reformer/convert_reformer_trax_checkpoint_to_pytorch.py,sha256=pwYPRNmvEk0FRpbR53-pOACHGqv3nzexvtlHaABQIrw,7950
transformers/models/reformer/modeling_reformer.py,sha256=Zl9ao1XSfdqwc92TrNu7VRsE22jYBDIhw1x1wKUPAq4,115536
transformers/models/reformer/tokenization_reformer.py,sha256=3NBWAU7B2kJwNnega3fB4Ys-E4VDKf57nffGvh0Prxw,6726
transformers/models/reformer/tokenization_reformer_fast.py,sha256=06LhqDifGmV9ZrpV0aSC9rFc5Rz3e896o4HMO5eVuUY,4245
transformers/models/regnet/__init__.py,sha256=TO3o_dEoG5M-5bbVo00Gseprgk71WQtquqEXFTezM-4,2888
transformers/models/regnet/configuration_regnet.py,sha256=qncSfSeJHffzN4kTtxwffJeWJNkaNXE_awMIShK0MuM,3945
transformers/models/regnet/convert_regnet_seer_10b_to_pytorch.py,sha256=PH1ePsaNgxZZu9YWob3FTbWZkxnLKoN9bcJCIVK2pYI,11754
transformers/models/regnet/convert_regnet_to_pytorch.py,sha256=kyfKbY-kwlaj-VsXnMZJZwaqZPbuyR-MU58lFDG9F_Y,18702
transformers/models/regnet/modeling_flax_regnet.py,sha256=2Ao7eODWcHufpZoNbGC4FbX6tZVE2bfWWrZSMbPGcMg,28410
transformers/models/regnet/modeling_regnet.py,sha256=zQr0tjypqtdoWZW-hGbsNQWCCoro0z7WlATrDoQgeM8,17687
transformers/models/regnet/modeling_tf_regnet.py,sha256=icuYegqG5RiaC3qajlzXd7AUCEY2BlnFyzOayukst7o,24300
transformers/models/rembert/__init__.py,sha256=TKbwvoTPIwEcR-OxwBZGRof0dhr8LaXMibjKWsu7H94,4222
transformers/models/rembert/configuration_rembert.py,sha256=-aRK_7SAzgP7lcdp-GajWLgSic9KhK_878GmZUbl_KQ,7240
transformers/models/rembert/convert_rembert_tf_checkpoint_to_pytorch.py,sha256=_FwapBBnk_xv1QPby_PGnFvIZfGe9vooclAUwU3Ve10,2207
transformers/models/rembert/modeling_rembert.py,sha256=gAPA40AU9pMp-os3Gpmkaoa_ZwflYzPKp-j51ilUXZk,67188
transformers/models/rembert/modeling_tf_rembert.py,sha256=aB0aDCKmebPHD5d2S-6mXjpNQJ14wzF-A5MFNz4jMn8,77681
transformers/models/rembert/tokenization_rembert.py,sha256=B8uwBMPsa6bsOzoip_7IZTITdplv2KuOzNp2UpBPd7I,10592
transformers/models/rembert/tokenization_rembert_fast.py,sha256=dzMtvv0EAC7HhM8MtlSvGow1-r5oTWrPhW0VACAgy6w,9995
transformers/models/resnet/__init__.py,sha256=40E1scjeQvfyDxwea_ToMN_QmBHgHmje44-3Jtk44cw,2930
transformers/models/resnet/configuration_resnet.py,sha256=Orw9QRpt_rNg-vPdOVZGsPZMschjyTGN6Amrc8LlDjU,6018
transformers/models/resnet/convert_resnet_to_pytorch.py,sha256=fVar-ifk_-_sENkZiTA93wIaEunPMInlp0mmDdH5KOQ,7286
transformers/models/resnet/modeling_flax_resnet.py,sha256=uJMz2FgVXm6ffwjiorCHkuPbCRra8VdN1vYILRuIgxY,24607
transformers/models/resnet/modeling_resnet.py,sha256=jM-MMRgicf-WROgzc85ZylO_VRjd893SF-IEe4mu7V0,19788
transformers/models/resnet/modeling_tf_resnet.py,sha256=buR7Tx6RRt4QHpRrRWwhET1cwZ-E1P3WQNHOQ0O8a_I,23650
transformers/models/roberta/__init__.py,sha256=dmIGqFO7JVd5GiDNTYdE7JQNP-db75ML424yh_t2jR8,4805
transformers/models/roberta/configuration_roberta.py,sha256=YPFSikBG6eFypgG1TQ4j96Bx9ZpuX18NIsmAgrz6NNg,7260
transformers/models/roberta/convert_roberta_original_pytorch_checkpoint_to_pytorch.py,sha256=5sX5PtUseHWXH78xTBXqGsrzn8YttrZsuN0E0H9CWi4,8001
transformers/models/roberta/modeling_flax_roberta.py,sha256=Bz5VgKKwWnVVmRFyHD11Ug7IlvgwOLIMbGI0lBkMHt8,56976
transformers/models/roberta/modeling_roberta.py,sha256=KxccW-28okULM7JdlKIxwP250l2Rt49b82OK9Fn6jGA,77771
transformers/models/roberta/modeling_tf_roberta.py,sha256=EgKfpBE8VeXfuIkP2QEwtZloOra-yub8X_QrzxxOTaw,79875
transformers/models/roberta/tokenization_roberta.py,sha256=4Ft2MWhG4ESEO2yJ0a_8jaUU98IxIH2oLaorhotpC1w,16451
transformers/models/roberta/tokenization_roberta_fast.py,sha256=O3W9P8i_5m-TmRuKf-egQgPrTkoOHeO15BbHW6A1lyc,11423
transformers/models/roberta_prelayernorm/__init__.py,sha256=vTJRMqCxWTYlYx_Mj59lZFIbKSgCeYHyDoCseysFgjM,5011
transformers/models/roberta_prelayernorm/configuration_roberta_prelayernorm.py,sha256=ExP5e6JYnDgbZUYwm6LXzE4A58pyxTx-XFLSYGZNRgQ,7808
transformers/models/roberta_prelayernorm/convert_roberta_prelayernorm_original_pytorch_checkpoint_to_pytorch.py,sha256=DWhNuDUo_dZsWSjJIPzNqxAY9KzWlAWnpgNxE88qpWQ,2974
transformers/models/roberta_prelayernorm/modeling_flax_roberta_prelayernorm.py,sha256=k83Cf_FpzLguXIHtJJSMpj6yuVzy_GddFDpukVmjscI,60537
transformers/models/roberta_prelayernorm/modeling_roberta_prelayernorm.py,sha256=sLg_wqSMtw_ZxgO2-LcpPkxX8NAo6q4iCohRkrOFGuE,72581
transformers/models/roberta_prelayernorm/modeling_tf_roberta_prelayernorm.py,sha256=ITLJNojw7RSiX2cYlUwcZrkbMkzSvNl_AHBaWUE6Yks,83040
transformers/models/roc_bert/__init__.py,sha256=w2OI8OfSZd4zSlfMFm1f4uy5WMZkD09SBjoZdkql9n0,2691
transformers/models/roc_bert/configuration_roc_bert.py,sha256=baWRtDQNSYg14hirr7V0BX43sb_9W1dG-VKc9CphJ0k,8498
transformers/models/roc_bert/modeling_roc_bert.py,sha256=iHfkViRYQEWgWYk5wWMwC83GkedMaTAnMPnnYHSzt1w,93295
transformers/models/roc_bert/tokenization_roc_bert.py,sha256=0xyAp8tchda4cOUc_fg51rClATnw8--f-StQLt_tpAE,50706
transformers/models/roformer/__init__.py,sha256=dkJYDhebvEE_ZktrSP9dOVqvn3Y7phvG0Zfahzjmwpk,4929
transformers/models/roformer/configuration_roformer.py,sha256=GhBdfvr7q94pkaHshseO4WQ65xf_ZLlVC4uOpbAlcME,6803
transformers/models/roformer/convert_roformer_original_tf_checkpoint_to_pytorch.py,sha256=TS6-r9GV2DJXof9onFihBgvXXdn08al0_kJutS8cwEQ,2239
transformers/models/roformer/modeling_flax_roformer.py,sha256=vtWuZyPBfTzBhlVjzJtembSDTogwYO_Am9GxjPhlvd4,39099
transformers/models/roformer/modeling_roformer.py,sha256=Nsr8AOGnfa-pVUh40xlujInS0izA8WU-3LQi0Usz0Sg,68352
transformers/models/roformer/modeling_tf_roformer.py,sha256=XkmJaRSmPdOQc3q5q3029iWlmwdDu9YUr5OrUiHnLs0,65913
transformers/models/roformer/tokenization_roformer.py,sha256=XhSsUFvLn2tUI38lRncHkOchW-Q7FJuUVSAuiq5fyZM,21977
transformers/models/roformer/tokenization_roformer_fast.py,sha256=5vmo29dE7bH0GyLxDc_cMhuZ37ZvW3_iX2EE7xSyQfs,6679
transformers/models/roformer/tokenization_utils.py,sha256=0ciH13qW2kCa5my1rPwfwAuSXX-jGzN0nzemvGvOBxw,2652
transformers/models/rt_detr/__init__.py,sha256=xfmcPdTDU5so1PRqVx-Ink2NaZ_NLmyDbyKxXZ7Yn3U,2451
transformers/models/rt_detr/configuration_rt_detr.py,sha256=4UyA8soIfOojT7QfNRFGSISamiPaqzCatCDH1X5FcWk,18041
transformers/models/rt_detr/configuration_rt_detr_resnet.py,sha256=8jIlVh_RXW-oTbiMJQU9EFq3hEwuDbyOpsTG1H1OHgk,5522
transformers/models/rt_detr/convert_rt_detr_original_pytorch_checkpoint_to_hf.py,sha256=L2tEnPp4NNJlXsI-fBBpib0Vg76qL3xN-OLvrd429Jo,32779
transformers/models/rt_detr/image_processing_rt_detr.py,sha256=W73pcfNfBZw-s8b4GejCiTKRiDxQGfI6wl-Hs_joEnk,51608
transformers/models/rt_detr/modeling_rt_detr.py,sha256=kOb_btLFEXeu8Y3ZnHSmo3TrX2E-5WRxiUS2SKkwvX0,104555
transformers/models/rt_detr/modeling_rt_detr_resnet.py,sha256=cCEG9jMK8qh917EvaJmRux0Hpwuqiw4er3Gl1h2bIHc,16383
transformers/models/rwkv/__init__.py,sha256=tMP2bqMTiU6OvXfECe_AOOepRs_6lDHmNsbbnam7ly0,1612
transformers/models/rwkv/configuration_rwkv.py,sha256=b3jryVZOsrARcWbgqXW0mg-vuwrcDkXj70ouJu-aczo,5176
transformers/models/rwkv/convert_rwkv_checkpoint_to_hf.py,sha256=V6l5MkYhQisv7bA4LUb966KDGSAq48dk2VKBie_iSDs,6993
transformers/models/rwkv/modeling_rwkv.py,sha256=V4-BF-IDqj6b8nncen_gLCXT3IDxia5DNsa7V_bnbe4,37015
transformers/models/sam/__init__.py,sha256=lx31A1rBHuy1QsPWGvxULXzHmTiFMS3Ow2UCMhrcqN0,2726
transformers/models/sam/configuration_sam.py,sha256=_tMyQWQbBsdstkiRpz7TEFA-z8NACH597Ls0s-55dCs,13761
transformers/models/sam/convert_sam_to_hf.py,sha256=Ter22qOhmN1eQyKi387HL0KCBrirnPnr1asU4BobQkk,8543
transformers/models/sam/image_processing_sam.py,sha256=kDfX7MCq1UwqXYLrV7eUw93tpfcRrA6EEPX2tU2OVsY,66718
transformers/models/sam/modeling_sam.py,sha256=ozfOdGE5tYkSQqTifgnlREyq2ROOvbNVzWn9HDnUmKE,64695
transformers/models/sam/modeling_tf_sam.py,sha256=Q5B3rVJ0jEVzGIG5gLDveUQquzyiqx3VMkRaVbyaVbA,75450
transformers/models/sam/processing_sam.py,sha256=UthL1YhtKbS_hnLUoWFEZoZIE2oOqeBGPD5RuPyR-z8,10931
transformers/models/seamless_m4t/__init__.py,sha256=OQWV0sb2N7e3FZpMt9jHKMjYmljFaoIfE6LjYhTzEzw,3506
transformers/models/seamless_m4t/configuration_seamless_m4t.py,sha256=wAX2u-yO1N2G_gkdu4fe7hy3hxxwSKVx-1kZ0BRzX2Y,23463
transformers/models/seamless_m4t/convert_fairseq2_to_hf.py,sha256=AQ75kQXD0Yv1Xn5y_mf3FhrHShAkO66wqg_Y-o93qy4,15958
transformers/models/seamless_m4t/feature_extraction_seamless_m4t.py,sha256=I7lXE8BRgjU2q1bIsASVY15pkDc6ZO6XJbqb5FVJERc,13599
transformers/models/seamless_m4t/modeling_seamless_m4t.py,sha256=5y7leekp6FeoIKdbRuF0e9May81rt19aZs-ffYxXnrA,198827
transformers/models/seamless_m4t/processing_seamless_m4t.py,sha256=OrPvDJkAAIuoWglyxt1Z4H993tm-AyX3OxDcu4Gmps0,5893
transformers/models/seamless_m4t/tokenization_seamless_m4t.py,sha256=ebF-o-zfq2cwprpg2p_rzs4-pHLSMhlIwPwg6_QK4Ec,25976
transformers/models/seamless_m4t/tokenization_seamless_m4t_fast.py,sha256=_KTkh1y6aGAHY4GO5J5ATqqZJYCvyelMRczfoMjH3oA,19885
transformers/models/seamless_m4t_v2/__init__.py,sha256=cPa9YAy0a2YBBLpS08YZA6tOcy3kLDpy8nRrUqnt2VU,1947
transformers/models/seamless_m4t_v2/configuration_seamless_m4t_v2.py,sha256=-BmLD5-GE4f4U1uz9e63GDSAqpxnOIosdEqXXQDTKP8,24320
transformers/models/seamless_m4t_v2/convert_fairseq2_to_hf.py,sha256=3fuW2IGoJpNKVGCE9YXrWbXZvdoko6cfQLyLMn5wycg,15082
transformers/models/seamless_m4t_v2/modeling_seamless_m4t_v2.py,sha256=RBCDMiQsyTt5IXJE7Gx5Sb8oZ6o7SPxoobZUrh6q6kA,225409
transformers/models/segformer/__init__.py,sha256=t4nnS6zRMt_WWrULCuLLlTsywy3qsOU0BpP-KBklOPI,3372
transformers/models/segformer/configuration_segformer.py,sha256=xXEa-6pfoWJUJH_Pj_NpwH22PVejPFzKqhb9aI-meCc,7365
transformers/models/segformer/convert_segformer_original_to_pytorch.py,sha256=JxOpEDBJ_IQHNlvbXADlMg9PBQwPdSgI4ybc8kj2hPY,17091
transformers/models/segformer/feature_extraction_segformer.py,sha256=yaRckmbmTyh1Oow3PnHLsjW4MURaWqddhTzG-PVcywk,1207
transformers/models/segformer/image_processing_segformer.py,sha256=1-1iT14G11Nft3koeh3nGdzlW1K1XUUGuDgG6dJUFK4,22744
transformers/models/segformer/modeling_segformer.py,sha256=hD_5jKL8a2c_Ec_kr0OLxBhYKgtYphNxHY1KNiXlVYY,35352
transformers/models/segformer/modeling_tf_segformer.py,sha256=PnPn8OXgaJ84Ga_XnWM-85Rig2KT1_9RF-KwqAn-IQk,43610
transformers/models/seggpt/__init__.py,sha256=E_Pm_8e-v0tEwe5EJuMOPW9frmS1Jd-4R9e2EJ0ISNY,2102
transformers/models/seggpt/configuration_seggpt.py,sha256=HuZd7Rk_Tp71kEbi_yrgQyTk6p2etcw9wEGhP1lJvzM,6463
transformers/models/seggpt/convert_seggpt_to_hf.py,sha256=BZLBrTnCLMLgbcRZbUk8sflo1R_jpMS2Ru90GwpQtxw,9778
transformers/models/seggpt/image_processing_seggpt.py,sha256=1TUOWkQy5b7z1I9an_379vC-s7qM7VHJ15f6AcQ10Cc,31472
transformers/models/seggpt/modeling_seggpt.py,sha256=DN2J0gl6Dy99nzSZcdrMIvPgQKyg8ZuKfq9cKiowNl4,45767
transformers/models/sew/__init__.py,sha256=4bFyAwCuucy4NQ_QrtlPwi-K-jmAvU7-OmQEWg2Htes,1614
transformers/models/sew/configuration_sew.py,sha256=iACEkWqbDjre1GtdxXLDQmJ0PbWB2h5BEZQQ9qoxFA4,14181
transformers/models/sew/convert_sew_original_pytorch_checkpoint_to_pytorch.py,sha256=SOWT4r47np8zJwW6fdWqtgCfuQAHjXUVUgMHZ6STCDA,12744
transformers/models/sew/modeling_sew.py,sha256=Df8d7r3DgE__TCWT142dcyDaMVpnrJFF5pCM7X3sf1E,67190
transformers/models/sew_d/__init__.py,sha256=jqzaSuSywiyUNzonA2Heh5TFVrIaBoxvu2o3TVGIGSI,1632
transformers/models/sew_d/configuration_sew_d.py,sha256=tcwJ607_TuIlYz5wcoAfbKQ4-AIp2_lxgPP8CCcDJr4,16148
transformers/models/sew_d/convert_sew_d_original_pytorch_checkpoint_to_pytorch.py,sha256=5Cy_FfZr1-7Kgmu8ipmETQt33BTyPFjX93tuaPV_tRw,13574
transformers/models/sew_d/modeling_sew_d.py,sha256=A-DapL908AuR8cQfMkKdYaw6gsL8_oqthaJDhwemi1c,74046
transformers/models/siglip/__init__.py,sha256=EqNmxsDysOinTc26ViLXvWcaPjrG-UvNY1n24H5fd00,2932
transformers/models/siglip/configuration_siglip.py,sha256=nEQScf4Qkb6BpnXkKP4LnL1lwqJiQsyn4KA2ZhG3GXs,13525
transformers/models/siglip/convert_siglip_to_hf.py,sha256=JWlMzjGcS_OgIE7KGlVX1yWQJsVpWG7X3rLCoXkUJ90,20829
transformers/models/siglip/image_processing_siglip.py,sha256=SY5nmdoD72XrzRzr4sKpIetpUa7K8OZF2ojqT3xYDRM,11916
transformers/models/siglip/modeling_siglip.py,sha256=KP9PQSvoy5wjmL177dR2hdH5yAbm-mnhdhptfz58Lc0,68929
transformers/models/siglip/processing_siglip.py,sha256=x5A9CKyzNzOF0udXvMVQ4hMFBCbAdH-WnLAXqop75zk,7302
transformers/models/siglip/tokenization_siglip.py,sha256=vJYlCDDT8g5GR_hIc33Ztc8941kga42tktBdZM0AvY0,15952
transformers/models/speech_encoder_decoder/__init__.py,sha256=987NzBteEbQy0IYY43B_JKolw2BbyX6Ox9s__xH0daQ,2037
transformers/models/speech_encoder_decoder/configuration_speech_encoder_decoder.py,sha256=7hzCE73LcHbiq3b4pTsMdSwjtl4izOtoZE-ldVs8Bx4,4575
transformers/models/speech_encoder_decoder/convert_mbart_wav2vec2_seq2seq_original_to_pytorch.py,sha256=T7TlN5DEbuMVgAtK0xwljBOEGjHGTzZ6Ndj7V-XVScA,14753
transformers/models/speech_encoder_decoder/convert_speech_to_text_wav2vec2_seq2seq_original_to_pytorch.py,sha256=wyaXE2ICf7YBoaJxi4W4a9gbxjGW7zwpdOxJCPBsLHA,11970
transformers/models/speech_encoder_decoder/modeling_flax_speech_encoder_decoder.py,sha256=a46D8AZ5BOcq7j5gnTmlyCYT9Pm0pmcIU9-RL99Xwww,44642
transformers/models/speech_encoder_decoder/modeling_speech_encoder_decoder.py,sha256=pxjxVMAlHRxjB4yqioywR-_37PlNJJZXVHgUopdo72E,31846
transformers/models/speech_to_text/__init__.py,sha256=etRxtayhPA4oGlVEQI6x2nXD-x-uQxH9FxQ5g8kPw8E,3163
transformers/models/speech_to_text/configuration_speech_to_text.py,sha256=MYjnDsybG3WziBxl3xinpNRcriUdNccCaoz18K1WxVA,9775
transformers/models/speech_to_text/convert_s2t_fairseq_to_tfms.py,sha256=v-5aSPwuCKCtqwU8gREj9wA2nm14Z97tg6wQ3S47gos,4478
transformers/models/speech_to_text/feature_extraction_speech_to_text.py,sha256=fPUzYlgnkCv2mzgtZVlvIl1mzxh_7WiPsHz8AJ1ezfA,13188
transformers/models/speech_to_text/modeling_speech_to_text.py,sha256=sRvou1sDb7uQIZX0Gj2hJ1qYPfi4SLciKCHp4SVIvc8,63538
transformers/models/speech_to_text/modeling_tf_speech_to_text.py,sha256=qWuPI6X_dS452tVe19ImwhksKj39txcoJqxzMdIYbO4,74312
transformers/models/speech_to_text/processing_speech_to_text.py,sha256=lSRyp08-pwXHmGsuMK_c_1Fj9NuL3u82QtHK8EvpSOE,4819
transformers/models/speech_to_text/tokenization_speech_to_text.py,sha256=SsUbAjjZZm8SzbToKOxIkI-UX3rHyHRAjTwYaEMq_CE,11401
transformers/models/speecht5/__init__.py,sha256=wj_1b9q7LXw_6Yuojtk5cJmM94OatTr9O_31EiLhui0,2657
transformers/models/speecht5/configuration_speecht5.py,sha256=10zI3UsOS2LeSdqU7XoPz1sZssUvN67NMyY-xnUTDM8,23378
transformers/models/speecht5/convert_hifigan.py,sha256=CL9GSX_bimjm_hU2rE55MaNvTUjTtWD6qCtqNMaXy7I,4241
transformers/models/speecht5/convert_speecht5_original_pytorch_checkpoint_to_pytorch.py,sha256=AyAjaeibe3002YZRT2maq1Yi8-iP1j7Ahs5qxYMjiJ0,17194
transformers/models/speecht5/feature_extraction_speecht5.py,sha256=lcKx3NaIXx0PGITRKP0kA8SZK75kd1Sn8PNHLBn-ST0,17809
transformers/models/speecht5/modeling_speecht5.py,sha256=C2eXZqLqwyLRlS7MBLA2WqG-TbA1WwIrmb_ucR1ok6o,154453
transformers/models/speecht5/number_normalizer.py,sha256=cxnEUdHSISW5eAo15cLuVkZa65zMFuMFaJ8zAOQCsAA,7019
transformers/models/speecht5/processing_speecht5.py,sha256=smqFdqKJQp9Vm1FDfmj7EvJeAZKSPB6u2AZMfsjsQa0,7562
transformers/models/speecht5/tokenization_speecht5.py,sha256=bSBbTWN-2yTgkT-IlmlGbzJ9fVyonH5y-OuOA-mNjK0,8912
transformers/models/splinter/__init__.py,sha256=dQmgY1NCNXi8cHdeszUdXje1pSGdTRxsJufCL3wZVIA,2348
transformers/models/splinter/configuration_splinter.py,sha256=-fn94zHBMtHYNTCbCLXLiQ3JgODMvSBovYowM98VW5M,5594
transformers/models/splinter/modeling_splinter.py,sha256=7nI5DGeYKLdla98yu-81jntqA2oTzvvFEFHMnSWAR2Y,53299
transformers/models/splinter/tokenization_splinter.py,sha256=gaNnQ3p1t6gjMF0YR95dfw1yXjALBiFuaIdw5Q5eVrI,20947
transformers/models/splinter/tokenization_splinter_fast.py,sha256=t-gbV9OTlANeZQ_XLiV5GYpp9qZW9i7VllaLKf47ztI,8565
transformers/models/squeezebert/__init__.py,sha256=bp45xndCYIUnwmiLsbse3SMxx6qACtyGBHBELrhekgA,2784
transformers/models/squeezebert/configuration_squeezebert.py,sha256=Hw3_FthVdI0SFkx5XTWRoW-IKRRKVR-z83fByxpJlCQ,7244
transformers/models/squeezebert/modeling_squeezebert.py,sha256=1dL1pCmHMybCEgHQV451UTHelfApjgR6xasjT4hyTj8,45053
transformers/models/squeezebert/tokenization_squeezebert.py,sha256=DUYrMJmBMX1k5Wg8YPT4XWwZ0pPKtV25zz_jm4s0qbo,21211
transformers/models/squeezebert/tokenization_squeezebert_fast.py,sha256=J22q1PJ-qa7ymcvvpmiX2ft2OxUDHi2Gdiny4rOiOZM,7819
transformers/models/stablelm/__init__.py,sha256=rly3o76iQXhys33WzZBj8yHAIAVUvMkNr1-HYteGqb4,1828
transformers/models/stablelm/configuration_stablelm.py,sha256=vrSkKqbrND0pmQPCZsxE02hk_POCCp6gvqYsWtM157w,10806
transformers/models/stablelm/modeling_stablelm.py,sha256=ReD7Ea0HAgtltynru8dejYifap1-mGA03xZ7HxyIEGM,67793
transformers/models/starcoder2/__init__.py,sha256=KvuuG416ad_oZhxX-zzkAkpTFpw4Qy9XlMs-CQYOugs,1855
transformers/models/starcoder2/configuration_starcoder2.py,sha256=k2tWXeiSvWoYsuZntP_tAQkehSmJvUaZyisNFV5Byv0,10264
transformers/models/starcoder2/modeling_starcoder2.py,sha256=gwTMPjIGxY9GS18LYRrQZO3WsMlzgotN9xW1fhH0wkY,65276
transformers/models/superpoint/__init__.py,sha256=F689OHjjtn1F35ezCfMPus5aDWiMTR8UkbxBjq_Mjwg,2105
transformers/models/superpoint/configuration_superpoint.py,sha256=qA0lWTHbPEKf2aB9g1qvstCi24EllKpmlfOSpDdSoyE,4039
transformers/models/superpoint/convert_superpoint_to_pytorch.py,sha256=tO1P6yqW46LY1hnWIJPOs4KjW0uZWkiVWW-GTOXbJGg,7243
transformers/models/superpoint/image_processing_superpoint.py,sha256=Om_ry5alSPtghMVDfFXI2CwDYRNm4siwZGYPmqdNFlE,12510
transformers/models/superpoint/modeling_superpoint.py,sha256=yptk889VYdYKaQOzKsH0qKLOSQKrrIwE-V2zS7XKKfs,21456
transformers/models/swiftformer/__init__.py,sha256=Zff4CKLyTCG1bb4OL1g0QaCgJN6OqZyXAdG5YN26QCg,2452
transformers/models/swiftformer/configuration_swiftformer.py,sha256=FkKIxalmrqw_PV_KuZVjpneGoK6VH5SAaO5ji3hfzKc,5799
transformers/models/swiftformer/convert_swiftformer_original_to_hf.py,sha256=f3WE1QJGHW6Ak8fu3n37S69m92xgICfsbYYKDvibn6c,6238
transformers/models/swiftformer/modeling_swiftformer.py,sha256=jdntmvVPb7cre-tEPBa7rHn7YKHQPSJvW4kEsDHBprg,22746
transformers/models/swiftformer/modeling_tf_swiftformer.py,sha256=N7x7HbuKGvxOe-We-iEwbNVsdZ--e8-vom2L0LKPUTM,34860
transformers/models/swin/__init__.py,sha256=0KzgLwL1tvPlqAmu_3FogqSM_dNHx3fgdOMQdrIeCAs,2435
transformers/models/swin/configuration_swin.py,sha256=aomGg6BFigfdVHi-1YHEPvZUwl5MI9bEw3hxp4D5t7Y,7904
transformers/models/swin/convert_swin_simmim_to_pytorch.py,sha256=35aBdY_dHVSumVv-vl6qS9wuJeNrqWT5EkMTvo8zVfo,6631
transformers/models/swin/convert_swin_timm_to_pytorch.py,sha256=UE5XxkTNJZjTKwfK_upI-bOqZwE5fIgQjJDSoPKkT-g,5809
transformers/models/swin/modeling_swin.py,sha256=k1G0tXc-aBSmZNCpb2p2mRigoAXN1sc5uHKeFZn8UpE,62987
transformers/models/swin/modeling_tf_swin.py,sha256=-PeP-jpAE9gUCI3zzI-vxe8l-HPtw-LV-n-cnyamlr0,70673
transformers/models/swin2sr/__init__.py,sha256=5asgXPZTPzEfh2hmTWc8ztYBmNtEAoSzTOGR37_U-ws,2097
transformers/models/swin2sr/configuration_swin2sr.py,sha256=FmiFWU4tecLaORaEUmSViGE4i80wdSMTk0F6DlfO6Iw,6811
transformers/models/swin2sr/convert_swin2sr_original_to_pytorch.py,sha256=xv4DkXMn4keBRk8lLaG5Kq91DdQMn4q_25WudPCYhyo,11363
transformers/models/swin2sr/image_processing_swin2sr.py,sha256=6ZbVabZkmmGHEYwdJ1SxuwcYmeXX9lF3elhGP1zKNyU,9209
transformers/models/swin2sr/modeling_swin2sr.py,sha256=HUFNUQtLVwIYpzQg50nlPMf4mGz9xU9787TYrSeLFso,50778
transformers/models/swinv2/__init__.py,sha256=GEPTL7RC14eQrc8dpdGIqv046z5U6EyZErY9cDuhF2o,1745
transformers/models/swinv2/configuration_swinv2.py,sha256=PQykgFSB4-MX2_wjFO7Ec_FYe25SPGRS3qa1P6K1f2E,7518
transformers/models/swinv2/convert_swinv2_timm_to_pytorch.py,sha256=Eg5ASHw46_huYnDn-QCdVXJnSb_U5QfzrktX__nP_D0,7693
transformers/models/swinv2/modeling_swinv2.py,sha256=ZsPSK2O0tHgFXsbgpgn85I69v3opB2_hOiYG1WzcJD0,66783
transformers/models/switch_transformers/__init__.py,sha256=I7mMOnAQlrQQlaWIzu6axcmn46k5FZuGjp9yXf9qX5o,2240
transformers/models/switch_transformers/configuration_switch_transformers.py,sha256=v8XcyDGyi0DILV2Rrkxfu0I1WUdktwMSbcFvfxHVlJc,9005
transformers/models/switch_transformers/convert_big_switch.py,sha256=wjMGjHXAqVool6fZQhdG_Av2Ujx9EDoZrtHC8RdDLk4,7659
transformers/models/switch_transformers/convert_switch_transformers_original_flax_checkpoint_to_pytorch.py,sha256=AAJNkPcr_THjPN_8RUnOdBYbbYc6GOqXdgdjhx9FZyw,7593
transformers/models/switch_transformers/modeling_switch_transformers.py,sha256=OMvDWQh1MIZr011n_7qssde7OtnTta3DJ1QslhXjAqM,94182
transformers/models/t5/__init__.py,sha256=0b3q_e-t6U3nDHzc2h88XPscvUDRCnzvx_K0lscUtAs,4236
transformers/models/t5/configuration_t5.py,sha256=fjQdDyZc3PS58X2xIM5BOW3IRTjNGQIIgcPz_5I-vY4,7331
transformers/models/t5/convert_t5_original_tf_checkpoint_to_pytorch.py,sha256=LEibHPdlDdKdyB6XHB5s7pHRsqB5qQxUWN93H8G_q5k,2119
transformers/models/t5/convert_t5x_checkpoint_to_flax.py,sha256=PLgfe5u_gcFjBduCmAeuKaDW4VjJtP6KKsx4zIRX8hs,10580
transformers/models/t5/convert_t5x_checkpoint_to_pytorch.py,sha256=GTF0FYHDDDBl2tcYgHcirqHOI2KOE2YkDG4ekzjh_Ao,10483
transformers/models/t5/modeling_flax_t5.py,sha256=AJ_KxZuAvPeBTFrXLSDAWB_61h0JqLM5Hs_99KvDDSc,74164
transformers/models/t5/modeling_t5.py,sha256=uYzv18SfvvcpdI4nIT8-b1c_ndwCYlDXyIW-6T40neA,115089
transformers/models/t5/modeling_tf_t5.py,sha256=KRNhg23WlBDGcWH_U9MD9ddImh8vb5JPGjfPN_w03I4,77079
transformers/models/t5/tokenization_t5.py,sha256=X9BfGeEOLXKkOADHRkH8D2RD7lJmp2ne91wERbITG2Q,19991
transformers/models/t5/tokenization_t5_fast.py,sha256=8Xcc-nHK9IXeKiMrOo9GgCD3ZRt18ON1sYm3ZdXTrY4,10110
transformers/models/table_transformer/__init__.py,sha256=5VJzy-OFzZtMyOI2vK3fY5Fi7xoHFU23ujDsO1iwxPo,1829
transformers/models/table_transformer/configuration_table_transformer.py,sha256=38_xDcXUcXfdFpkNej0pKty6SNimCLFWmzDOEBts-M8,13304
transformers/models/table_transformer/convert_table_transformer_to_hf.py,sha256=Fw7-BfEEli4_Qk4AXZ9sNfiitz6IJwyTGPdtCGlkBg8,15094
transformers/models/table_transformer/convert_table_transformer_to_hf_no_timm.py,sha256=9A3hwdQWayhc8RoxUWpLoPyhhpHDuVgheIN_MOkLnYI,21185
transformers/models/table_transformer/modeling_table_transformer.py,sha256=kG2cjM9FGsjVxCqTKJnlOX2XUco940s6zU80IFv10-Y,69918
transformers/models/tapas/__init__.py,sha256=iyb5L4YVgub8YAXp89LsHjrc81hPtTk0xoUbRZB_1oI,2678
transformers/models/tapas/configuration_tapas.py,sha256=Y6fdPefBKdklapxnmdZxNjYh4iTgNgv3ysFA2yDqE10,12265
transformers/models/tapas/convert_tapas_original_tf_checkpoint_to_pytorch.py,sha256=K69dSJ-h4XbiYh_4pd7UX7KErXThx8dtDORaiCcBKTM,5048
transformers/models/tapas/modeling_tapas.py,sha256=jKb2-AS0GUjYDknTRGfZoCB_pCEMvr285Cxtc_x8EOM,110435
transformers/models/tapas/modeling_tf_tapas.py,sha256=aihq-2EvXt9bQswyLOlR4BfdqeTsDx4u4oIjnfV7_eo,112228
transformers/models/tapas/tokenization_tapas.py,sha256=xX6ABBqc1ZiAeN27qGSKSPf9optC1vZATXwafxUHvPs,118393
transformers/models/time_series_transformer/__init__.py,sha256=SGCjnebDLeCZOFRcSjeojL3IgIuW4Lu_lHwxX7XWarQ,1794
transformers/models/time_series_transformer/configuration_time_series_transformer.py,sha256=AjgVw6YjCaYCnJJiT_V5-5bgZMEvQZnlIJ6MBrXfWJ8,11657
transformers/models/time_series_transformer/modeling_time_series_transformer.py,sha256=sqUB4_ldIg_oeb80K0zaSRO4lASEupsxDhAx-nCKyHE,88553
transformers/models/timesformer/__init__.py,sha256=AnmocPlunuIAehwJ7EYSuKJCy8QWtrFLsD-7NqA6dUs,1666
transformers/models/timesformer/configuration_timesformer.py,sha256=3FX4rs8g8vsCRE_N8GphIJ-Ib2pyiUgPjOOLa7jexMY,5534
transformers/models/timesformer/convert_timesformer_to_pytorch.py,sha256=TjOfPbEC4oVb5tlOgU2m9g36OBizDEEjm0bbcZz6Mq8,10176
transformers/models/timesformer/modeling_timesformer.py,sha256=-5LIsbKosfORf9B6JmdGZ3HsHl08uVaazDYFa2rebCA,35193
transformers/models/timm_backbone/__init__.py,sha256=rn9y1wXicP1g6IiI_tSWu7fnt5q_x6hfu3g9yQvovEU,1624
transformers/models/timm_backbone/configuration_timm_backbone.py,sha256=pYzjQoWVLkxUGScEoM9UCAmTgIeBFYw2DpXOS_ZOXsg,3151
transformers/models/timm_backbone/modeling_timm_backbone.py,sha256=RWRPlTWGQ-oziBMNUCCpSdaQWiWW-uhj7RPvzjemeOI,6937
transformers/models/trocr/__init__.py,sha256=BinAFMZ1GwQ8eNwe8MJ3OC9hdExObFUud2hTrHeRT2Q,1658
transformers/models/trocr/configuration_trocr.py,sha256=WLVxVr-Hw8LumF67LkpcqqhKMhKYjV_XEc5OJxtBDw4,6522
transformers/models/trocr/convert_trocr_unilm_to_pytorch.py,sha256=FO6U6L51DYBxCxvalXRVpsfZZWKK7PHZV-6nJgt4icI,10165
transformers/models/trocr/modeling_trocr.py,sha256=9x9T2R_OO8CM0HxO6CgrRR7BLhl-8fbR16Q685tCYu8,44883
transformers/models/trocr/processing_trocr.py,sha256=7uB47Cr2SajBW_4MhbdEmJ6-mtfKnuKl8PhZyrIkjWY,5746
transformers/models/tvp/__init__.py,sha256=cljUuwy0tTsvdXsdZOTl8BgrTy8sRruCFWFUDobI-qI,2171
transformers/models/tvp/configuration_tvp.py,sha256=K3uECqqcf-SPODsUWZjXgOScrawxp1arjjhQElmHmCE,9906
transformers/models/tvp/image_processing_tvp.py,sha256=KtDG4MvtrBSD_4bNIuAmU3yxks6Rr2uOowRe1wnL1sY,22548
transformers/models/tvp/modeling_tvp.py,sha256=0XzPwaI7wuulpEBCGUEsJcfTgJWVdZO9h4UhXDg0vt4,43604
transformers/models/tvp/processing_tvp.py,sha256=c9nERZMOgxsu8wLQ8KhJ8b37_LLUReURTouYA0GM6Xg,6980
transformers/models/udop/__init__.py,sha256=eopYOXkMGcU9DhpsBVSPmREG5nGQF7MRdSgCvHwTb9Y,2696
transformers/models/udop/configuration_udop.py,sha256=4w_9uMxh1exqrKTHqfmUkDjuNwBs0cXu8Vkc1lAYrfA,7648
transformers/models/udop/convert_udop_to_hf.py,sha256=3HkMdxV39MaBHOzIHQnAzBnA7yxoz7Zl87pspmORr3Q,33524
transformers/models/udop/modeling_udop.py,sha256=62-Z7tBwQYJzt89T3Q0zbnIIKH5ZYYBLrbCYJAJ9110,100824
transformers/models/udop/processing_udop.py,sha256=Lui_ylij5XT8-oHaJCnYBWeJDbgn12fstwwzQJM-zOM,10569
transformers/models/udop/tokenization_udop.py,sha256=Q1_zIBepVPrfONxt_Opz4GS8AomtK_arXkCZ74xvf_4,71686
transformers/models/udop/tokenization_udop_fast.py,sha256=0CGeJcrM4SsFJinaZimU6uBD478TB_AqA6ol8cdjpI8,49760
transformers/models/umt5/__init__.py,sha256=wcKbkdS_suuZCQs52Oz0lBegIa0QDSPZW2Q-XBpM3ns,1908
transformers/models/umt5/configuration_umt5.py,sha256=qEmNaoPveW1y8uEdg1AsNM4p0bCAxiAGvschSvan40Y,7695
transformers/models/umt5/convert_umt5_checkpoint_to_pytorch.py,sha256=cSB6TobLxWoeNNqPXPiH4YOKwj0ji9phK8gA4vzt-jo,12072
transformers/models/umt5/modeling_umt5.py,sha256=QAQsS4GLyaDAxzzn3nXNOOgig-ZAZLLuEuL0AwiXPfk,94465
transformers/models/unispeech/__init__.py,sha256=v1Xl2KNrosFlv-ep9bXLiML1C-9lhiultR-UmlB8XGU,1830
transformers/models/unispeech/configuration_unispeech.py,sha256=bxlMcrmyJVytWngvIOa7vz6zvEiqI7fVud21q7lbmq0,17454
transformers/models/unispeech/convert_unispeech_original_pytorch_checkpoint_to_pytorch.py,sha256=9Sy8RKspS_mb4rTl4t9IlqNaMiXQz31ATrWRFfM5xhA,11339
transformers/models/unispeech/modeling_unispeech.py,sha256=BgB7jaQZ4Xyffx7eiqBKOTnd_BVrR7dYIO6aNBIu2gg,86652
transformers/models/unispeech_sat/__init__.py,sha256=p1GTWc9LU-N1jH_I3EY5HeEZOeFgUzuHSlElpTpCynA,2063
transformers/models/unispeech_sat/configuration_unispeech_sat.py,sha256=WB5cTDJdSlEIIsM-NVGHxyqb5WMIRI4OigV74_HnYLw,18796
transformers/models/unispeech_sat/convert_unispeech_original_s3prl_checkpoint_to_pytorch.py,sha256=OLOISwA82PhBuqITPvfR7bP23Fx0Gxvb2c2SbCKk_XY,4869
transformers/models/unispeech_sat/convert_unispeech_sat_original_pytorch_checkpoint_to_pytorch.py,sha256=aqmh0Am1E26ifRHt8MujjdUO9CHkVZ6ziqoOIr3n6-o,9288
transformers/models/unispeech_sat/modeling_unispeech_sat.py,sha256=qHCIdbRIzS1i_eSN7QO6KLkxll6Luk4F4Gb0HWXuVbM,100852
transformers/models/univnet/__init__.py,sha256=mvvYY7ztGirStHFoaJ3ksEf2Yt6RCI72xnBhPySISG0,1631
transformers/models/univnet/configuration_univnet.py,sha256=6x2-kNWG4Ah3kSqN_BpMcmK5eduT4L4Fsxp0SbcO4Ik,6728
transformers/models/univnet/convert_univnet.py,sha256=R2gqXfz8Oq2rwIUU01V7T_oSoDGG2A4Gety-R80Yn24,6364
transformers/models/univnet/feature_extraction_univnet.py,sha256=snAVdQ5ClFX_Sw7upgvWyzJq4bUNRelRQaxcWxgHIgA,22821
transformers/models/univnet/modeling_univnet.py,sha256=sAyJ_f4fbWxo81688dbFefQf1TKua9iObA1AJKGz4oo,27543
transformers/models/upernet/__init__.py,sha256=z2avy6tP_WpANiGPA5RCxT_9yPp0PfEDlfUjL9rQsXM,1535
transformers/models/upernet/configuration_upernet.py,sha256=f53XBT9QQpqwrJU4HK1pbW7MurCddZtL7St0fBzNbYo,6613
transformers/models/upernet/convert_convnext_upernet_to_pytorch.py,sha256=l_CJoXwANEE9rm5mwpHwbusIoJLmN8jNGjxsj6WhZrk,10271
transformers/models/upernet/convert_swin_upernet_to_pytorch.py,sha256=lHV8SE_bZnxOo-zEJ21S2nY449uPVc3bpcl2JGKNEjA,14026
transformers/models/upernet/modeling_upernet.py,sha256=g3dYlwGd8yQ4PNCki1Uuk_xYoMN5BLGvgEW9KSC_CII,17135
transformers/models/video_llava/__init__.py,sha256=gOB5OsDdV-YgmsiRMt5CDcPCntP1r5l8Vh-sKe4aEKs,2231
transformers/models/video_llava/configuration_video_llava.py,sha256=GzTx4rW5L1C7hiNzRjZn1RtDAMMgmR6rkJmDri1dN5w,5995
transformers/models/video_llava/convert_video_llava_weights_to_hf.py,sha256=2ubkFfdHTRc-6BPLYG1A82Daki0EBnSkmVPLQyRc7eo,6078
transformers/models/video_llava/image_processing_video_llava.py,sha256=MM21fhRF4kah18zISxdNxarL1NQY8aYo5nzBiDcTLBg,19311
transformers/models/video_llava/modeling_video_llava.py,sha256=Y1liKxVV9MtXDbxaMLX0pHdklMHBJ_ztPaoC2qiAxHc,39111
transformers/models/video_llava/processing_video_llava.py,sha256=JDSWoWmHVu0q0_ctILUZbT1qhLxQHkKKczWVlUgscoY,11542
transformers/models/videomae/__init__.py,sha256=7mPLbj32OCbHA8_chBPNVUEbCRm4BwOs3F9fHKKShwI,2335
transformers/models/videomae/configuration_videomae.py,sha256=OZNNpgURTWBUC9mr8ZuhKPyMXwz-D5F_8BdoIc9-tgQ,6569
transformers/models/videomae/convert_videomae_to_pytorch.py,sha256=rq2nT2ZJekra1G38kM2DH_qOvcZBDQFNgbCvH3mKZjY,13989
transformers/models/videomae/feature_extraction_videomae.py,sha256=Hg5wmFhkbncqR3nfvtevV6msaUEqvLBf4mtO4aICYTI,1200
transformers/models/videomae/image_processing_videomae.py,sha256=EqLfU7pKeyo-PgkQ5Pz4xtu6WSOvUVSJD9cmE_DfxKM,16508
transformers/models/videomae/modeling_videomae.py,sha256=8k2lf6zmvg_rcMJsWaGo1NvmNtjG0UZI96O99mjJvow,49292
transformers/models/vilt/__init__.py,sha256=x1LOq1RWrRzvl1Zo6na-6RGCP32oIQA-DrQ5fM1k05M,2620
transformers/models/vilt/configuration_vilt.py,sha256=rd2Ee35CnNQXOjWBeTNAZZ2ugpCAiwK1Dp0-zjs9dq0,6788
transformers/models/vilt/convert_vilt_original_to_pytorch.py,sha256=PE1IIC0UxmzENP_L_Ev-2mOw3Q43N7Hpog-I8qQN_Yc,12881
transformers/models/vilt/feature_extraction_vilt.py,sha256=dC0Glwc_rDX7zqp8BxRtzaLogQGI4I4CjQCgxU7UORw,1172
transformers/models/vilt/image_processing_vilt.py,sha256=p8YWFJNMPNXF3UBM3XuIKSpgmRQ0ucUUqdvFugzqtnU,23156
transformers/models/vilt/modeling_vilt.py,sha256=86fjdFWrymg99Zm6Q5zo7lrJ1R3WvjKtSz8WxhwceSM,64888
transformers/models/vilt/processing_vilt.py,sha256=0iOal8dCaE7JCQlZjbJ1-sHGxpDPZgUkMowEbxFRF2Q,6079
transformers/models/vipllava/__init__.py,sha256=ETx0yY4OemeAzBoFkOcyk-cXVVnxAB4BytukiSI6xRQ,1556
transformers/models/vipllava/configuration_vipllava.py,sha256=VbZvisqfhWZfVC_tO9VSW3vUoerXkPvUrZ3DKE1q3gA,5122
transformers/models/vipllava/convert_vipllava_weights_to_hf.py,sha256=u64-lOXDE0JMGhkGYJEtyrOh3gpeJtxSDC_dC08mc2c,4794
transformers/models/vipllava/modeling_vipllava.py,sha256=kr8G3p8rK3dr6VROgWIQDybo2BXIAPgNfmCNSlos9dQ,31689
transformers/models/vision_encoder_decoder/__init__.py,sha256=IRQsS-4Bz-cm6B97rSoeC62Z1l1wns0XVDZwBn1KBIU,2627
transformers/models/vision_encoder_decoder/configuration_vision_encoder_decoder.py,sha256=6x7tdTBOrsvKOMy12NCtbPatY2qaqOJaVIGGxy3uPDw,8273
transformers/models/vision_encoder_decoder/modeling_flax_vision_encoder_decoder.py,sha256=AtoTzqUjolUXn-NBPdzhc2gNRaDJXPR92eGUj9fZmSg,41533
transformers/models/vision_encoder_decoder/modeling_tf_vision_encoder_decoder.py,sha256=rz7SPcIwpGCHnrdZk-uix9hsGOIuUcOgZjIfr9U1xxY,36237
transformers/models/vision_encoder_decoder/modeling_vision_encoder_decoder.py,sha256=GRNCegbd1ICr8YNGz7YEQIAU--b78XHUhSvjq4NW43w,34677
transformers/models/vision_text_dual_encoder/__init__.py,sha256=kULrtY2Ie2eigdn63xnoEqRUlmKm31D9mUCJs4F62Lo,2730
transformers/models/vision_text_dual_encoder/configuration_vision_text_dual_encoder.py,sha256=awSM75zqIk9PMkhuvMXYaxW-DoSw7sNRX1Kozg9kAOI,4895
transformers/models/vision_text_dual_encoder/modeling_flax_vision_text_dual_encoder.py,sha256=IIJwejtF8IWZCr1XwWq7G0_h0oJRJ2l5AfDpEzCEFLE,26312
transformers/models/vision_text_dual_encoder/modeling_tf_vision_text_dual_encoder.py,sha256=MWSMzdJZLiB4uLqHlAuL4ih-At3C22KGavV1xw4qx8A,28640
transformers/models/vision_text_dual_encoder/modeling_vision_text_dual_encoder.py,sha256=sGQQSzbewqChnIrWjXsXpqE2PpjbXyfYvwdTTf9EyH4,25199
transformers/models/vision_text_dual_encoder/processing_vision_text_dual_encoder.py,sha256=Wxw-ShdBxDkWK76hcJjHrvySp-uW0yrTvoqWouovhy8,6929
transformers/models/visual_bert/__init__.py,sha256=C2L_RDRTsnoWLC-FeN3qDuCKqaf2uf6baAydgUNZ4Lc,2039
transformers/models/visual_bert/configuration_visual_bert.py,sha256=mhddUhQY3OOqrTTsqh0W77b6oI_Mk0ipvSR7M30KSNg,6734
transformers/models/visual_bert/convert_visual_bert_original_pytorch_checkpoint_to_pytorch.py,sha256=jaKViPxrFh302Z0muKnQovpjLlenroT1RwXeXBGVh5Q,5157
transformers/models/visual_bert/modeling_visual_bert.py,sha256=MCZQ94U_DA5MHi8kOFekj1UhCaXldgOLqJ-avFNXmYE,68915
transformers/models/vit/__init__.py,sha256=1UCVk3-29ZnEgXRs-YlLMgOrxbmyt4zDbi3aWqZduls,3929
transformers/models/vit/configuration_vit.py,sha256=8wqeZmlMXppMsu0soRhz7ncm6ii3S4IwT79ohP2gxbM,5612
transformers/models/vit/convert_dino_to_pytorch.py,sha256=_lGR0qDnyjBxr0BtxibxxpSwo71za6iKt-CJ2cKXpHI,8853
transformers/models/vit/convert_vit_timm_to_pytorch.py,sha256=vZPsceKZQo8OOVaVEMUoy5HAM7KJFW4vG7DSqynVp_c,10889
transformers/models/vit/feature_extraction_vit.py,sha256=R-W_HNOybSpKxKGKfo4iDB4zGTRHeW1cq-29iwnbVl4,1165
transformers/models/vit/image_processing_vit.py,sha256=PvZDjJEJkYLBReAobMI407eNGg3lGjIDpqNDSA1UVkY,13748
transformers/models/vit/image_processing_vit_fast.py,sha256=NUGVEvFQiwA8iOJ5eCWqMKXw8j6Ly3CzPgRcCgsv3tg,13164
transformers/models/vit/modeling_flax_vit.py,sha256=KsTqlse5b5euRgYXhrXoNqCNvo0LEPBGuU_b0uNO0yo,25340
transformers/models/vit/modeling_tf_vit.py,sha256=RorkNKdk3rzuKAI0g01rEciEGphZMdNzPkKIxpxUQWU,37326
transformers/models/vit/modeling_vit.py,sha256=ZoP9mZqEjAJCuBuUV-dJubGblGOjmUikAdOWEAOg2Oo,37977
transformers/models/vit_mae/__init__.py,sha256=7qPTFWY_icEM8R7T_0JmqckCR_KBrW6PYLkyMjFNiJk,2248
transformers/models/vit_mae/configuration_vit_mae.py,sha256=dZOTKc4uXQoFPRIzyMD7pEvCtJow4r98enXIwLjdtdY,6343
transformers/models/vit_mae/convert_vit_mae_to_pytorch.py,sha256=Nj4Y5LS8H7xbyWNeLE9Vn0NFyXSQQYEcj1QQMzN1Hdg,7516
transformers/models/vit_mae/modeling_tf_vit_mae.py,sha256=HCJwfvgg-chHbrM4MKyKMkhGaqVejGb5_oD5aoAzxms,57945
transformers/models/vit_mae/modeling_vit_mae.py,sha256=Cm_DLNE8m8qL-f-iTGK2b9IBTTH9UdcXQKbjqrCS8zk,51194
transformers/models/vit_msn/__init__.py,sha256=7t6WI5W0Ny3qSJYUMxr567LtrB2wTvoZJCDrdHabKLw,1603
transformers/models/vit_msn/configuration_vit_msn.py,sha256=X__EgNtTLyfKHKrdifVM2GttFCCRwO83TJItk86kjdw,4835
transformers/models/vit_msn/convert_msn_to_pytorch.py,sha256=1xBjqvbviFkGxhi_xq2956R7qZpFEBdKPNOQYb-SoIA,9841
transformers/models/vit_msn/modeling_vit_msn.py,sha256=rVjzG9RJry34NqW3mtIc5my6JcXeGZcZydf6qlPN2C0,32334
transformers/models/vitdet/__init__.py,sha256=JlPOjtIlbaZxS-kmjh0dEuCHQBbRyo5Gbb4_KY8CCV4,1588
transformers/models/vitdet/configuration_vitdet.py,sha256=KYDwqi6EGkqngqGgfRIughg5JEM_8EfbK4WwZk2pLq0,7512
transformers/models/vitdet/modeling_vitdet.py,sha256=NVyVuu2-DyApNsWPcuzxswdEIbMC5pPmDmnZQl84-8o,34824
transformers/models/vitmatte/__init__.py,sha256=vW-SReLeVNo8Th8-Xt4Tv3zBgbsXH_R0v71rThH4hVg,2055
transformers/models/vitmatte/configuration_vitmatte.py,sha256=Li26IpLl-PBoWMjqNgIGjZ6fTGXwSKfcq2aVOyhiUkc,6238
transformers/models/vitmatte/convert_vitmatte_to_hf.py,sha256=1xctm78nmCLelPMqGJepxSyq5saKgA4by5CTzyxRPvc,6404
transformers/models/vitmatte/image_processing_vitmatte.py,sha256=X1Pp25hvOIbYuLsE3keiUhFLqh_BddhKkLGn9IBCt4c,13434
transformers/models/vitmatte/modeling_vitmatte.py,sha256=WUnanwEVuOPVj3X77sKJ7HFCRaXAv9GeyZsfCmE3u4U,12986
transformers/models/vits/__init__.py,sha256=vlwwg8NeCwRv6rx5z2MuHwssqLSQQCt-D3djFKWpK8c,1688
transformers/models/vits/configuration_vits.py,sha256=CuB53I2kKfMvasGrCGc6qUcghy-gcpr4YlEoX2CLO48,13857
transformers/models/vits/convert_original_checkpoint.py,sha256=N6rRzBaJlMxRwT7u33kUyJKy-4fFTWTD6nu_RTTOGt0,18610
transformers/models/vits/modeling_vits.py,sha256=Y3ubtpo_6Wn00q2VuXXP5kgHnJNfCntOufiQXXwoE-A,66511
transformers/models/vits/tokenization_vits.py,sha256=lQUpEVWexDtbDD81NftfTwl031VsdPyp8qKtzlqDMHw,9358
transformers/models/vivit/__init__.py,sha256=rjAmFZl3VXvsc_Hp8DLz-S487KwXCPg-v11KLshJkWQ,2269
transformers/models/vivit/configuration_vivit.py,sha256=s6k0V9KrKa6q0fTzGNjMDxJLsygNmOwDsPl-v6siz6w,5114
transformers/models/vivit/convert_vivit_flax_to_pytorch.py,sha256=7lqPLrfC2tORMS69USynZGlVZBcPv1Ljzt7UO68Zu9w,9112
transformers/models/vivit/image_processing_vivit.py,sha256=erq3MP-jJ0jTYlueFo2P7Tm8m3XAx1PRe2XID3Q5APo,19039
transformers/models/vivit/modeling_vivit.py,sha256=BnyRcbTaUwzROE5ZnyTVwttncH_y0MaPW536wMlGXPw,35497
transformers/models/wav2vec2/__init__.py,sha256=2AQvLWHNJ3KqZVFlLoR-fzK9Ipgr2LIgS2HmF0upA9s,3829
transformers/models/wav2vec2/configuration_wav2vec2.py,sha256=QCG-isZugbWEXlDpwbPGL3Z4C85dV_M2tZf4776rVHg,20045
transformers/models/wav2vec2/convert_wav2vec2_original_pytorch_checkpoint_to_pytorch.py,sha256=d3WhsQr53BCVEpENSK0N0-lWCktnul2pTyx9O2n1KV8,15166
transformers/models/wav2vec2/convert_wav2vec2_original_s3prl_checkpoint_to_pytorch.py,sha256=xtC7g9fzjpjN-Zeuk2MjhrJsSbn6hs1jhT5evZvtNUM,4837
transformers/models/wav2vec2/feature_extraction_wav2vec2.py,sha256=cqA2EbclMP-afVlboQVwNNAbmFdrru5kGvPbGjd8EaY,11559
transformers/models/wav2vec2/modeling_flax_wav2vec2.py,sha256=XyuYnXFM6om9uhmxleAug4SXyFtxshimUwYcAjjDgTE,57330
transformers/models/wav2vec2/modeling_tf_wav2vec2.py,sha256=KD1YEUtXnsy8-UdNFoRdup4-e1Fjt_vOFT0u0bDzPno,78614
transformers/models/wav2vec2/modeling_wav2vec2.py,sha256=2RLSI4qV6ik5nU5HMZvPsgLgmijVNCPTkWj-pFKKeQQ,120751
transformers/models/wav2vec2/processing_wav2vec2.py,sha256=Ud9dE_egFdjsOsPsA2gVq0cYWC7upfcIkWEnsFSNOnw,7704
transformers/models/wav2vec2/tokenization_wav2vec2.py,sha256=xgOLyf5lcZSGNVZ_sECKzYJxDPgRdG3jpGTV6O8hDCU,38741
transformers/models/wav2vec2_bert/__init__.py,sha256=4kJuF03qjYji0pDR42X-E52MqR60fAidlmqlshLhPyg,2065
transformers/models/wav2vec2_bert/configuration_wav2vec2_bert.py,sha256=0L5glQSdlObZoUVFMrMYk390H-JwxxQK3nJ6pZiqnzU,18075
transformers/models/wav2vec2_bert/convert_wav2vec2_seamless_checkpoint.py,sha256=JU4IQi_3dgf_j_foK5JvpxCbk0ZMFbE9wAaA-WRnQ9s,7419
transformers/models/wav2vec2_bert/modeling_wav2vec2_bert.py,sha256=b1MdHRY83wOPrHGpF0dOsAgJZi5HT5f1dmgh74bqHSs,74546
transformers/models/wav2vec2_bert/processing_wav2vec2_bert.py,sha256=fD46zQOtnF4OjY_uFNGXm9lbq_PCSYfe-9vlXiQrQyc,7844
transformers/models/wav2vec2_conformer/__init__.py,sha256=ap1FmrO8hw48sMklmLMm-WlQKo8u6aChgnvG3zLXvyI,2120
transformers/models/wav2vec2_conformer/configuration_wav2vec2_conformer.py,sha256=noOz_He-kp-tvs5iCq3w_FEHwjjbk8H6AcwVLENTs94,20874
transformers/models/wav2vec2_conformer/convert_wav2vec2_conformer_original_pytorch_checkpoint_to_pytorch.py,sha256=gJvDTMypi4dwCM4RN4N7Zh8W1ZnIk2aYa_VtO4uarQo,13381
transformers/models/wav2vec2_conformer/modeling_wav2vec2_conformer.py,sha256=hUsTG2NjJNbhQexp2SYcFDylsOHTJDiUf_Xww7uCYtU,95929
transformers/models/wav2vec2_phoneme/__init__.py,sha256=E2xRyViyzCISV8XE7YQ1gx5Wlx9_ACoPDB6ZZEm9bWo,993
transformers/models/wav2vec2_phoneme/tokenization_wav2vec2_phoneme.py,sha256=kF6VHvIC7r3TdlbHayUbWBlq7MEei_hwqhz65e9p5Cg,23161
transformers/models/wav2vec2_with_lm/__init__.py,sha256=d_lvk8QAia4BIKN7d_Uy3HdRqrDp_ZJHTDZ-nkHKwPA,981
transformers/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.py,sha256=vBudrF2AwNRurSzdIS46l4T2rXhlFvFWpMsTDJOOZ58,30000
transformers/models/wavlm/__init__.py,sha256=PrI2Y7NGaErI2zAdqWvlcqk3xpQ5Ve0H6k3uOrw_W6w,1787
transformers/models/wavlm/configuration_wavlm.py,sha256=vUo373Hhq14_GYvQYT97zhT18ZPBFy08p7Bjp2Y8eL8,18536
transformers/models/wavlm/convert_wavlm_original_pytorch_checkpoint_to_pytorch.py,sha256=dYZIX8q3-JQ-LkhggxjbMWPjixu_ZpONwXqQRu9ImjQ,8579
transformers/models/wavlm/convert_wavlm_original_s3prl_checkpoint_to_pytorch.py,sha256=pMXFACce5UgAMK1uHdwI6ksoirFDr6G0OER6z1yFYFM,4813
transformers/models/wavlm/modeling_wavlm.py,sha256=TdDQS7YgQbAvqGHa8J0fhJc-Vn8fg2RGTG3B_GHRgXo,78878
transformers/models/whisper/__init__.py,sha256=e87C_luRTn1AFQiJMwAUhy9mZzOHY-Fxy5nTfkNNOag,4060
transformers/models/whisper/configuration_whisper.py,sha256=xBZbk7DY2SHfMQ9TNEEoxAs7dTjbcXeq4cT31sGNVac,16991
transformers/models/whisper/convert_openai_to_hf.py,sha256=e1E6mFHUwF4o3BvUhwNya-P5yGRGHcPCwdni4I0OdQo,14961
transformers/models/whisper/english_normalizer.py,sha256=MTJ16OhstprR2X8owfEJmONqkoSHHyzztENejmEhSBM,22822
transformers/models/whisper/feature_extraction_whisper.py,sha256=kqfVeqN5HoNOjvwSRVrknwRMbBmhFS2FIf_tCOE54tg,14895
transformers/models/whisper/generation_whisper.py,sha256=GaHEv4wNklUPBn47z0e3MagauR59GhLcXbJF4e_M5v8,95292
transformers/models/whisper/modeling_flax_whisper.py,sha256=heYV1oX9VfM6sz2UHFani-6yRTgUhWnyiBb2aRnyKJQ,73610
transformers/models/whisper/modeling_tf_whisper.py,sha256=Gk5jMed88BU6x_zeG3o9KYM6nGHyCuSFpzAc36IBM9U,84764
transformers/models/whisper/modeling_whisper.py,sha256=ZYe_pJqfEhTm1t4bJ9I_DJ6fecFX-Qlw6jmEQb0BrAk,108216
transformers/models/whisper/processing_whisper.py,sha256=_f1JxM_uoe5KJ9CuDNhTtVqJclcG4nA57RuYYviwmSI,3890
transformers/models/whisper/tokenization_whisper.py,sha256=w9drBcZ1SNZzVgtRUhqBnRj4gdJJQmnGExPTMroX8yc,56183
transformers/models/whisper/tokenization_whisper_fast.py,sha256=Oi8pJMubyZ9m7K5l9ZVWX2gP4P9TQSqD7J_e0QE2nyo,29564
transformers/models/x_clip/__init__.py,sha256=HNjpuri5BG5qGqwuhiAYO3WFetp9aclHm31FfYzKnMU,1865
transformers/models/x_clip/configuration_x_clip.py,sha256=Vgh0-scQiku0BQBWEDWivtcUbJWij4FFEaOY-1RTRCw,20308
transformers/models/x_clip/convert_x_clip_original_pytorch_to_hf.py,sha256=WzXe8IKqSz4Bi78EIvRA6C3QiLL4c-SpARggHjIWtt4,18066
transformers/models/x_clip/modeling_x_clip.py,sha256=NTdW12n315wRJhnsUCOO--rxe5MFZ-_srIBZeLvC9_w,72917
transformers/models/x_clip/processing_x_clip.py,sha256=vuwuN_pNagPMfdvGJrSbhQVTslOHBMGFgYV2xD9BHsw,6897
transformers/models/xglm/__init__.py,sha256=yCOfidBHseDwbodeG6oNldoDi3uRb7_WotolzI1eNjc,3615
transformers/models/xglm/configuration_xglm.py,sha256=FFh8lXfaYpMMXl3LpB3MgY7Bm0JgL9jRMGvmCXFPHO4,5846
transformers/models/xglm/convert_xglm_original_ckpt_to_trfms.py,sha256=9fjXP40nMFbiI9H0VV66Buqk9JQrPhAFERCOBYHl_7g,2325
transformers/models/xglm/modeling_flax_xglm.py,sha256=mO8pJ0ZwSq3YFyxEU5_iZbGQD_8bLYJZz09AzB6tZEg,33115
transformers/models/xglm/modeling_tf_xglm.py,sha256=58_Fj9tHgKJA9YsNk-MjTeQt4hirVhdgEuWtdjYfzqE,45275
transformers/models/xglm/modeling_xglm.py,sha256=tbdg0wBNYjlDywX1xYfFd59q3vDC-ldoOgJVDIhLFIs,37637
transformers/models/xglm/tokenization_xglm.py,sha256=oIF19hBaND3aIHnjkZRjhz7Y0172g0hYzdfH43LdEXo,12483
transformers/models/xglm/tokenization_xglm_fast.py,sha256=7Cicl8Ilnoq_RbwK136hhQgBiL6-tf0h3vjxifUwAzE,7588
transformers/models/xlm/__init__.py,sha256=BJ39L6oezmjY0dUv0pCCKJgCB-uvo7eBulJHjn5dIIw,3030
transformers/models/xlm/configuration_xlm.py,sha256=FWSszRdcATzwOjDYUhfkhlkxzUeS8x0o3pPgVqC85fk,11010
transformers/models/xlm/convert_xlm_original_pytorch_checkpoint_to_pytorch.py,sha256=WUF2ZQtwzejYGtF54ToSahenIRvAFxgtUZ5ckzQl9Hc,2933
transformers/models/xlm/modeling_tf_xlm.py,sha256=tMH3Tn8UQzzOh787HbwEbQ1X83dTCFXYlO7ueKv0x34,56410
transformers/models/xlm/modeling_xlm.py,sha256=SV379Wec5-7gFm72ljnzx__GroHoR41SOuMXf_Thob4,54810
transformers/models/xlm/tokenization_xlm.py,sha256=gLVAmEmhqwBkc6g1JUKkatD-bkFjE9mCG_z9MmHuIeg,24447
transformers/models/xlm_roberta/__init__.py,sha256=EBT6vyuHrYf1F9GWBJAyp-jhoHPFP_hwT-2fSYmoowA,5381
transformers/models/xlm_roberta/configuration_xlm_roberta.py,sha256=6wwemXvnEbXKY3bERbWUEJMFCqAQTGd_cc04SDrPuew,7514
transformers/models/xlm_roberta/modeling_flax_xlm_roberta.py,sha256=OjstH2WR6oioiTGvUT6HwEvJxkOx24iDN2cUC8Py8BQ,58445
transformers/models/xlm_roberta/modeling_tf_xlm_roberta.py,sha256=ltQYeMYT2z6uFn61EuBzN7d8ufmTtOox_wWSMcDDAoU,81822
transformers/models/xlm_roberta/modeling_xlm_roberta.py,sha256=RF7rZt98WRurtynAJx6Awm-u2nkC5bwe_O6CM2YX3Wk,79414
transformers/models/xlm_roberta/tokenization_xlm_roberta.py,sha256=vPcsKZVYDyWSeA48n7PkJZk5iANKIngyzpkzDV6dofY,12705
transformers/models/xlm_roberta/tokenization_xlm_roberta_fast.py,sha256=iPgRJmnSFPqIbf29saxZE--ZGApKLfsGx1VqxD4SfP8,7920
transformers/models/xlm_roberta_xl/__init__.py,sha256=1kabrrWXrbvGQNfyvEoFkAuxr3-YX3MHtJ-9J50D45Y,2181
transformers/models/xlm_roberta_xl/configuration_xlm_roberta_xl.py,sha256=JdiaUTf5JMNJiRcB4wfmUtyc8WJL0TzXJ3tktg6NR3g,7262
transformers/models/xlm_roberta_xl/convert_xlm_roberta_xl_original_pytorch_checkpoint_to_pytorch.py,sha256=zVa6azx9rd33D3JkH2uqJ6W20TosJyWi9eLm3LNtc5U,8228
transformers/models/xlm_roberta_xl/modeling_xlm_roberta_xl.py,sha256=1CaOIWYoOUUCuAVmuqUnZrW1YoLX8bfQDJ9KIbKVLyI,77073
transformers/models/xlnet/__init__.py,sha256=oQijY83K76LLKusrEiTIHmXp_47NeIwFobmYaWN-TD4,4014
transformers/models/xlnet/configuration_xlnet.py,sha256=MOnMVQJHTsUnr-MhJmX8ykvQT5Gh6m60O9MxgVetlP8,10925
transformers/models/xlnet/convert_xlnet_original_tf_checkpoint_to_pytorch.py,sha256=vabPDgM_imZsTVqlUq_YrKElpWhEpiNf8xkzShmgH6o,3687
transformers/models/xlnet/modeling_tf_xlnet.py,sha256=LsyGRSTR0AnPcVdwwkbhWX6SMMyeWl7QMsEH4Y5GrKg,77603
transformers/models/xlnet/modeling_xlnet.py,sha256=0zSvyQyFcyLo9-4a1l3fkRQ3cpzcQgcQtQaT-hxMDyo,92966
transformers/models/xlnet/tokenization_xlnet.py,sha256=SQXMFFbN5yjD08_xoAt5l1Ruv_-e9c2Ys82oWv0Punw,15701
transformers/models/xlnet/tokenization_xlnet_fast.py,sha256=UFRytSMjW-vIZ_3Kt8acm2aNOp70i-msJibfBHOI9Sw,9364
transformers/models/xmod/__init__.py,sha256=hc6cAUa8-JQkHCPfgyOnqbXvJ9dD_EHGUl5tTbB6Cts,2149
transformers/models/xmod/configuration_xmod.py,sha256=qD2eTUuRecfe4opPyy3uEoOnUqU_3B9wbKkEOrngBjs,9110
transformers/models/xmod/convert_xmod_original_pytorch_checkpoint_to_pytorch.py,sha256=yFSAtXjxbAy6uXBg2XinRbk3VSEBOsWj1ugBhVNrGjQ,9859
transformers/models/xmod/modeling_xmod.py,sha256=xf3wXfLAtHa53Bk3Ib6DaBWfNWEZdkdv2n3pQERa8PA,75137
transformers/models/yolos/__init__.py,sha256=lWuTOt7yUlqdzoN5rO3GnI0Oz26hHgh54BW3fOmJur8,2228
transformers/models/yolos/configuration_yolos.py,sha256=9WA88-q7oO8vat3f0nJr72UObDTJpMQjFCj1ACAdZM0,7571
transformers/models/yolos/convert_yolos_to_pytorch.py,sha256=dDZ8lJdoh8FEi6iujUPGhnr-ejbcTGQIIRUB2c7WA8c,11258
transformers/models/yolos/feature_extraction_yolos.py,sha256=0ebN1Be4y86C2yyN2rMQ9AbguEDjcQ7fkabropUpwcs,1481
transformers/models/yolos/image_processing_yolos.py,sha256=MHyyUno6Oc_ohIcaZWDQjUZnPIqLSpqWl98p0d3bCoQ,67891
transformers/models/yolos/modeling_yolos.py,sha256=DV_NjrjBG6R4OAKIgD6wNZNvLR1HBXmNttvOqsyyIy4,39126
transformers/models/yoso/__init__.py,sha256=NjOBUKuRta4m_jCzNmY0nT7RT53ZnXh-ucOjp0CB7ww,1906
transformers/models/yoso/configuration_yoso.py,sha256=LqHGF4Au5VTYXgGw0X9KAlldG-bqjqE48pvQMV5iAxw,6688
transformers/models/yoso/convert_yoso_pytorch_to_pytorch.py,sha256=VjPOSLINfkiaHx8M3dTNMdC8hXh3M1yyhIQ9t4Vzqk0,4115
transformers/models/yoso/modeling_yoso.py,sha256=BWwP4J-OFSW6NIBEaFhT84oS9_ATxWE75C6vigYfJhk,54745
transformers/models/zamba/__init__.py,sha256=VOPAxPSkA1r30okibA3VBPYpU0NDKOA80Cipx33Qj-Y,1660
transformers/models/zamba/configuration_zamba.py,sha256=r9md2jK9WjMt3pBNOUp0aIUgMwHzKwSc38wFeo0hWNM,11260
transformers/models/zamba/modeling_zamba.py,sha256=qVU_gbzddJrby1Mz6EuKpr59GS09tvGLQPlxCRlOwEs,80541
transformers/models/zoedepth/__init__.py,sha256=eGStAGKPksn9bnl7J6UdMlegf97Xm0khGgrQJ5HupKs,2148
transformers/models/zoedepth/configuration_zoedepth.py,sha256=SFUH_TQgkcFJM_f482nBbEazK_QEYPmDHLGM22hROCo,12684
transformers/models/zoedepth/convert_zoedepth_to_hf.py,sha256=STtF2DGuZvFgSGpeDj8uNQRadiLQ0h17HvoKDD5TdFQ,18075
transformers/models/zoedepth/image_processing_zoedepth.py,sha256=acBXdFf84nR10iIB3zYao0cjU0ec2m18ca3lsJeNXrU,28037
transformers/models/zoedepth/modeling_zoedepth.py,sha256=kJebj0hzmnzdCkjbBwFW6W-f2KjCljtJZwCSHTbOPZ4,57201
transformers/onnx/__init__.py,sha256=wALLY4TPOK2iPrFcfZf_WiEmTRAU6dAWHElxGdexr58,1548
transformers/onnx/__main__.py,sha256=JZ9ZmeRsnDitwTMWb-dFT8W9AEmMoMKLQ3SvbyCkY0w,9497
transformers/onnx/config.py,sha256=zPDgC_HSLmMeqPkcLv_Y8EfbfLLEDLqPrvrfQCRyhl8,32556
transformers/onnx/convert.py,sha256=ZSh9jQE6B6cCxhlSbKLHxNmj48HkXXdl-HF7iGtZy5k,19369
transformers/onnx/features.py,sha256=GSuwZj760THxAkDmJYROt43La0GaY-bA19j2bE-XYVI,28264
transformers/onnx/utils.py,sha256=39Uw_GkFBsTb6ZvMIHRTnI289aQDhc6hwfEapaBGE-o,3625
transformers/pipelines/__init__.py,sha256=yeubhgfB0f-ubD5cQxE98gQbKc1Ym8Xwe-qqO4cf1HM,54672
transformers/pipelines/audio_classification.py,sha256=7l7K7qDap9OJagv1KDROqvismyyXmBHbYjUcqeFhSwQ,10000
transformers/pipelines/audio_utils.py,sha256=scYCl1Y762qyjKJ3IBY9tCzvcW--hXxV9mRnFU9Tsb4,12283
transformers/pipelines/automatic_speech_recognition.py,sha256=nD7lU1A-C_Sr5jN6FeEe0N5rtWGqcmVquyVYXUioNz8,39019
transformers/pipelines/base.py,sha256=OES_MZUDzhGqmTZi27XxaAmzTy1EIHCDtiND9fP9fYE,60135
transformers/pipelines/depth_estimation.py,sha256=pmo3E6w6Ozgk-Ta_DyWYQZgXR66s8lejtTENdbsSUHs,5702
transformers/pipelines/document_question_answering.py,sha256=B5AAPgl3rmY2ePtPeVmrHNt_eSjqM9nc2l50cUada9E,23948
transformers/pipelines/feature_extraction.py,sha256=Ar_hPljY1Fa_xAsRYX4cCCss1vf-iC5uuKYHp3rejd0,3374
transformers/pipelines/fill_mask.py,sha256=t2SwI6R-8jdnh5_XENMOXGHqkg2S6r1NEWpkHbnc43I,11646
transformers/pipelines/image_classification.py,sha256=eI94goIiAqosJ9atkmYBfj0gT-gMSqc_KTX80CezhoU,9735
transformers/pipelines/image_feature_extraction.py,sha256=KIn3yldAUTxFmal-SNF7CPkcx1h3q_KMvyjUsHzsx9o,4732
transformers/pipelines/image_segmentation.py,sha256=roAU9gb0kndD7niajzpVvJx0FMfOl_baqNSOssRqqb0,9568
transformers/pipelines/image_to_image.py,sha256=VHB7ElQIYrBGPQsD5QtTZ0p4piXJ_r13KU4fkWgbLms,5022
transformers/pipelines/image_to_text.py,sha256=pm9XgXhoC2XWCBdteu5x9c9QBAIrwGiSFhO3_irjYuk,8908
transformers/pipelines/mask_generation.py,sha256=HRmUx-H9pl85go26t70Th7lsPQu_nDdnHgi62FkKL-s,13204
transformers/pipelines/object_detection.py,sha256=whsGNSF8M_zJkvsxi_mTnC1456R5kmyiRp5u-3zRq1o,8173
transformers/pipelines/pt_utils.py,sha256=D-cFFKAaVtn3jaZGPKFr-U3JF3_YR5H3kO4QD1jrqQY,12762
transformers/pipelines/question_answering.py,sha256=JJnyXpsKkZNKCZhj6xzBYfClcivquXVZBBXn08dqPeA,30031
transformers/pipelines/table_question_answering.py,sha256=ZC2O-dSqRyCHwpx4LsM9KO_cHYlvgctRTzsZZM71KvA,20059
transformers/pipelines/text2text_generation.py,sha256=cvIRd80Lnd8PickAvPO8m6cnLdi0J7ahXNui4KmgKAU,17408
transformers/pipelines/text_classification.py,sha256=x2aqpPTMHnlxl7UaCiF7svNYFvGYQaLXFck0wjuREkM,11044
transformers/pipelines/text_generation.py,sha256=v7yQgtmxUlYWh-Rntwn8sqRVA17ErvSYfw5L2qL6Y6Q,22398
transformers/pipelines/text_to_audio.py,sha256=1aQ12TRzoR8d5FEMCf3FciDoi4AHOFU1OXdFxfb1640,8568
transformers/pipelines/token_classification.py,sha256=h0GzOec0wAEofnGukueLcP8QHB14r3b4ne9UaFVpcSc,26943
transformers/pipelines/video_classification.py,sha256=H7Utj2UBVjRIblmJlF86koR8LYj9NMgcH24L4Qrvspc,7833
transformers/pipelines/visual_question_answering.py,sha256=OZ_KLB35FFjKp0KDT8tdlNySiCkbJMqRZUmZrMH08NA,8853
transformers/pipelines/zero_shot_audio_classification.py,sha256=yxjBnd1f99GoJYOE51n8JhUiMRmkotMiFn-2uLnSsPo,6869
transformers/pipelines/zero_shot_classification.py,sha256=fBqB7-aNsKCk0nKMQPuiGFVb6HWxzAp8K-geg89-F9Q,12500
transformers/pipelines/zero_shot_image_classification.py,sha256=MfQyS3P4QVKrE5WXxnfosNHRUp2JDUOOVA25fuehKG4,7755
transformers/pipelines/zero_shot_object_detection.py,sha256=nvabAvHT3nvu-gd3962aQsYSHaQyw7SQDJmCsLlD8sg,9580
transformers/quantizers/__init__.py,sha256=hCprQnoI20-O1FSMSRgD-P9_NKEzN7kEfY66_BrQxz0,699
transformers/quantizers/auto.py,sha256=HRxlks48Cp2d01dTP-qqWrWTsQMUnucFqsJ4K0BrC4M,7756
transformers/quantizers/base.py,sha256=1VabMptBYWO9oCf6hmWz9DHlMOGUSp6VLZqXNxyvQgM,10197
transformers/quantizers/quantizer_aqlm.py,sha256=khCb8mQ2T0tYPwCJqA7wkO6_59R2ChtHHnwQJzhfABc,3692
transformers/quantizers/quantizer_awq.py,sha256=FM27RGxHazRTIN2o2qtl4Q6b10tE-FNGKlEOI8aoQIc,6425
transformers/quantizers/quantizer_bitnet.py,sha256=CemDWbQrsFSlay_VK0fumF5EKPo6QsCEzJihkbC8PZg,4302
transformers/quantizers/quantizer_bnb_4bit.py,sha256=izKg-NiYypgBaSsm5tCWjm49PjFyx886Q_rAewzGx9Q,16028
transformers/quantizers/quantizer_bnb_8bit.py,sha256=KE4Vj-l1gFzmL1dIiOpCvgEFPtg6v3Bsa1JwoSpbndc,13894
transformers/quantizers/quantizer_compressed_tensors.py,sha256=LKoSJOvpH6_2Bdvy3awAw_VzecSxwJXLavQ3_HVQP8o,2947
transformers/quantizers/quantizer_eetq.py,sha256=EuKzFhksrYYJuSXBMgmZotPiTpjIYSLEr9mgujKJ8ys,6587
transformers/quantizers/quantizer_fbgemm_fp8.py,sha256=i2Ad9lj4jZtwjd3nDRgLXy5I35qYYgItuZ8AwNSQlSo,8142
transformers/quantizers/quantizer_gptq.py,sha256=z1qqsavIfrWS7mM0OzEuEcUXFjBMsHSuCg8Bpmv_Id8,3889
transformers/quantizers/quantizer_hqq.py,sha256=-9GP6MPqtRDdLLundZmpgkppjWZ9WFWFAO13xCttDHw,11438
transformers/quantizers/quantizer_quanto.py,sha256=H7hfgC8nZ_P_SWjcSI-b9ZQtBGhGjGC9gIK55gIAN4M,8675
transformers/quantizers/quantizer_torchao.py,sha256=1vk6GyEplsFwXqOy64SWl722B7sZgArisaqeTulmD4g,7414
transformers/quantizers/quantizers_utils.py,sha256=6bgmf8mLxow6gXonTFX7PLfqFsf6plUj7DOeXnXhwMM,1066
transformers/sagemaker/__init__.py,sha256=fKtKAHamz_CLL9jPGCa2E-1n8RmuS-58qGtzZuKc3qg,730
transformers/sagemaker/trainer_sm.py,sha256=7GsKLtjdMfKp98OwHD7RcBsl745OOwHAaBswkfLkfsE,1044
transformers/sagemaker/training_args_sm.py,sha256=4ZnQhITfMwT0y2Y2MvkI11PEB_yfTX5Z7WrPKt0VXD8,5389
transformers/utils/__init__.py,sha256=uiaaKdXEmjt8bFb2jxdIJ7EcdLihixGaQPM0qKvVBig,9133
transformers/utils/backbone_utils.py,sha256=BZJOavniwDKDkz_f7yD-m8ZGDUx-li5FwqVZtJjm3rM,17431
transformers/utils/bitsandbytes.py,sha256=LzOKwcHWAxxZZv-7Ts9Q0vlEYvHd18affVgVbiR3Tzs,1040
transformers/utils/chat_template_utils.py,sha256=TxkzDYhSgloROUQIR-JWe9NpYw8voTCr_L3Fi0D0m_M,16972
transformers/utils/constants.py,sha256=sZsUwOnA3CbtN1svs9YoaNLTTsAc9RVaITsgpf8K4iI,282
transformers/utils/deprecation.py,sha256=1XHELEE0sqQkrB_FhtBcwAjHk5ETcLVCsEeakZa8Jdw,7614
transformers/utils/doc.py,sha256=1oAZT_lRqLlvI-tL9BCd_whVOvGCN1dub8b5Z85n3GA,41001
transformers/utils/dummy_detectron2_objects.py,sha256=n7Pt_7sbVBNfohKGcOARB-ZcPcJRbjEAcoLd2vTXndU,340
transformers/utils/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.py,sha256=n6pY4s7zCII3dzo7Ejd0RviHa_pMateuDEwbbHgsTUY,902
transformers/utils/dummy_flax_objects.py,sha256=-iOHQAjQUx2S0_lpUA2REHQUULUmox2iEINyBjM3xn0,34003
transformers/utils/dummy_keras_nlp_objects.py,sha256=AVWt2orICCUXi754bkavvqPzYO91PjER-FlUZAw2jZc,294
transformers/utils/dummy_music_objects.py,sha256=1lxIebYUOdHJWMQ_T5IQgPgcO_wp_8YM_HGc3skuGVg,458
transformers/utils/dummy_pt_objects.py,sha256=vEIuDj7uzPa6hB5Lo2RS8HZEofmvkHxrdbgKoqO6bqY,242675
transformers/utils/dummy_sentencepiece_and_tokenizers_objects.py,sha256=BgPLr8Wz8A-17K86x04N21CKXtWNQLJEWx2c4aZRqaA,286
transformers/utils/dummy_sentencepiece_objects.py,sha256=pBykNNg9IPDeshVOeaw4sxHvgmt3by9X4rIQtz0ONYg,6455
transformers/utils/dummy_speech_objects.py,sha256=9eFm1cjdsYOPBoAz9JTgP35Bg8WF2C9AZ_y1hFpKZdQ,465
transformers/utils/dummy_tensorflow_text_objects.py,sha256=43V0IA2kb9gtuL0S1OL1eRFFxzQwKg4pPjMVuXUB5qg,306
transformers/utils/dummy_tf_objects.py,sha256=q_GvL4tCtlJjSpsuLbBt3uIw_4xdE9eU0V0x7rslRq4,66262
transformers/utils/dummy_tokenizers_objects.py,sha256=HW_eUXlwV3VPXxfSHSX3l4taOLbrajkziGUKTx8PCtE,11456
transformers/utils/dummy_torchaudio_objects.py,sha256=9A7Y4643_hTaqqZKlL-O524wRnrmNtODxisuDdO_7kU,488
transformers/utils/dummy_torchvision_objects.py,sha256=IJutS_GiJRCPu6fUJ4ejexaRHMBptdF5_9KmWFjXovY,482
transformers/utils/dummy_vision_objects.py,sha256=1gnpKLQAG57omUi4i2fNukyWii9BZU1W2ohb3Qrw4ZA,17265
transformers/utils/fx.py,sha256=rwUOW_FUb93exap6U6PIN5xZHU59fsfQihYs10bcn0M,57417
transformers/utils/generic.py,sha256=ddU1RhyY6Q_KQf71GLXRNWM8BSTGz_SuZooAjrkKM4o,28064
transformers/utils/hp_naming.py,sha256=vqcOXcDOyqbISWo8-ClUJUOBVbZM1h08EcymTwcRthc,4979
transformers/utils/hub.py,sha256=T11FihWc5B_ya9CH6KrF4s4DlzKIu78aNXMVefuIxPw,58035
transformers/utils/import_utils.py,sha256=nX9j7_vXyNz-aefzTK4NFk25zKLV9NiHQFrg8-K6RVo,79073
transformers/utils/logging.py,sha256=jhMbECpCDaMMjd6LEZC7Qys3wpFtsmZ0K3dCVB5ZLCo,12197
transformers/utils/model_parallel_utils.py,sha256=XbGU9IlFF59K_aplRxUGVnTfIZ9mpbLomKqQ08ooTew,2272
transformers/utils/notebook.py,sha256=ltSxM8qzQZ4GBlG5xgkthPLhZfhiKZOkHDiPNeKih_M,15592
transformers/utils/peft_utils.py,sha256=Jw6MjvVLtQ7booot0zK6X_xqRl_PAOh3lFZj1A2Guc8,5207
transformers/utils/quantization_config.py,sha256=6GbvY5RYxPOpJ9tDNMZzTU7A4KsP1F07VyzLxNy8zJk,60168
transformers/utils/sentencepiece_model_pb2.py,sha256=XiQs9uMEusfAZP6t6IBuTTX9yl7LiOyJEi7Ib-Wzmq0,50677
transformers/utils/sentencepiece_model_pb2_new.py,sha256=Is_lMJU8MlmXGTkRL-Ut9hDWJwEmYeXedPCHPFaqlwM,6622
transformers/utils/versions.py,sha256=C-Tqr4qGSHH64ygIBCSo8gA6azz7Dbzh8zdc_yjMkX8,4337
transformers-4.46.3.dist-info/LICENSE,sha256=d_1HEN757DwPYiWADgI18VpCWr1KiwNVkSf814JhIEk,11418
transformers-4.46.3.dist-info/METADATA,sha256=3J8WUxXqvVoK8Mrda9a0ZmYf4bvaUK_N9I2k7mWc8ic,44133
transformers-4.46.3.dist-info/WHEEL,sha256=P9jw-gEje8ByB7_hXoICnHtVCrEwMQh-630tKvQWehc,91
transformers-4.46.3.dist-info/entry_points.txt,sha256=kgdW_0F_tXNrWKSZXKWKeUD_LqVgcji9j7atGXve8z4,81
transformers-4.46.3.dist-info/top_level.txt,sha256=GLBaeTo_CSdhnHvbxQ0kzpEHdlLuA_33foIogaWxntI,13
transformers-4.46.3.dist-info/RECORD,,
