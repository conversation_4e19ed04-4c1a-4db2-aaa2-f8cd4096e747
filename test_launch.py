"""
Test de lancement rapide pour vérifier que l'application peut démarrer
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test des imports principaux"""
    print("🧪 Test des imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit: OK")
    except ImportError as e:
        print(f"❌ Streamlit: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL: OK")
    except ImportError as e:
        print(f"❌ PIL: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy: OK")
    except ImportError as e:
        print(f"❌ NumPy: {e}")
        return False
    
    # Test des imports optionnels
    try:
        import torch
        print("✅ PyTorch: OK")
    except ImportError:
        print("⚠️ PyTorch: Non disponible (mode démo)")
    
    try:
        import cv2
        print("✅ OpenCV: OK")
    except ImportError:
        print("⚠️ OpenCV: Non disponible (mode démo)")
    
    try:
        import mediapipe
        print("✅ MediaPipe: OK")
    except ImportError:
        print("⚠️ MediaPipe: Non disponible (mode démo)")
    
    return True

def test_config():
    """Test de la configuration"""
    print("\n🔧 Test de la configuration...")
    
    try:
        from config import UI_CONFIG, PROMPTS, IMAGE_CONFIG
        print("✅ Configuration: OK")
        return True
    except ImportError as e:
        print(f"⚠️ Configuration: {e} (utilisation config par défaut)")
        return True

def test_app_import():
    """Test de l'import de l'application"""
    print("\n📱 Test de l'application...")
    
    try:
        from app import VirtualTryOnApp
        print("✅ Application: Import OK")
        
        # Test d'initialisation
        app = VirtualTryOnApp()
        print("✅ Application: Initialisation OK")
        return True
    except Exception as e:
        print(f"❌ Application: {e}")
        return False

def main():
    """Test principal"""
    print("🚀 Test de lancement Virtual Try-On IA")
    print("="*50)
    
    tests = [
        ("Imports de base", test_imports),
        ("Configuration", test_config),
        ("Application", test_app_import)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        try:
            result = test_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name}: Erreur - {e}")
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 Tous les tests sont passés !")
        print("✅ L'application peut être lancée avec: streamlit run app.py")
    else:
        print("⚠️ Certains tests ont échoué, mais l'application peut fonctionner en mode démo")
        print("💡 Essayez: streamlit run app.py")
    
    print("="*50)

if __name__ == "__main__":
    main()
