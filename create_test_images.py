"""
Créer des images de test pour démontrer le système Virtual Try-On
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_person_image():
    """Crée une image de test représentant une personne"""
    width, height = 384, 512
    image = Image.new('RGB', (width, height), color='lightblue')
    draw = ImageDraw.Draw(image)
    
    # Dessiner une silhouette humaine simple
    center_x = width // 2
    
    # Tête
    head_y = 80
    draw.ellipse([center_x-40, head_y-40, center_x+40, head_y+40], 
                fill='peachpuff', outline='black', width=2)
    
    # Cou
    draw.rectangle([center_x-15, head_y+40, center_x+15, head_y+60], 
                  fill='peachpuff', outline='black', width=1)
    
    # Corps (torse)
    body_top = head_y + 60
    body_bottom = height - 200
    draw.rectangle([center_x-60, body_top, center_x+60, body_bottom], 
                  fill='white', outline='black', width=2)
    
    # Bras
    arm_y = body_top + 30
    # Bras gauche
    draw.rectangle([center_x-100, arm_y, center_x-60, arm_y+120], 
                  fill='peachpuff', outline='black', width=2)
    # Bras droit
    draw.rectangle([center_x+60, arm_y, center_x+100, arm_y+120], 
                  fill='peachpuff', outline='black', width=2)
    
    # Jambes
    leg_top = body_bottom
    # Jambe gauche
    draw.rectangle([center_x-40, leg_top, center_x-10, height-50], 
                  fill='darkblue', outline='black', width=2)
    # Jambe droite
    draw.rectangle([center_x+10, leg_top, center_x+40, height-50], 
                  fill='darkblue', outline='black', width=2)
    
    # Pieds
    draw.ellipse([center_x-50, height-60, center_x-5, height-40], 
                fill='black', outline='black')
    draw.ellipse([center_x+5, height-60, center_x+50, height-40], 
                fill='black', outline='black')
    
    # Ajouter du texte
    try:
        font = ImageFont.load_default()
        draw.text((10, 10), "PERSONNE TEST", fill='black', font=font)
        draw.text((10, height-30), "Virtual Try-On IA", fill='black', font=font)
    except:
        pass
    
    return image

def create_dress_image():
    """Crée une image de test représentant une robe"""
    width, height = 384, 512
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    center_x = width // 2
    
    # Corps de la robe
    dress_top = 100
    dress_bottom = height - 80
    dress_width = 120
    
    # Partie haute (corsage)
    draw.rectangle([center_x-dress_width//2, dress_top, 
                   center_x+dress_width//2, dress_top+150], 
                  fill='red', outline='darkred', width=3)
    
    # Partie basse (jupe évasée)
    points = [
        (center_x-dress_width//2, dress_top+150),  # Haut gauche
        (center_x+dress_width//2, dress_top+150),  # Haut droit
        (center_x+dress_width, dress_bottom),      # Bas droit
        (center_x-dress_width, dress_bottom)       # Bas gauche
    ]
    draw.polygon(points, fill='red', outline='darkred', width=3)
    
    # Manches
    sleeve_top = dress_top + 20
    sleeve_bottom = dress_top + 100
    # Manche gauche
    draw.rectangle([center_x-dress_width//2-40, sleeve_top, 
                   center_x-dress_width//2, sleeve_bottom], 
                  fill='red', outline='darkred', width=2)
    # Manche droite
    draw.rectangle([center_x+dress_width//2, sleeve_top, 
                   center_x+dress_width//2+40, sleeve_bottom], 
                  fill='red', outline='darkred', width=2)
    
    # Détails décoratifs
    # Ceinture
    draw.rectangle([center_x-dress_width//2, dress_top+120, 
                   center_x+dress_width//2, dress_top+140], 
                  fill='gold', outline='orange', width=2)
    
    # Motifs sur la robe
    for i in range(3):
        y = dress_top + 200 + i * 60
        draw.ellipse([center_x-20, y, center_x+20, y+20], 
                    fill='pink', outline='hotpink', width=1)
    
    # Ajouter du texte
    try:
        font = ImageFont.load_default()
        draw.text((10, 10), "ROBE TEST", fill='black', font=font)
        draw.text((10, height-30), "Virtual Try-On IA", fill='black', font=font)
    except:
        pass
    
    return image

def create_tshirt_image():
    """Crée une image de test représentant un t-shirt"""
    width, height = 384, 512
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    center_x = width // 2
    
    # Corps du t-shirt
    shirt_top = 150
    shirt_bottom = 350
    shirt_width = 140
    
    # Corps principal
    draw.rectangle([center_x-shirt_width//2, shirt_top, 
                   center_x+shirt_width//2, shirt_bottom], 
                  fill='blue', outline='darkblue', width=3)
    
    # Col
    collar_width = 60
    collar_height = 30
    draw.ellipse([center_x-collar_width//2, shirt_top-10, 
                 center_x+collar_width//2, shirt_top+collar_height], 
                fill='white', outline='darkblue', width=2)
    
    # Manches
    sleeve_width = 50
    sleeve_height = 80
    # Manche gauche
    draw.rectangle([center_x-shirt_width//2-sleeve_width, shirt_top+20, 
                   center_x-shirt_width//2, shirt_top+20+sleeve_height], 
                  fill='blue', outline='darkblue', width=2)
    # Manche droite
    draw.rectangle([center_x+shirt_width//2, shirt_top+20, 
                   center_x+shirt_width//2+sleeve_width, shirt_top+20+sleeve_height], 
                  fill='blue', outline='darkblue', width=2)
    
    # Logo/motif sur le t-shirt
    draw.ellipse([center_x-30, shirt_top+50, center_x+30, shirt_top+110], 
                fill='white', outline='yellow', width=3)
    draw.text((center_x-15, shirt_top+70), "IA", fill='yellow')
    
    # Ajouter du texte
    try:
        font = ImageFont.load_default()
        draw.text((10, 10), "T-SHIRT TEST", fill='black', font=font)
        draw.text((10, height-30), "Virtual Try-On IA", fill='black', font=font)
    except:
        pass
    
    return image

def main():
    """Crée les images de test"""
    print("🎨 Création des images de test...")
    
    # Créer les dossiers s'ils n'existent pas
    os.makedirs("data/persons", exist_ok=True)
    os.makedirs("data/clothes", exist_ok=True)
    
    # Créer les images
    print("👤 Création de l'image de personne...")
    person_img = create_person_image()
    person_img.save("data/persons/test_person.jpg", quality=95)
    
    print("👗 Création de l'image de robe...")
    dress_img = create_dress_image()
    dress_img.save("data/clothes/test_dress.jpg", quality=95)
    
    print("👕 Création de l'image de t-shirt...")
    tshirt_img = create_tshirt_image()
    tshirt_img.save("data/clothes/test_tshirt.jpg", quality=95)
    
    print("\n✅ Images de test créées avec succès !")
    print("📁 Fichiers créés :")
    print("   - data/persons/test_person.jpg")
    print("   - data/clothes/test_dress.jpg")
    print("   - data/clothes/test_tshirt.jpg")
    print("\n💡 Vous pouvez maintenant tester l'application avec ces images !")
    print("🌐 Ouvrez http://localhost:8501 et uploadez ces images")

if __name__ == "__main__":
    main()
