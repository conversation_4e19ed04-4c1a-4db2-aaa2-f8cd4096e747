# Copyright 2020 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from jax._src.lax.linalg import (
  cholesky,
  cholesky_p,
  eig,
  eig_p,
  eigh,
  eigh_p,
  hessenberg,
  hessenberg_p,
  lu,
  lu_p,
  lu_pivots_to_permutation,
  householder_product,
  householder_product_p,
  qr,
  qr_p,
  svd,
  svd_p,
  triangular_solve,
  triangular_solve_p,
  tridiagonal,
  tridiagonal_p,
  tridiagonal_solve,
  tridiagonal_solve_p,
  schur,
  schur_p
)


from jax._src.lax.qdwh import (
  qdwh as qdwh
)
