"""
Configuration principale pour le système de Virtual Try-On
"""

import os
from pathlib import Path

# Chemins de base
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
MODELS_DIR = BASE_DIR / "models"
OUTPUT_DIR = BASE_DIR / "output"
TEMP_DIR = BASE_DIR / "temp"

# Créer les dossiers s'ils n'existent pas
for dir_path in [DATA_DIR, MODELS_DIR, OUTPUT_DIR, TEMP_DIR]:
    dir_path.mkdir(exist_ok=True)

# Configuration des images
IMAGE_CONFIG = {
    "input_size": (512, 384),  # (hauteur, largeur)
    "output_size": (512, 384),
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp"],
    "max_file_size_mb": 10
}

# Configuration du modèle
MODEL_CONFIG = {
    "device": "cuda" if os.environ.get("CUDA_AVAILABLE", "false").lower() == "true" else "cpu",
    "batch_size": 1,
    "num_workers": 2,
    "learning_rate": 0.0002,
    "epochs": 100,
    "checkpoint_interval": 10,
    "input_size": (512, 384)  # Ajouter input_size manquant
}

# Configuration de la segmentation
SEGMENTATION_CONFIG = {
    "model_name": "facebook/detr-resnet-50-panoptic",
    "confidence_threshold": 0.5,
    "person_class_id": 1,  # ID de classe pour "personne" dans COCO
    "clothing_classes": {
        "shirt": 2,
        "pants": 3,
        "dress": 4,
        "skirt": 5,
        "jacket": 6
    }
}

# Configuration de la détection de pose
POSE_CONFIG = {
    "model_complexity": 1,
    "min_detection_confidence": 0.5,
    "min_tracking_confidence": 0.5,
    "keypoints_to_use": [
        "nose", "left_shoulder", "right_shoulder",
        "left_elbow", "right_elbow", "left_wrist", "right_wrist",
        "left_hip", "right_hip", "left_knee", "right_knee"
    ]
}

# Messages et prompts
PROMPTS = {
    "system_prompt": """
    Vous êtes un assistant IA spécialisé dans l'essayage virtuel de vêtements.
    Votre tâche est de combiner harmonieusement l'image d'une personne avec un vêtement
    pour créer un résultat réaliste et naturel.
    
    Instructions importantes :
    1. Respectez la morphologie et la pose de la personne
    2. Adaptez la taille et la forme du vêtement à la personne
    3. Maintenez un éclairage cohérent
    4. Préservez les détails du visage et des mains
    5. Assurez-vous que le vêtement s'adapte naturellement au corps
    """,
    
    "user_instructions": """
    Pour utiliser ce système :
    1. Fournissez une photo claire de la personne (face ou profil)
    2. Indiquez la taille de la personne si disponible
    3. Fournissez une photo du vêtement sur fond neutre
    4. Spécifiez le type de vêtement (robe, maillot, 2 pièces, etc.)
    """,
    
    "error_messages": {
        "no_person_detected": "Aucune personne détectée dans l'image",
        "multiple_persons": "Plusieurs personnes détectées, veuillez utiliser une image avec une seule personne",
        "invalid_clothing": "Type de vêtement non reconnu ou image de mauvaise qualité",
        "processing_error": "Erreur lors du traitement de l'image"
    }
}

# Configuration de l'interface
UI_CONFIG = {
    "title": "Virtual Try-On IA",
    "description": "Essayez virtuellement des vêtements avec l'intelligence artificielle",
    "max_upload_size": 10 * 1024 * 1024,  # 10MB
    "allowed_extensions": ["jpg", "jpeg", "png"],
    "theme": "default"
}

# Configuration de logging
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": BASE_DIR / "logs" / "virtual_tryon.log"
}

# Créer le dossier de logs
(BASE_DIR / "logs").mkdir(exist_ok=True)
