# 📖 Guide d'Utilisation - Virtual Try-On IA

## 🎯 Objectif du Système

Ce système d'intelligence artificielle permet d'essayer virtuellement des vêtements en combinant :
- Une photo d'une personne (homme ou femme)
- Une photo d'un vêtement (robe, maillot, 2 pièces, etc.)
- La taille de la personne (optionnel)

Le résultat est une image montrant la personne portant le vêtement, aidant à prendre des décisions d'achat éclairées.

## 🚀 Démarrage Rapide

### 1. Installation Automatique

```bash
python install.py
```

Cette commande installe automatiquement toutes les dépendances nécessaires.

### 2. Lancement de l'Interface Web

```bash
streamlit run app.py
```

Ou sur Windows, double-cliquez sur `start_app.bat`

### 3. Utilisation

1. Ouvrez votre navigateur à `http://localhost:8501`
2. Uploadez une photo de la personne
3. Uploadez une photo du vêtement
4. Cliquez sur "Essayer le vêtement"
5. Téléchargez le résultat

## 📸 Conseils pour de Meilleurs Résultats

### Photos de Personnes ✅

- **Une seule personne** visible dans l'image
- **Pose naturelle** : face ou profil, bras le long du corps
- **Éclairage uniforme** sans ombres marquées
- **Fond neutre** de préférence
- **Résolution minimale** : 512x384 pixels
- **Vêtements ajustés** pour voir la morphologie

### Photos de Vêtements ✅

- **Fond blanc ou neutre** uni
- **Vêtement bien étalé** sans plis
- **Éclairage uniforme** sans reflets
- **Vue de face** du vêtement
- **Haute résolution** pour les détails
- **Pas de mannequin** sur la photo

### À Éviter ❌

- Photos floues ou de mauvaise qualité
- Plusieurs personnes dans l'image
- Poses complexes ou bras croisés
- Éclairage trop sombre ou trop lumineux
- Vêtements froissés ou mal présentés

## 🎨 Types de Vêtements Supportés

| Type | Description | Conseils |
|------|-------------|----------|
| **Robes** | Robes de soirée, d'été, cocktail | Photo complète du vêtement |
| **Maillots** | Une pièce, bikinis, 2 pièces | Fond contrasté recommandé |
| **Hauts** | T-shirts, chemises, blouses | Bien étaler les manches |
| **Pantalons** | Jeans, pantalons, shorts | Photo des deux jambes |
| **Vestes** | Blazers, manteaux, cardigans | Veste ouverte de préférence |

## 💻 Interface Ligne de Commande

### Utilisation de Base

```bash
python cli.py photo_personne.jpg photo_vetement.jpg
```

### Avec Options

```bash
python cli.py personne.jpg vetement.jpg \
  --output resultat.jpg \
  --height 165 \
  --type "robe" \
  --verbose
```

### Options Disponibles

- `-o, --output` : Fichier de sortie
- `--height` : Taille en cm
- `--type` : Type de vêtement
- `--verbose` : Mode détaillé
- `--demo` : Mode démonstration

## 🔧 Configuration Avancée

### Modifier la Qualité

Dans `config.py`, ajustez :

```python
IMAGE_CONFIG = {
    "input_size": (768, 512),  # Plus haute résolution
    "output_size": (768, 512)
}
```

### Optimiser les Performances

```python
MODEL_CONFIG = {
    "device": "cuda",  # Utiliser GPU si disponible
    "batch_size": 2    # Traiter plusieurs images
}
```

## 🧪 Mode Démonstration

Si vous n'avez pas de modèle entraîné, utilisez le mode démo :

### Interface Web
Le mode démo s'active automatiquement si aucun modèle n'est trouvé.

### Ligne de Commande
```bash
python cli.py personne.jpg vetement.jpg --demo
```

Le mode démo effectue une composition simple pour démontrer le concept.

## 📊 Qualité des Résultats

### Facteurs d'Influence

1. **Qualité des photos** : Plus importantes que la résolution
2. **Pose de la personne** : Face ou profil donnent de meilleurs résultats
3. **Type de vêtement** : Les robes sont plus faciles que les ensembles
4. **Contraste** : Vêtement contrastant avec le fond

### Améliorer les Résultats

- Utilisez des photos professionnelles si possible
- Assurez-vous que la personne est bien centrée
- Évitez les accessoires qui cachent le corps
- Préférez les vêtements unis aux motifs complexes

## 🔍 Dépannage

### Problèmes Courants

#### "Aucune personne détectée"
- Vérifiez que la personne est bien visible
- Essayez avec une photo de meilleure qualité
- Assurez-vous qu'il n'y a qu'une personne

#### "Image trop petite"
- Utilisez des images d'au moins 256x256 pixels
- Redimensionnez vos images si nécessaire

#### "Erreur de mémoire"
- Réduisez la taille des images
- Fermez d'autres applications
- Utilisez le mode CPU si GPU insuffisant

#### Résultats de mauvaise qualité
- Vérifiez l'éclairage des photos
- Assurez-vous que le vêtement est bien visible
- Essayez avec des photos de meilleure résolution

### Logs et Debug

Les logs sont sauvegardés dans `logs/virtual_tryon.log`

Pour plus de détails :
```bash
python cli.py image1.jpg image2.jpg --verbose
```

## 📈 Optimisation des Performances

### GPU vs CPU

- **GPU (CUDA)** : 10-20x plus rapide
- **CPU** : Plus lent mais fonctionne partout

### Vérifier CUDA

```python
import torch
print(f"CUDA disponible: {torch.cuda.is_available()}")
```

### Optimiser la Mémoire

- Réduisez `batch_size` dans la config
- Utilisez des images plus petites
- Fermez les autres applications

## 🎯 Cas d'Usage Business

### E-commerce
- Réduire les retours de vêtements
- Améliorer l'expérience client
- Augmenter les conversions

### Mode et Stylisme
- Présenter des collections
- Créer des lookbooks virtuels
- Tester des associations

### Marketing
- Campagnes publicitaires personnalisées
- Contenu pour réseaux sociaux
- Démonstrations produits

## 📞 Support

### Test du Système
```bash
python test_system.py
```

### Informations Système
```bash
python cli.py --help
```

### Fichiers de Log
Consultez `logs/virtual_tryon.log` pour les détails des erreurs.

---

**💡 Conseil** : Commencez par le mode démonstration pour vous familiariser avec le système, puis passez au mode complet pour de meilleurs résultats.
