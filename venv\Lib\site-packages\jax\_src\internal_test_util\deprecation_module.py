# Copyright 2022 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from jax._src.deprecations import deprecation_getattr as _deprecation_getattr

x = 42
deprecated_y = 101


_deprecations = {
    "y": ("Please use x", deprecated_y),
    "z": ("Please do not use z", None),
}

__getattr__ = _deprecation_getattr(__name__, _deprecations)
