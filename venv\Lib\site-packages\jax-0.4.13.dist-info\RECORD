jax-0.4.13.dist-info/AUTHORS,sha256=OOBQygrI1Zc94AYJycH1r_BSCwP38Hyv3sludltJ7hY,313
jax-0.4.13.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jax-0.4.13.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
jax-0.4.13.dist-info/METADATA,sha256=d5TsyKRTFWLog28qerBnhh6P4-FD8iscu6X7FVEUt54,30247
jax-0.4.13.dist-info/RECORD,,
jax-0.4.13.dist-info/WHEEL,sha256=iAkIy5fosb7FzIOwONchHf19Qu7_1wCWyFNR5gu9nU0,91
jax-0.4.13.dist-info/top_level.txt,sha256=DabAh8MXQ-HM0EtjKD-Wx_D6tTIW-_de73pdG_f_KTE,4
jax/__init__.py,sha256=3Mbn8Xe4kRsRiM1pfrf8Loxv2pt-WTtGRN09CoUEn2Q,10050
jax/__pycache__/__init__.cpython-38.pyc,,
jax/__pycache__/abstract_arrays.cpython-38.pyc,,
jax/__pycache__/ad_checkpoint.cpython-38.pyc,,
jax/__pycache__/api_util.cpython-38.pyc,,
jax/__pycache__/cloud_tpu_init.cpython-38.pyc,,
jax/__pycache__/collect_profile.cpython-38.pyc,,
jax/__pycache__/config.cpython-38.pyc,,
jax/__pycache__/core.cpython-38.pyc,,
jax/__pycache__/custom_batching.cpython-38.pyc,,
jax/__pycache__/custom_derivatives.cpython-38.pyc,,
jax/__pycache__/custom_transpose.cpython-38.pyc,,
jax/__pycache__/debug.cpython-38.pyc,,
jax/__pycache__/distributed.cpython-38.pyc,,
jax/__pycache__/dlpack.cpython-38.pyc,,
jax/__pycache__/dtypes.cpython-38.pyc,,
jax/__pycache__/errors.cpython-38.pyc,,
jax/__pycache__/flatten_util.cpython-38.pyc,,
jax/__pycache__/jaxpr_util.cpython-38.pyc,,
jax/__pycache__/linear_util.cpython-38.pyc,,
jax/__pycache__/monitoring.cpython-38.pyc,,
jax/__pycache__/prng.cpython-38.pyc,,
jax/__pycache__/profiler.cpython-38.pyc,,
jax/__pycache__/random.cpython-38.pyc,,
jax/__pycache__/sharding.cpython-38.pyc,,
jax/__pycache__/stages.cpython-38.pyc,,
jax/__pycache__/test_util.cpython-38.pyc,,
jax/__pycache__/tree_util.cpython-38.pyc,,
jax/__pycache__/typing.cpython-38.pyc,,
jax/__pycache__/util.cpython-38.pyc,,
jax/__pycache__/version.cpython-38.pyc,,
jax/_src/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/__pycache__/__init__.cpython-38.pyc,,
jax/_src/__pycache__/abstract_arrays.cpython-38.pyc,,
jax/_src/__pycache__/ad_checkpoint.cpython-38.pyc,,
jax/_src/__pycache__/ad_util.cpython-38.pyc,,
jax/_src/__pycache__/api.cpython-38.pyc,,
jax/_src/__pycache__/api_util.cpython-38.pyc,,
jax/_src/__pycache__/array.cpython-38.pyc,,
jax/_src/__pycache__/basearray.cpython-38.pyc,,
jax/_src/__pycache__/callback.cpython-38.pyc,,
jax/_src/__pycache__/checkify.cpython-38.pyc,,
jax/_src/__pycache__/cloud_tpu_init.cpython-38.pyc,,
jax/_src/__pycache__/compilation_cache.cpython-38.pyc,,
jax/_src/__pycache__/compilation_cache_interface.cpython-38.pyc,,
jax/_src/__pycache__/config.cpython-38.pyc,,
jax/_src/__pycache__/core.cpython-38.pyc,,
jax/_src/__pycache__/custom_api_util.cpython-38.pyc,,
jax/_src/__pycache__/custom_batching.cpython-38.pyc,,
jax/_src/__pycache__/custom_derivatives.cpython-38.pyc,,
jax/_src/__pycache__/custom_transpose.cpython-38.pyc,,
jax/_src/__pycache__/debugging.cpython-38.pyc,,
jax/_src/__pycache__/deprecations.cpython-38.pyc,,
jax/_src/__pycache__/dispatch.cpython-38.pyc,,
jax/_src/__pycache__/distributed.cpython-38.pyc,,
jax/_src/__pycache__/dlpack.cpython-38.pyc,,
jax/_src/__pycache__/dtypes.cpython-38.pyc,,
jax/_src/__pycache__/effects.cpython-38.pyc,,
jax/_src/__pycache__/environment_info.cpython-38.pyc,,
jax/_src/__pycache__/errors.cpython-38.pyc,,
jax/_src/__pycache__/flatten_util.cpython-38.pyc,,
jax/_src/__pycache__/gfile_cache.cpython-38.pyc,,
jax/_src/__pycache__/iree.cpython-38.pyc,,
jax/_src/__pycache__/lax_reference.cpython-38.pyc,,
jax/_src/__pycache__/lazy_loader.cpython-38.pyc,,
jax/_src/__pycache__/linear_util.cpython-38.pyc,,
jax/_src/__pycache__/maps.cpython-38.pyc,,
jax/_src/__pycache__/mesh.cpython-38.pyc,,
jax/_src/__pycache__/monitoring.cpython-38.pyc,,
jax/_src/__pycache__/op_shardings.cpython-38.pyc,,
jax/_src/__pycache__/partition_spec.cpython-38.pyc,,
jax/_src/__pycache__/path.cpython-38.pyc,,
jax/_src/__pycache__/pickle_util.cpython-38.pyc,,
jax/_src/__pycache__/pjit.cpython-38.pyc,,
jax/_src/__pycache__/pretty_printer.cpython-38.pyc,,
jax/_src/__pycache__/prng.cpython-38.pyc,,
jax/_src/__pycache__/profiler.cpython-38.pyc,,
jax/_src/__pycache__/public_test_util.cpython-38.pyc,,
jax/_src/__pycache__/random.cpython-38.pyc,,
jax/_src/__pycache__/sharding.cpython-38.pyc,,
jax/_src/__pycache__/sharding_impls.cpython-38.pyc,,
jax/_src/__pycache__/sharding_specs.cpython-38.pyc,,
jax/_src/__pycache__/source_info_util.cpython-38.pyc,,
jax/_src/__pycache__/stages.cpython-38.pyc,,
jax/_src/__pycache__/test_util.cpython-38.pyc,,
jax/_src/__pycache__/traceback_util.cpython-38.pyc,,
jax/_src/__pycache__/tree_util.cpython-38.pyc,,
jax/_src/__pycache__/typing.cpython-38.pyc,,
jax/_src/__pycache__/util.cpython-38.pyc,,
jax/_src/__pycache__/xla_bridge.cpython-38.pyc,,
jax/_src/abstract_arrays.py,sha256=YnB0I7pDdrl-BjTQBZFEgt_diKEWYTIinCtHCFP6jAE,3229
jax/_src/ad_checkpoint.py,sha256=q5kMiB6dYgueqTKOXEoHit8T6zoe5HJtoVEwv3Hx3Ek,34763
jax/_src/ad_util.py,sha256=rLH09EWLguN3Mx6-6My0OVA8r47mZ0zK9fsV6Sap3mc,4009
jax/_src/api.py,sha256=jVnHWIdcvLZTDBUJ5oSR0xOMDfCGKabPihe55gV4roY,129408
jax/_src/api_util.py,sha256=X1m9MQWsy8mGzmKypuOHdqDYyTxBYPXH_DStTS-9Hzw,25205
jax/_src/array.py,sha256=vg2YjiBktypleKHW2sFoix3B2h2bdKsO-l6NEOwf8RU,26612
jax/_src/basearray.py,sha256=dhWhN_7PfXHomkAo3NdOTQ42bNB_96y9t5Q-XuqJ8Sc,2665
jax/_src/basearray.pyi,sha256=RV6gwuE9y-tfT1wj4EoiGIfFFqrNQwyNzGA84Ltryxc,10598
jax/_src/callback.py,sha256=QCotbQqY3oUrRRwi7OIN-71kn5ocrF6qaNGIN0rZqyw,16377
jax/_src/checkify.py,sha256=qeVRzt05DGgsUmD95RzU3aQUzZWYg-ovDHBe13_6MeQ,53070
jax/_src/cloud_tpu_init.py,sha256=_MZGfua1T9SqOwxCv2XW5TnRd3sn6NkmKNt1V_MngDE,2672
jax/_src/clusters/__init__.py,sha256=NPJVuAPVR1oyikfhx5dIMfhTybhoyEkhx2pzWyowNQI,1047
jax/_src/clusters/__pycache__/__init__.cpython-38.pyc,,
jax/_src/clusters/__pycache__/cloud_tpu_cluster.cpython-38.pyc,,
jax/_src/clusters/__pycache__/cluster.cpython-38.pyc,,
jax/_src/clusters/__pycache__/ompi_cluster.cpython-38.pyc,,
jax/_src/clusters/__pycache__/slurm_cluster.cpython-38.pyc,,
jax/_src/clusters/cloud_tpu_cluster.py,sha256=i4_JhJOvkzR4GTejlvxmAquWe0U4dLchthfJPZCNFcc,2456
jax/_src/clusters/cluster.py,sha256=IreIC5w_8Be9kjhUPt69YT3ZLTNUY7fhZyeIPi0nJtk,4774
jax/_src/clusters/ompi_cluster.py,sha256=T9UIKSuWvJWHj5otL5apd17ivnjzn1aweTTaUJgXRBw,2263
jax/_src/clusters/slurm_cluster.py,sha256=MZ4YyNkVLGXLLT7QITOWimK_ZH5nobsBHb5zxTs76eI,2266
jax/_src/compilation_cache.py,sha256=K6nbUiHXrBvKGLnpQza3duwPu98u9mPJ3v4tCX--axo,12837
jax/_src/compilation_cache_interface.py,sha256=xRXZ8RjI3kLnCDAxkBh8jvr_cN3e9Sk9E9CR1xkbrpo,831
jax/_src/config.py,sha256=_Q_vy-TIKwDLFNaWvG9zSSNwm4IIDLzOZ4ELvsMl6aE,51525
jax/_src/core.py,sha256=riOXbut5ioB_DsgxuaPARk7H2M8jwZN4c_gV4pHyIIk,115239
jax/_src/custom_api_util.py,sha256=uv6uyrXU1BHGzKuAe498W2PD9bcWt1PEa-8UAqr9Zaw,876
jax/_src/custom_batching.py,sha256=N690CQU4mr1AzJMh1aFjkS0Qjh3AFj6_rzaZPwn9yCI,10475
jax/_src/custom_derivatives.py,sha256=VFIftfBV0GRJ68x4RPNyIbJG2HP42jvwZjivnM4jzNk,58607
jax/_src/custom_transpose.py,sha256=qVBAjJyyFYx_6VPDiRG78Wj0rZbgUtF6wFvZ9TfZcIA,8294
jax/_src/debugger/__init__.py,sha256=VPrkm_4GZ-dm4SN7L4xqXCwGSFiD5K4BkXGy0MvWnSw,885
jax/_src/debugger/__pycache__/__init__.cpython-38.pyc,,
jax/_src/debugger/__pycache__/cli_debugger.cpython-38.pyc,,
jax/_src/debugger/__pycache__/colab_debugger.cpython-38.pyc,,
jax/_src/debugger/__pycache__/colab_lib.cpython-38.pyc,,
jax/_src/debugger/__pycache__/core.cpython-38.pyc,,
jax/_src/debugger/__pycache__/web_debugger.cpython-38.pyc,,
jax/_src/debugger/cli_debugger.py,sha256=EHJdQY9vKkKbGhfyBNVDcBjlfXQa0nh2wJlE3UoZ1wI,4806
jax/_src/debugger/colab_debugger.py,sha256=Xo6_YuTd3DKYxFLlCQLJ0IA0Ic1nqEEAgQd5CVE_zec,7853
jax/_src/debugger/colab_lib.py,sha256=NTfdVgvKkQr0zE4H6AGUVlg6dEi3Q7z7fb_C9Ek2hOQ,4322
jax/_src/debugger/core.py,sha256=87dMMme5XVVBBuYOOXWo1Po7A5sUjEH-AcG-fmMT_7w,7473
jax/_src/debugger/web_debugger.py,sha256=bJhHBaPTp4qogEkh0THWjF25l9hm3r4Znvhd-bzd8u0,3317
jax/_src/debugging.py,sha256=8f4B8x5jUbS9xosBi-2NKN4gxrHkSk761hi3cOqnepI,24380
jax/_src/deprecations.py,sha256=Y2ID9gpK__WiYbwkM7buxpOaC3Yc1Ynls37vJr1wOn0,1763
jax/_src/dispatch.py,sha256=qzws3Jb-esSNG0I_1ODJhZRZuLZ76BAgem2kdQCidvI,25865
jax/_src/distributed.py,sha256=oiz7TGEb4ELHrcbJg1FIg53dghGf-a5pr4tKSKVUCcw,7259
jax/_src/dlpack.py,sha256=qewJRVXRDq5G6kHciVRgF1QufoS6matMLWgNiwoLc3M,2530
jax/_src/dtypes.py,sha256=ShT1G4cn9S7FgAeHa74bxSpAEk-m99yFXM9fT89_SDY,22529
jax/_src/effects.py,sha256=VrY6M1DsQi-8VrPcOFQQ3vsg0KifSuMQlIZ9EOEpjvo,2317
jax/_src/environment_info.py,sha256=F5p2xq60p9BT36TStxLxLZoRPojHmEFuDur4zj0zl00,1970
jax/_src/errors.py,sha256=4qU742iQc74E1nbF1jUaCvcK6NJLTBHtJxg1AAjQqeA,21638
jax/_src/flatten_util.py,sha256=jzKbWVjG04I3Xwltjw7rOxg80FjuZLhZoSD7QftE18Q,3557
jax/_src/gfile_cache.py,sha256=yfHAo5Jl-Knt-P1GWRaaB4Ucxw3t7g66jD-rGti9KwY,1923
jax/_src/image/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/image/__pycache__/__init__.cpython-38.pyc,,
jax/_src/image/__pycache__/scale.cpython-38.pyc,,
jax/_src/image/scale.py,sha256=ehvj8Oikn6PCJpg9H1vEDr6JDqLtDvbgbyuN40eoJhM,13726
jax/_src/internal_test_util/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/internal_test_util/__pycache__/__init__.cpython-38.pyc,,
jax/_src/internal_test_util/__pycache__/deprecation_module.cpython-38.pyc,,
jax/_src/internal_test_util/__pycache__/lax_test_util.cpython-38.pyc,,
jax/_src/internal_test_util/deprecation_module.py,sha256=iZ6fRYNaUcOwrftwpDXMnlxrLrrQbdMrbybmmQFwql4,851
jax/_src/internal_test_util/lax_test_util.py,sha256=YlpbIzstLbipdoHRa8qPv1jbC9SAls8BXccI_IkMjUI,12061
jax/_src/internal_test_util/lazy_loader_module/__init__.py,sha256=UP_c2VfWHRhfEi6n4F6qLHZuageXhPiVSAcUKHrrxFs,705
jax/_src/internal_test_util/lazy_loader_module/__pycache__/__init__.cpython-38.pyc,,
jax/_src/internal_test_util/lazy_loader_module/__pycache__/lazy_test_submodule.cpython-38.pyc,,
jax/_src/internal_test_util/lazy_loader_module/lazy_test_submodule.py,sha256=luZv4DGyWwdcBX0_NBVwu81IE4jFYVbRkKo9gQPmxPk,610
jax/_src/interpreters/__init__.py,sha256=hnuhN7h1jY5__Kt2rBvTrk0o8nX4fQl244fDCIMh0qk,690
jax/_src/interpreters/__pycache__/__init__.cpython-38.pyc,,
jax/_src/interpreters/__pycache__/ad.cpython-38.pyc,,
jax/_src/interpreters/__pycache__/batching.cpython-38.pyc,,
jax/_src/interpreters/__pycache__/mlir.cpython-38.pyc,,
jax/_src/interpreters/__pycache__/partial_eval.cpython-38.pyc,,
jax/_src/interpreters/__pycache__/pxla.cpython-38.pyc,,
jax/_src/interpreters/__pycache__/xla.cpython-38.pyc,,
jax/_src/interpreters/ad.py,sha256=e7iZxwgKcRAut1vOxAPsEOOhqr-CDCpoovI4KgWDEqA,33409
jax/_src/interpreters/batching.py,sha256=cjrb-cBSA45guRdY_c4ZgAoRo_qJxPwThL1rfcRDBP4,45131
jax/_src/interpreters/mlir.py,sha256=FfdqOlX1Y0jFD56p9qlyYoQl_nfOE0oa7i9OXZ5s9vo,89360
jax/_src/interpreters/partial_eval.py,sha256=yS5JQGhZHlw7gqY_OTvo8IG8wXeMj9FLsWLMicW_CvI,118043
jax/_src/interpreters/pxla.py,sha256=pgMYtnBvhZOuacmM2FZDn5Fs05M8i149Lo1Gb6r5FUs,131012
jax/_src/interpreters/xla.py,sha256=TBMbcqL2KVUDQVhSAP5mP76MS4xqdNdSizuUtEZh4jM,16877
jax/_src/iree.py,sha256=GAHuZObP9agSEsD6e1Ygc3HJ_ZYv1048bIl5xwOB7ZE,6168
jax/_src/lax/__init__.py,sha256=jRL3qQoUco8BVUAh8lV-KuY38h_4GxXcb41asljW2yI,690
jax/_src/lax/__pycache__/__init__.cpython-38.pyc,,
jax/_src/lax/__pycache__/ann.cpython-38.pyc,,
jax/_src/lax/__pycache__/convolution.cpython-38.pyc,,
jax/_src/lax/__pycache__/eigh.cpython-38.pyc,,
jax/_src/lax/__pycache__/fft.cpython-38.pyc,,
jax/_src/lax/__pycache__/lax.cpython-38.pyc,,
jax/_src/lax/__pycache__/linalg.cpython-38.pyc,,
jax/_src/lax/__pycache__/other.cpython-38.pyc,,
jax/_src/lax/__pycache__/parallel.cpython-38.pyc,,
jax/_src/lax/__pycache__/qdwh.cpython-38.pyc,,
jax/_src/lax/__pycache__/slicing.cpython-38.pyc,,
jax/_src/lax/__pycache__/special.cpython-38.pyc,,
jax/_src/lax/__pycache__/stack.cpython-38.pyc,,
jax/_src/lax/__pycache__/svd.cpython-38.pyc,,
jax/_src/lax/__pycache__/utils.cpython-38.pyc,,
jax/_src/lax/__pycache__/windowed_reductions.cpython-38.pyc,,
jax/_src/lax/ann.py,sha256=jGg7NZBJKtTMZeF2Bk9euLOaa8J9KcCh_L9rQLRBpio,19161
jax/_src/lax/control_flow/__init__.py,sha256=oK7DbXklP9v3q9qtpWxDRmS6TOs2M2xm-Wh3OSKqcI4,2090
jax/_src/lax/control_flow/__pycache__/__init__.cpython-38.pyc,,
jax/_src/lax/control_flow/__pycache__/common.cpython-38.pyc,,
jax/_src/lax/control_flow/__pycache__/conditionals.cpython-38.pyc,,
jax/_src/lax/control_flow/__pycache__/for_loop.cpython-38.pyc,,
jax/_src/lax/control_flow/__pycache__/loops.cpython-38.pyc,,
jax/_src/lax/control_flow/__pycache__/solves.cpython-38.pyc,,
jax/_src/lax/control_flow/common.py,sha256=MvmY6zjveEQH-VNBQ3262X10J619RGTJWwbZUxXTfXA,10278
jax/_src/lax/control_flow/conditionals.py,sha256=xcNqj5ND-3Dg0d21yaXWewCwLd4mJcxuUrLqAcuI6YE,37732
jax/_src/lax/control_flow/for_loop.py,sha256=Pfday2SIEjgPbnK8QPgkdBQbJ_iUAkToU_9zoh2dIvA,37004
jax/_src/lax/control_flow/loops.py,sha256=HDYSS09VmMaFaJZrnv4Xx610cMVE51qFHpl6_vYf9rQ,93895
jax/_src/lax/control_flow/solves.py,sha256=D3PEw6MsfWehqEV8bvW2Ol3zyFO-funjbMzaJR3ArJ4,18898
jax/_src/lax/convolution.py,sha256=1aHi-qfjpf7P5B3vf0IeU6NA2FZoY6Pl7bJcfrJKXYo,44880
jax/_src/lax/eigh.py,sha256=tZCLdbrArpe8HskzEPhyEV_qSewLEbPJqc3ebdzCSqI,19853
jax/_src/lax/fft.py,sha256=xgLmHxAsMlUGrsfMUyUOdpj4njwsoUOQW_QzFl3HyU4,9954
jax/_src/lax/lax.py,sha256=_VwzyCmxl8jgQKfw4INbN7RORVjpc4LoQ115IrMQvSc,201695
jax/_src/lax/linalg.py,sha256=32SZcBj30lRDHmPyZ2GnZr7_K2L4zXGCPZ2dDR3uMQk,87203
jax/_src/lax/other.py,sha256=xcZuWezZZhhaU6czKq0G7sh5LUJentLVrTz0pXF8H6Y,10534
jax/_src/lax/parallel.py,sha256=pLDSCkiQoZ7jaqbKTN_n9Zskrq_XDIkQmH42w395YLQ,74025
jax/_src/lax/qdwh.py,sha256=m4J3cBHiMTgtSPGOSZ05ooM7UyPa_XlFcn5XGt9Ts2s,8030
jax/_src/lax/slicing.py,sha256=maPfwHuuj2c911-_LWZVHJT1dU7doXHO5xBAA4CTtLE,98669
jax/_src/lax/special.py,sha256=H1GBztInvcQ2GZWRqUJJSFhKC5r_h0ZtL7h2dIraxns,26097
jax/_src/lax/stack.py,sha256=p5v0dZUi7urr5WANY5Iug4bnCLan1U1oIFftPkdM-aw,2567
jax/_src/lax/svd.py,sha256=nSDbMzOLHynXpdVbUMv2sHJ5yVWMzYd8H4WYE8g6gtY,9806
jax/_src/lax/utils.py,sha256=dWzLKapgVIJfGrYFwTaY60gAABn-6Qr9aAG9ogeM1A8,5191
jax/_src/lax/windowed_reductions.py,sha256=nqHyCR4f6wIT0SfVnK-WmxK63QoacQ3FdiN0cNROHRY,40235
jax/_src/lax_reference.py,sha256=Uea1YbgJqKMBEtbUZRdsFkaqMMRnilLJfIuxrJBzaO0,17297
jax/_src/lazy_loader.py,sha256=DhaxBBBH5OzgdHLM0QJzju1KsLWiVGG1mJQSWsVzxEA,1319
jax/_src/lib/__init__.py,sha256=DlnDCpsYZlPS7-zFOdN0j2ShzMPt2ePd5dTY24BXyoM,5314
jax/_src/lib/__pycache__/__init__.cpython-38.pyc,,
jax/_src/lib/mlir/__init__.py,sha256=BiNbgNL5EDNE2U2PR1awdC8Hg-_G9FGVcAVmFTgyKoE,678
jax/_src/lib/mlir/__pycache__/__init__.cpython-38.pyc,,
jax/_src/lib/mlir/__pycache__/jax.cpython-38.pyc,,
jax/_src/lib/mlir/dialects/__init__.py,sha256=Bm4kk4bOkk_BO46n6dO07_NGeK-jV31lq6gm3jT69Xs,1010
jax/_src/lib/mlir/dialects/__pycache__/__init__.cpython-38.pyc,,
jax/_src/lib/mlir/jax.py,sha256=JM5WAi5Y27l2SB624nTHq0vgpnIin98JviHYRKN7dk4,645
jax/_src/linear_util.py,sha256=JRLDMHHqmsNZiSX2Iagjdz8aXeMFtvZFPU49mUsvIKo,13751
jax/_src/maps.py,sha256=zRrtsj1j_eEKwkeYaA8QIXBsl_G0mY52dLboydaemqA,86423
jax/_src/mesh.py,sha256=IsgXp9ekAjLyBW1hYGgap8icC0oVlX18y5zoxBWWDew,10076
jax/_src/monitoring.py,sha256=7uHYAWGXmY12ut6zghhIpD3UbGP8gJsiMCn0KNJQk54,1928
jax/_src/nn/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/nn/__pycache__/__init__.cpython-38.pyc,,
jax/_src/nn/__pycache__/functions.cpython-38.pyc,,
jax/_src/nn/__pycache__/initializers.cpython-38.pyc,,
jax/_src/nn/functions.py,sha256=EYYP6yuzqyMwyEAlh5m1xitUwVTU1qr37MLmlhNVrBU,15433
jax/_src/nn/initializers.py,sha256=IWIqu5PqnPgBfkqdQtPHKYHvFokoStpnDD3tLm5RArY,23841
jax/_src/numpy/__init__.py,sha256=-QhmzWj2qFbKVIT9TDAIvQWaKQibiRXy3r1zQkI3X4M,581
jax/_src/numpy/__pycache__/__init__.cpython-38.pyc,,
jax/_src/numpy/__pycache__/array_methods.cpython-38.pyc,,
jax/_src/numpy/__pycache__/fft.cpython-38.pyc,,
jax/_src/numpy/__pycache__/index_tricks.cpython-38.pyc,,
jax/_src/numpy/__pycache__/lax_numpy.cpython-38.pyc,,
jax/_src/numpy/__pycache__/linalg.cpython-38.pyc,,
jax/_src/numpy/__pycache__/polynomial.cpython-38.pyc,,
jax/_src/numpy/__pycache__/reductions.cpython-38.pyc,,
jax/_src/numpy/__pycache__/setops.cpython-38.pyc,,
jax/_src/numpy/__pycache__/ufuncs.cpython-38.pyc,,
jax/_src/numpy/__pycache__/util.cpython-38.pyc,,
jax/_src/numpy/__pycache__/vectorize.cpython-38.pyc,,
jax/_src/numpy/array_methods.py,sha256=S8-D6e3u1XWCMtazgVJ8eqQg4JzVuX6h5ZYjHXghDmQ,34032
jax/_src/numpy/fft.py,sha256=Nq7Mz-bixChupr2rt5iyi49HUvhy7QDzQRGBnFKA7nI,11321
jax/_src/numpy/index_tricks.py,sha256=sxIlUpADQUzhkVjeWWQbiJAb240xAt3TPblTeOK4D-w,9882
jax/_src/numpy/lax_numpy.py,sha256=2s-nV_oGexagHCmh-yw4aF180RQJU5eF3x13EM4dDHk,195098
jax/_src/numpy/linalg.py,sha256=zr-nGzsaHP7DWfkyRznNZh_07YawR__qHbkQDNZaLg8,26577
jax/_src/numpy/polynomial.py,sha256=KXi7TiCHk0t-YyEaV98zuwEZ60K1PAD7Opn2g0bwhJI,12133
jax/_src/numpy/reductions.py,sha256=LnHSJIba7ivOCsQNDQjNJt9O2gP8-RqIQcG6FwdSLmY,40611
jax/_src/numpy/setops.py,sha256=ir2PHii8ILiBYdslDNyKGFOezJb4K2cL-eQpgPrTu1M,13743
jax/_src/numpy/ufuncs.py,sha256=-RCjlZNXViJSI9PNuascYXzw33JwO27_cIz5A6UGWPo,25857
jax/_src/numpy/util.py,sha256=qad1EgEr19y5hNFd9WvFF-iBeiW6yG-j4SopPtrP7_k,17569
jax/_src/numpy/vectorize.py,sha256=pDy2XnZkbnQAlbrAMK-AW0mLq32rEv7xJBNEw2YAzxs,12139
jax/_src/op_shardings.py,sha256=wW1nQPh6vfJVru8n5vpBHl-nkFhxDKRZCjOYDii1R04,3863
jax/_src/ops/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/ops/__pycache__/__init__.cpython-38.pyc,,
jax/_src/ops/__pycache__/scatter.cpython-38.pyc,,
jax/_src/ops/__pycache__/special.cpython-38.pyc,,
jax/_src/ops/scatter.py,sha256=uWNNhjRHgHG8pv9jV6LhF_bG2HnhASQUzXrAnaRcdW0,17325
jax/_src/ops/special.py,sha256=nis1MTo6JQtwNrvII9ybEYpYGXAJO90kgbsjuPfpzAE,4409
jax/_src/partition_spec.py,sha256=yNkh5t9h24YnrEQZHq8BFIK8yglf23-xRabd7ZdEf3c,1627
jax/_src/path.py,sha256=Zd6Q4jJEK8m4MOVUsiT2ALoUDHvoZjP37SZFYJzacVI,1121
jax/_src/pickle_util.py,sha256=UUNYUdIIcN9G8NW5EwRiVuxJo7zP8-p4v66rwq7jt9w,2302
jax/_src/pjit.py,sha256=R6eqCAfdmgKDjX5gzcRQBjf3KBni260Z7vDdiDJoZuI,87366
jax/_src/pretty_printer.py,sha256=b9IYYt2XlHwqwHKx2Lp3XrYihqAK0XWQklbZ7MmWkwQ,12892
jax/_src/prng.py,sha256=uROFGNqxW58t4H1T8RZn3cf5UvIh3grKMHmvj1vSeDI,50526
jax/_src/profiler.py,sha256=RLmDVrmgCRbBg6jRpbz3R0KLKv5k1EB7Ax5nAffFVmE,13925
jax/_src/public_test_util.py,sha256=GBEl-XIciWBsD-jyzyHIVIDDFiDDuzifibdS-MSqRRw,10306
jax/_src/random.py,sha256=GektScJY_5g4_RdrcwjxEGYK9AhTksvanYdD_xMpX3M,83699
jax/_src/scipy/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/__pycache__/__init__.cpython-38.pyc,,
jax/_src/scipy/__pycache__/fft.cpython-38.pyc,,
jax/_src/scipy/__pycache__/linalg.cpython-38.pyc,,
jax/_src/scipy/__pycache__/ndimage.cpython-38.pyc,,
jax/_src/scipy/__pycache__/signal.cpython-38.pyc,,
jax/_src/scipy/__pycache__/special.cpython-38.pyc,,
jax/_src/scipy/cluster/__init__.py,sha256=S-o0CP72882Xv3l6k2PlrEx6eNvXBTk5Oaed5KF4iL8,581
jax/_src/scipy/cluster/__pycache__/__init__.cpython-38.pyc,,
jax/_src/scipy/cluster/__pycache__/vq.cpython-38.pyc,,
jax/_src/scipy/cluster/vq.py,sha256=OxDujGx3RvogMoqXKWWfpxsKigzBwxG2AYPCAsqLh4g,1771
jax/_src/scipy/fft.py,sha256=iXKAZiUQdZ2sLv7k2teR1fCkdSQsIVpMYP4hzu4u0PE,6220
jax/_src/scipy/interpolate/__init__.py,sha256=S-o0CP72882Xv3l6k2PlrEx6eNvXBTk5Oaed5KF4iL8,581
jax/_src/scipy/interpolate/__pycache__/__init__.cpython-38.pyc,,
jax/_src/scipy/linalg.py,sha256=qC4C3yPCU4lwRG-wV0FN89FiFj8fyi4oei6N6aEaGRw,42066
jax/_src/scipy/ndimage.py,sha256=HS3_q_Xpx0aYMo_Jimn0vwsQ4ZopQ4e1HCnnO_Qyg8w,5347
jax/_src/scipy/optimize/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/optimize/__pycache__/__init__.cpython-38.pyc,,
jax/_src/scipy/optimize/__pycache__/_lbfgs.cpython-38.pyc,,
jax/_src/scipy/optimize/__pycache__/bfgs.cpython-38.pyc,,
jax/_src/scipy/optimize/__pycache__/line_search.cpython-38.pyc,,
jax/_src/scipy/optimize/__pycache__/minimize.cpython-38.pyc,,
jax/_src/scipy/optimize/_lbfgs.py,sha256=p_IMzCth237gXPrilQXye7HT8xCgU6QWiS3PClomGgk,7809
jax/_src/scipy/optimize/bfgs.py,sha256=MskxYeLfZVfJLEemLHxckhrQmE0tRdd4D5e8Ksw8Kkg,5613
jax/_src/scipy/optimize/line_search.py,sha256=iE5Ak9OYven2izsJ-a78AED7ReGOfsX7384IK1qR0yw,13430
jax/_src/scipy/optimize/minimize.py,sha256=M_XbYm9RwF8iShgS_MJ49nXUQCdQu-8oDsDRKHGKxhY,4978
jax/_src/scipy/signal.py,sha256=_fVAONzU8BUgGb2JpKYcErrvWat1fTktEaZPzeDMJpo,27540
jax/_src/scipy/sparse/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/sparse/__pycache__/__init__.cpython-38.pyc,,
jax/_src/scipy/sparse/__pycache__/linalg.cpython-38.pyc,,
jax/_src/scipy/sparse/linalg.py,sha256=Ev_-7aIZRjnyisSZkBydXAfzZULpg148s-znErsFhpQ,27330
jax/_src/scipy/spatial/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/_src/scipy/spatial/__pycache__/__init__.cpython-38.pyc,,
jax/_src/scipy/spatial/__pycache__/transform.cpython-38.pyc,,
jax/_src/scipy/spatial/transform.py,sha256=X2CBNHrs3VLGMR8r6CSJv2gdttKYh9lQbCAF_DuQ5Q0,15206
jax/_src/scipy/special.py,sha256=MEX3W6yVAa4FJcrBHE_635K94ct5m7i5MKJsM-AcdDU,56226
jax/_src/scipy/stats/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/stats/__pycache__/__init__.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/_core.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/bernoulli.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/beta.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/betabinom.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/cauchy.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/chi2.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/dirichlet.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/expon.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/gamma.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/gennorm.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/geom.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/kde.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/laplace.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/logistic.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/multinomial.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/multivariate_normal.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/nbinom.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/norm.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/pareto.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/poisson.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/t.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/truncnorm.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/uniform.cpython-38.pyc,,
jax/_src/scipy/stats/__pycache__/vonmises.cpython-38.pyc,,
jax/_src/scipy/stats/_core.py,sha256=k1eRa_ww-cDhDGtxiwBy7bPHFdF18VElk6KjxbkLzFY,4966
jax/_src/scipy/stats/bernoulli.py,sha256=ad0-qS___0-0PXi_JSNcnIc8-2kspvmCECwPSCW1lBE,2250
jax/_src/scipy/stats/beta.py,sha256=7FPvw3jxVQ2HvDvUN8kWSZNysoFoHZbmukFQOXmCdwI,2603
jax/_src/scipy/stats/betabinom.py,sha256=kBh8yc_9QaCBmkHcgs25HfG7vpk9MkXYklw22ffbl6I,1993
jax/_src/scipy/stats/cauchy.py,sha256=g3hkfjKMIspgx_zDac9ssShVKi7V_Kl4I8GZ3_xdImY,2912
jax/_src/scipy/stats/chi2.py,sha256=3ScunNKDneVY8s5ye4ttrBx76XxRwb2HCqFyUW7jVVI,2524
jax/_src/scipy/stats/dirichlet.py,sha256=0cp5kgUBlEvAO-B4vWRsa1MFwpHg2aOztpAWbKxLYO0,2136
jax/_src/scipy/stats/expon.py,sha256=zOROY78Y70-DQRA7hrEOcbarXLHhfb_9BU3VPISKmpc,1314
jax/_src/scipy/stats/gamma.py,sha256=D2PCmrncvrtvtMb2VMZ_M_dRIU7A9dpptEvb9VGkdfE,2359
jax/_src/scipy/stats/gennorm.py,sha256=Y70sqdWpQnqPiL577XFHZxTIW4ZC-hq30Rg1MRahxg4,1295
jax/_src/scipy/stats/geom.py,sha256=fDxS432YyqwPuvWNvHwR6CuMckopnFGMUOsNOwrryAk,1382
jax/_src/scipy/stats/kde.py,sha256=1QmT4Avlc568n3dkFq8A9182QevM1PrHOFcZ4nX8Tgc,9856
jax/_src/scipy/stats/laplace.py,sha256=0UxvVoID2Fj9K-UBQI28pGKsaGv7OKLqAXBVU1VuMvM,1787
jax/_src/scipy/stats/logistic.py,sha256=aSRXac2fq0gks2MXUNJFhWwPywQiKu8a8Up8e-29cY8,2406
jax/_src/scipy/stats/multinomial.py,sha256=I7kD1jq3Kkp-EA_0VRRwegazpDKQ-jCn_Yi26_N620g,1675
jax/_src/scipy/stats/multivariate_normal.py,sha256=-FrmiStPBfnU_2zgxyOg5qILmIHzo9YvaBzOoJ3CYGU,2219
jax/_src/scipy/stats/nbinom.py,sha256=DviJZ0ATjbVeONzaBrXOians399z-uAZvRxKQj_nTZU,1698
jax/_src/scipy/stats/norm.py,sha256=JJQAZfzsk5qojp6U-BQHU-D-qQfGHi9o7c52_pRAXlU,2631
jax/_src/scipy/stats/pareto.py,sha256=Yt_WwIdmC6mvf8xGwh-SpznyRHgyglYcg64HikBUBjE,1497
jax/_src/scipy/stats/poisson.py,sha256=56Y1k4sOfWz7h361KZHH-eUAx-G9ndPKQX5pMNObedg,1684
jax/_src/scipy/stats/t.py,sha256=nX8aCRT6Muy3GNg36piZ-wmaZwWjbM6N--YN7S1CKZc,1822
jax/_src/scipy/stats/truncnorm.py,sha256=YbjgBD4qxbsburwR9kbWCINg5PqohF84I9cEXzcPYlk,4368
jax/_src/scipy/stats/uniform.py,sha256=jTU0WPQwX6ohnaeLwyvdkEN5a3Zh40gRJm-k6ATCWBU,1325
jax/_src/scipy/stats/vonmises.py,sha256=NUMe-B35glSUasmZ9Qny8rCB7cgklCTVKJuu59b_suU,1272
jax/_src/sharding.py,sha256=n6U6uTvILychdrWd9I9OY_heenE5ezKpOMl_kaqV79I,4495
jax/_src/sharding_impls.py,sha256=2vPoybj_OjF7FHwZJnxdjUYICpduqre8kXWIiQ_AryQ,44106
jax/_src/sharding_specs.py,sha256=-VO1QZjTzXSYEkdCvletBqdttZYC0LUKXsL50sw9zMI,13398
jax/_src/source_info_util.py,sha256=-D9UgoTSGls-kfHoIawrgqS0nuo1-rcRAxValOrsz0I,8959
jax/_src/stages.py,sha256=RgbJpazeXNqE7SXnxKcEHy7FG3iPtqUqROtxqJTCvXo,24903
jax/_src/state/__init__.py,sha256=khala6P_HUY5rJ54SzsKvJwDweLSfbBp84q1r4SYl9A,823
jax/_src/state/__pycache__/__init__.cpython-38.pyc,,
jax/_src/state/__pycache__/discharge.cpython-38.pyc,,
jax/_src/state/__pycache__/primitives.cpython-38.pyc,,
jax/_src/state/__pycache__/types.cpython-38.pyc,,
jax/_src/state/__pycache__/utils.cpython-38.pyc,,
jax/_src/state/discharge.py,sha256=u6i76F4WAZGiCvPQ6RgbItxlUjNVNw_oWz8hi4E5Gbw,33470
jax/_src/state/primitives.py,sha256=a5xlseMm0OZM6JtjNBpI1XeB3cPGsYx7WaitKP_IG9c,21674
jax/_src/state/types.py,sha256=WA83k7xcQTAvwke5rJEWF--2dvxsd_Unz6QjQGdMaDo,5280
jax/_src/state/utils.py,sha256=fn8NOU_WBT04Rd21eWmzecHiPKtBTaX5OqTqgeri5V8,2340
jax/_src/test_util.py,sha256=xX04OxdtpTpWrtEal9S54_tQM6dVuGXxOJm4h8IpdZk,42890
jax/_src/third_party/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jax/_src/third_party/__pycache__/__init__.cpython-38.pyc,,
jax/_src/third_party/numpy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jax/_src/third_party/numpy/__pycache__/__init__.cpython-38.pyc,,
jax/_src/third_party/numpy/__pycache__/linalg.cpython-38.pyc,,
jax/_src/third_party/numpy/linalg.py,sha256=TZc4moT_05TmTxRqL9fzP8DyzYBYq5iBGZIgyvtoFO0,6211
jax/_src/third_party/scipy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jax/_src/third_party/scipy/__pycache__/__init__.cpython-38.pyc,,
jax/_src/third_party/scipy/__pycache__/betaln.cpython-38.pyc,,
jax/_src/third_party/scipy/__pycache__/interpolate.cpython-38.pyc,,
jax/_src/third_party/scipy/__pycache__/linalg.cpython-38.pyc,,
jax/_src/third_party/scipy/__pycache__/signal_helper.cpython-38.pyc,,
jax/_src/third_party/scipy/betaln.py,sha256=TT77MW5HcPt-C6m-jHT43mGPLuAKO9ANcoEsXjl8JDE,2373
jax/_src/third_party/scipy/interpolate.py,sha256=PMusO102slpONLd0mydFZ1UAHRQsSD3PIkUbgBnFoNg,5869
jax/_src/third_party/scipy/linalg.py,sha256=Y1EzjY8rgJUd7GaxC4V5KWydoyUYysS5liU2J59wJFg,2624
jax/_src/third_party/scipy/signal_helper.py,sha256=HTQ7K0oYgl5c_I4TahYwh0HOFUckSrGZBMtpsgHmtro,3077
jax/_src/traceback_util.py,sha256=jB01V6Sm3PLMcae7NI_FvazVhDLEI36LmkMmZBB8uus,8086
jax/_src/tree_util.py,sha256=ueVXaudiB6tokDdnxkSZ2ZPfn8lv9XDF2WovVJ_TCo8,35059
jax/_src/typing.py,sha256=xKk50SazHupnuWKTDMksTEK50gQQVVq5CKGLA7TR7Gc,2709
jax/_src/util.py,sha256=x-rKvy4lXZDUekvObgP07EoWaSgy0PPbarxm5qfWLZs,17824
jax/_src/xla_bridge.py,sha256=h8-wh_LHtrbJS_cfXZfTSAEwaD4lsSfxhmg4jtTqjRU,31845
jax/abstract_arrays.py,sha256=zDyppYGrzAAylvLXZx30d03t1Qqbp4wMpUJyAh_GGTE,1691
jax/ad_checkpoint.py,sha256=gZOMDUzLVSQCsZi5AYi98FSmPH0lyqCLVdXpfdUsf1I,711
jax/api_util.py,sha256=JnZFSuqh5WBglJwerrWxcIDslBaXYZQrCv6j0jPfu7M,769
jax/cloud_tpu_init.py,sha256=nNdPdZZffdRGb38rEx1YOLXNpymSNt05ZVWSm8lhZ8c,633
jax/collect_profile.py,sha256=XVCIPglqBadvnmZz0rWy13_wxOVoR1rf0qVcUOx1dqA,4716
jax/config.py,sha256=eARsQrI4Ti7e6y56r_dAj6BJfd5qS4BhpI1ccBdLf5w,696
jax/core.py,sha256=QWp-EPDPZNH3SYu8xABYKWLLB9oh039c737Fhz3Ainw,7122
jax/custom_batching.py,sha256=zQmkJIxOxxgAqEgby1q-vQWHYKzyqp1__n8Hqs04OV4,657
jax/custom_derivatives.py,sha256=FWBXGy0zp70zI46ErbGucn23R6axD_0BuvZoeZJaW0U,1341
jax/custom_transpose.py,sha256=BmKD3o61GFyTE68GoNX_3Z8NBJl5HItzBCi8RehA9ks,644
jax/debug.py,sha256=HXdNZlaM5LxtEYS6fsH3J4LRgiDVL-5W6AXcimbFS5o,1083
jax/distributed.py,sha256=_-zv-zCv4BirRPOM8Uy6zXOCp-V4ej4zNDS2YTiCRSw,638
jax/dlpack.py,sha256=Xq3mQbR6R_ak_CD2itRttfJx11cVaXtdl_xLQL3clfY,653
jax/dtypes.py,sha256=70P2_vv4eRD8mxndzjkyjxVs2UzKsrMbE5BL1AQnLHs,1094
jax/errors.py,sha256=CJ0jLldSs803popDowHG5qfeGxU41J9CvOP65jmYBlk,1101
jax/example_libraries/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/example_libraries/__pycache__/__init__.cpython-38.pyc,,
jax/example_libraries/__pycache__/optimizers.cpython-38.pyc,,
jax/example_libraries/__pycache__/stax.cpython-38.pyc,,
jax/example_libraries/optimizers.py,sha256=MtdB1_MDmR6Q__gDXWGS0PLYoNT5F1JoB3v_uJmwrWw,20543
jax/example_libraries/stax.py,sha256=cMt8-39Q-o-tWwquR00RAlbbiWaitHLK4P03Xj0Wa3M,13849
jax/experimental/__init__.py,sha256=PIs2CgK8WYTCTx1EODRmbnaQuPWaJb-NDFYy9diby_4,877
jax/experimental/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/__pycache__/checkify.cpython-38.pyc,,
jax/experimental/__pycache__/custom_partitioning.cpython-38.pyc,,
jax/experimental/__pycache__/host_callback.cpython-38.pyc,,
jax/experimental/__pycache__/jet.cpython-38.pyc,,
jax/experimental/__pycache__/maps.cpython-38.pyc,,
jax/experimental/__pycache__/mesh_utils.cpython-38.pyc,,
jax/experimental/__pycache__/multihost_utils.cpython-38.pyc,,
jax/experimental/__pycache__/ode.cpython-38.pyc,,
jax/experimental/__pycache__/pjit.cpython-38.pyc,,
jax/experimental/__pycache__/rnn.cpython-38.pyc,,
jax/experimental/__pycache__/serialize_executable.cpython-38.pyc,,
jax/experimental/__pycache__/shard_map.cpython-38.pyc,,
jax/experimental/__pycache__/topologies.cpython-38.pyc,,
jax/experimental/__pycache__/x64_context.cpython-38.pyc,,
jax/experimental/array_serialization/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/experimental/array_serialization/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/array_serialization/__pycache__/serialization.cpython-38.pyc,,
jax/experimental/array_serialization/__pycache__/serialization_test.cpython-38.pyc,,
jax/experimental/array_serialization/serialization.py,sha256=J3YQ__FeYoI2nKGMYvSLDKNlPl3parRGQVMzk0ptT_M,20034
jax/experimental/array_serialization/serialization_test.py,sha256=zT-l-1ubssK5MRTXoY6jZGPjSAhfrMv1J8oTBDDw74c,11554
jax/experimental/checkify.py,sha256=OfaBF3DRHQpmIHEex-3nFtu3D4hHGLK7GIpSVZbewYA,1213
jax/experimental/compilation_cache/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/experimental/compilation_cache/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/compilation_cache/__pycache__/compilation_cache.cpython-38.pyc,,
jax/experimental/compilation_cache/compilation_cache.py,sha256=FuNWsnrwmZgC00X2-K27s-D1bKIR7SC7gkMGLQ1WxmM,731
jax/experimental/custom_partitioning.py,sha256=NZZgBfoRNjdiO5NW_u_1OPLECJeeG4eZjBT0q5We4yg,21024
jax/experimental/host_callback.py,sha256=9jqg2e0TAGb8L8LalsS93roHOcGSabYwHo5BSBiX2SE,86443
jax/experimental/jax2tf/__init__.py,sha256=zAjffo3sksJB_KI-bMB4rYEQd7UZdkM61gUaoWeoeL4,925
jax/experimental/jax2tf/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/jax2tf/__pycache__/call_tf.cpython-38.pyc,,
jax/experimental/jax2tf/__pycache__/impl_no_xla.cpython-38.pyc,,
jax/experimental/jax2tf/__pycache__/jax2tf.cpython-38.pyc,,
jax/experimental/jax2tf/__pycache__/jax_export.cpython-38.pyc,,
jax/experimental/jax2tf/__pycache__/shape_poly.cpython-38.pyc,,
jax/experimental/jax2tf/call_tf.py,sha256=fAKE6HiheTNtpM5G1OlPs2HDWuxsSDsBDPP6Pii1fU0,27764
jax/experimental/jax2tf/examples/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/experimental/jax2tf/examples/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/jax2tf/examples/__pycache__/keras_reuse_main.cpython-38.pyc,,
jax/experimental/jax2tf/examples/__pycache__/keras_reuse_main_test.cpython-38.pyc,,
jax/experimental/jax2tf/examples/__pycache__/mnist_lib.cpython-38.pyc,,
jax/experimental/jax2tf/examples/__pycache__/saved_model_lib.cpython-38.pyc,,
jax/experimental/jax2tf/examples/__pycache__/saved_model_main.cpython-38.pyc,,
jax/experimental/jax2tf/examples/__pycache__/saved_model_main_test.cpython-38.pyc,,
jax/experimental/jax2tf/examples/keras_reuse_main.py,sha256=1NE2vDgU7UYMBHbbK0__YbpEIORCRMOgYJNd1wwz4vw,2883
jax/experimental/jax2tf/examples/keras_reuse_main_test.py,sha256=1FBpLLwCpm9v4udaL0tEN3umiqF4a1lXSr8k483R9Eg,1616
jax/experimental/jax2tf/examples/mnist_lib.py,sha256=jEix4Sktr7PXR7wNW7n2oxt_kDH96OGHCFkA2xGEYhc,11438
jax/experimental/jax2tf/examples/saved_model_lib.py,sha256=MBdLvp_EuONkpU2gTUuuVG_6roShPJGBp5Nc49_5Mgk,7068
jax/experimental/jax2tf/examples/saved_model_main.py,sha256=m_Pxj_Srz9NMiKyDnrlXpK3WzOb-k-foQq8wlZlQqew,7779
jax/experimental/jax2tf/examples/saved_model_main_test.py,sha256=w6QzDTG-t8f3tdu2a7qfYLoXXWw5bcU7ANJi1nuUIRc,2453
jax/experimental/jax2tf/examples/serving/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/experimental/jax2tf/examples/serving/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/jax2tf/examples/serving/__pycache__/model_server_request.cpython-38.pyc,,
jax/experimental/jax2tf/examples/serving/model_server_request.py,sha256=ULaCKz6Co504_WTI5zfzQ6l8cfJiJgV2JCSFhP0E5Mk,4957
jax/experimental/jax2tf/impl_no_xla.py,sha256=kvow8A4Ggn6JOemXNmv98fF4VoJ9pzEOIzla65YTOV8,53825
jax/experimental/jax2tf/jax2tf.py,sha256=TzoOTeihTpAyu5UCeUuK94XR-31QK6LmvsZLC70bpd8,135391
jax/experimental/jax2tf/jax_export.py,sha256=0lAUv59asfv4gQE9GiKTbT_ffkWRf0HxecSCum2g730,46081
jax/experimental/jax2tf/shape_poly.py,sha256=dm7Vw1fnQ9wM1tpDpCIPN1IyunpPxubsPiQARZRy22E,57799
jax/experimental/jax2tf/tests/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/experimental/jax2tf/tests/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/back_compat_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/back_compat_test_util.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/back_compat_tf_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/call_tf_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/control_flow_ops_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/converters.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/cross_compilation_check.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/jax2tf_limitations.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/jax2tf_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/jax_export_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/jax_primitives_coverage_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/model_harness.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/models_test_main.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/primitive_harness.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/primitives_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/savedmodel_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/shape_poly_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/sharding_test.cpython-38.pyc,,
jax/experimental/jax2tf/tests/__pycache__/tf_test_util.cpython-38.pyc,,
jax/experimental/jax2tf/tests/back_compat_test.py,sha256=Qqnaylasjz9htraydFhbsZjmmyuTBQHpPzVoDM96Fuo,16903
jax/experimental/jax2tf/tests/back_compat_test_util.py,sha256=jEXhpvS4_CNB2VrvFeN6TuVd_8fpf86M5JcVdn8JZUs,14826
jax/experimental/jax2tf/tests/back_compat_tf_test.py,sha256=3_sdi3WQaFZgbuL7IbkUBtau5XaYxFvh6WkLYVRqF9Y,4400
jax/experimental/jax2tf/tests/call_tf_test.py,sha256=-_JDNQfnV5vOAlzHXxHmK6M8pBLlDrGls0v9yn-wp5w,54097
jax/experimental/jax2tf/tests/control_flow_ops_test.py,sha256=LlIDjrL29S_LCJYF42h9SBU2AmLPH0JNMkh82PfWsv4,10142
jax/experimental/jax2tf/tests/converters.py,sha256=IMOkFWZUnlA1RCq_eHoPIy6IhoLhmFKTRWNQyTjxbVE,3527
jax/experimental/jax2tf/tests/cross_compilation_check.py,sha256=OFDOoFYj_cK2viCUn_E2V0zFu4MZqTKajdQpGvFrIng,7379
jax/experimental/jax2tf/tests/jax2tf_limitations.py,sha256=cs5Mm_E1M9NauurqPuUml66BmPinvXIQiqnEjfTcFDs,54931
jax/experimental/jax2tf/tests/jax2tf_test.py,sha256=kSbdBhN7rB2mLTcnOqxZi_WwmlVoM0SFcSOByJR9s9g,69655
jax/experimental/jax2tf/tests/jax_export_test.py,sha256=txuIeGjphfc1DaM62MSyHTFMRCzMEpDomth1PysZ3EQ,23830
jax/experimental/jax2tf/tests/jax_primitives_coverage_test.py,sha256=kQN0uIPbVk-k7xa_1BtiFJlGCXLOAUMj6COsJ7fibho,6607
jax/experimental/jax2tf/tests/model_harness.py,sha256=oM7Q_dbs0JTXdAj8bOvcKpA9w2uNZ9kWKOgGpES6t0Y,13905
jax/experimental/jax2tf/tests/models_test_main.py,sha256=o1WvKE3NLvUuv0cG78oznghreDnDNEvXj1xzjgpnpJw,8928
jax/experimental/jax2tf/tests/primitive_harness.py,sha256=CtBqHcThxT_1tdPi4_51HP73hFBBWMgP3csiWq85gVQ,122946
jax/experimental/jax2tf/tests/primitives_test.py,sha256=fpyYdVwFawUilC99G8WRQ8IRX6VkUG8lozM79DR4fbs,14819
jax/experimental/jax2tf/tests/savedmodel_test.py,sha256=ZnhxROx6NRlsPuq95fTt39yNHEQGVEGWgMuKdUUyS8M,12815
jax/experimental/jax2tf/tests/shape_poly_test.py,sha256=a3KYUozFzTr9XpDBX1EkXC2h42Eks9IusLrHS1V1IT4,136552
jax/experimental/jax2tf/tests/sharding_test.py,sha256=E2taKNU2DFNreLJjSf2f9zUpGoKw4EDdw6zksC8tK2c,30262
jax/experimental/jax2tf/tests/tf_test_util.py,sha256=immjtxUmHr1gG0K7pVfA-5uXgMK7KEeRROJrN-RJmMQ,18730
jax/experimental/jet.py,sha256=cdJVPAEJ_7KeANS1bdC1_7ZRyoh0d76t9xMMwN7tm_Q,26912
jax/experimental/maps.py,sha256=yjycDuNpT_aNQJK_XUBqhDDfY7qHUKeBpfi7w_zwSAs,964
jax/experimental/mesh_utils.py,sha256=CyYPcUBb-J2-2Uo5o68E2QTnza4dKzJjjJCAF7tsZD4,14469
jax/experimental/multihost_utils.py,sha256=1BVCgL-BYule4ro8tUdKYPk_NRFr4o8ovBYvhSGMZ5M,15883
jax/experimental/ode.py,sha256=rYtcxYMLz5vR5aiPYiRcQKsHqLFF4JR4taypMeJr78M,10659
jax/experimental/pjit.py,sha256=vcglDLCaoX9vLYwHk5PJncgrl5szuh0C00lF7ShND8E,1289
jax/experimental/rnn.py,sha256=6Pe3F2fndopdvPbHD8FmzOV6_HlwVOhEGAfSzSU0954,19218
jax/experimental/serialize_executable.py,sha256=8wx9DBxOd2SxDYvDUSJQ6WmRc8Bsls9xUIcCOYCU2ik,3014
jax/experimental/shard_map.py,sha256=gMVWR5KvofANM6vCkbbXAE9Yv5vk80tsDAnhW7ZfHsk,56732
jax/experimental/sparse/__init__.py,sha256=hRWildIHCGDjwsHx8I8YzwJk6Q5cbkPSQ2NMPd1EPUM,11186
jax/experimental/sparse/__pycache__/__init__.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/_base.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/_lowerings.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/ad.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/api.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/bcoo.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/bcsr.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/coo.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/csr.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/linalg.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/random.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/test_util.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/transform.cpython-38.pyc,,
jax/experimental/sparse/__pycache__/util.cpython-38.pyc,,
jax/experimental/sparse/_base.py,sha256=0D9JqSIjUe-_MijU0jS0yE3psrNqqKP3fdjt116E9Qw,3186
jax/experimental/sparse/_lowerings.py,sha256=4BKVcIwJ1gJqO3gi7_X7HVTmY10HoZ-UnWzqCNj-bBk,7229
jax/experimental/sparse/ad.py,sha256=Seo7g8GK7KjELPf5kRXzG3YyTAydul9vpOxWuXwteKk,7552
jax/experimental/sparse/api.py,sha256=jOBzLmtMafrEssVTtn6m2wSIdsRP79W7HBIz1yEgo8A,6609
jax/experimental/sparse/bcoo.py,sha256=VS4bzhz6TEh8hix9-8C_e_Yg8bMvjXKkKfDjB0q3dlE,125780
jax/experimental/sparse/bcsr.py,sha256=EklPgDbReX_7yAYMbbuCdep_jeL7UsiL8c36DDOkwRk,37117
jax/experimental/sparse/coo.py,sha256=WUT3sXfD-dOWiQEZWbXY0aTLHRm-3hCqmQeqo9z0PGQ,24145
jax/experimental/sparse/csr.py,sha256=d3dlELko1hh5qyYiDQWFTc65KFF8UXDaOiRVZ2C43YM,24432
jax/experimental/sparse/linalg.py,sha256=JhCnPczfmpOOMgcAE3hR8VrTI5BKUT2OPoph-fs0e90,21626
jax/experimental/sparse/random.py,sha256=eW-JQxJ1RdWwzPIlS83Zg9GdIEdE9J7uUuECMlCHZdQ,3889
jax/experimental/sparse/test_util.py,sha256=DTnoAUboE82bQVTvc8prxCnQ8eiL9od_A8N1dLlpAQ4,7708
jax/experimental/sparse/transform.py,sha256=bVAPELd0jxWyes-QWkv7VXcXwK2SaJrk3zosnskxThk,35752
jax/experimental/sparse/util.py,sha256=bTruAip16sSXlcx9u9PyZHLAGWm-gMj5NsYnIpCsmO8,4392
jax/experimental/topologies.py,sha256=1ZyUd-rNaikYdgmLcl8Ifb2Dt1q_YLNtJhLD5orz93w,1936
jax/experimental/x64_context.py,sha256=QXJ2sdwE_hStQ-L5QPbrNVRXws3k_FuzZnWa4iTR_VM,1736
jax/extend/__init__.py,sha256=nyAbjtrxcB4R4Qhm2mWLUgprtvmqf3fyVNWGfuHD8vM,1083
jax/extend/__pycache__/__init__.cpython-38.pyc,,
jax/flatten_util.py,sha256=jGwQUB80s2jr78XuEaT1_OP2OUyVm8Pk-yAQq-C1SsY,645
jax/image/__init__.py,sha256=pEcSFsopr4U6kry9Gy82id9Whs95vn0XfBIy7haJm6c,1027
jax/image/__pycache__/__init__.cpython-38.pyc,,
jax/interpreters/__init__.py,sha256=HNgKpgKpz4svFyTlHE9wMrDp5iQtg6XeeUshYBOnRko,690
jax/interpreters/__pycache__/__init__.cpython-38.pyc,,
jax/interpreters/__pycache__/ad.cpython-38.pyc,,
jax/interpreters/__pycache__/batching.cpython-38.pyc,,
jax/interpreters/__pycache__/mlir.cpython-38.pyc,,
jax/interpreters/__pycache__/partial_eval.cpython-38.pyc,,
jax/interpreters/__pycache__/pxla.cpython-38.pyc,,
jax/interpreters/__pycache__/xla.cpython-38.pyc,,
jax/interpreters/ad.py,sha256=u8bSc_VvMVs4DB8MMb9aO2sAMqqeJUzivFnE8JmMfuU,2748
jax/interpreters/batching.py,sha256=j1AOoXkTys_su9BXgh56bM8_WVGp0KKwFrsA76_LDhk,2672
jax/interpreters/mlir.py,sha256=Aa49KzJLQovs-BhOMXrk70zJ7WMr9Ea04g1iYGXe9HU,2553
jax/interpreters/partial_eval.py,sha256=qdFyqIw1ofTvFGnSivNX9ivPGLE8G84WWgKI9gJ83Hc,4265
jax/interpreters/pxla.py,sha256=FER2O8EuXwSaNdfcUmRIXlO18WT4Tp_qTFqT55VgTkA,5697
jax/interpreters/xla.py,sha256=S_iIIDOZZV8z6bs9PHjynZSXT2zo1gdCbE0LfT-UriA,1853
jax/jaxpr_util.py,sha256=C2UxWJ1PxTohQZXReqKxmW3rsbW-N3EgIjhQumsohz4,6547
jax/lax/__init__.py,sha256=92nG5ikCrQHnmzRURoig3mNUfgxuGLT-_5uKuacmwKU,10834
jax/lax/__pycache__/__init__.cpython-38.pyc,,
jax/lax/__pycache__/linalg.cpython-38.pyc,,
jax/lax/linalg.py,sha256=w66gTsb4Z1X5_PjFUQbSP2FJgN0nNEZ9wMaBS9li6xY,1016
jax/lib/__init__.py,sha256=n6Ugts8VenX6PmSqtbE3cxWrVeZCVYevWggJFZ2sgAc,769
jax/lib/__pycache__/__init__.cpython-38.pyc,,
jax/lib/__pycache__/xla_bridge.cpython-38.pyc,,
jax/lib/xla_bridge.py,sha256=Ahw3OyFt9cSldvCLA82pLRy3Du4KtVeK8z1HCLi1bO4,807
jax/linear_util.py,sha256=rdsdO13qwtsOwj4Kmu_7vXbtaA4fim7Wt1VkVX3olmY,1044
jax/monitoring.py,sha256=TvXZn2SrpvtilfovXIJO_dHexznozSHncz7CEmTmbuw,1131
jax/nn/__init__.py,sha256=xEdi9Drex40slz4dJRQmndY9qm7lf0ceu67qNmkKdQo,1428
jax/nn/__pycache__/__init__.cpython-38.pyc,,
jax/nn/__pycache__/initializers.cpython-38.pyc,,
jax/nn/initializers.py,sha256=7eUxOyKmDNnzgBb3YVQ5NMYLdBP0G9epFXi8wk9KhFE,1429
jax/numpy/__init__.py,sha256=w10-8PTudBKvq9k0HKU03ymWWxbCyHuJXZ7iZwEUV40,11921
jax/numpy/__pycache__/__init__.cpython-38.pyc,,
jax/numpy/__pycache__/fft.cpython-38.pyc,,
jax/numpy/__pycache__/linalg.cpython-38.pyc,,
jax/numpy/fft.py,sha256=uPIPoBVZmUyE1BOCtJeYVSTPwKUWDhbNV7GpzeenTTk,1084
jax/numpy/linalg.py,sha256=1-lGB4PtTA9xr5NZR4SfwFaYM0xGTEG09pZERJEzCqY,1203
jax/ops/__init__.py,sha256=3kFyxifjh_OkJqQDDYjSkQw6EH173Rf-fb7s5aPP1iQ,870
jax/ops/__pycache__/__init__.cpython-38.pyc,,
jax/prng.py,sha256=SpQ_xnm2K-elxv5uf2zB30pR5akscVt-nhUn_tTav5s,997
jax/profiler.py,sha256=tMxGkMNYoug4DQGLE-OKx0z_nhlR-gBSs6vFpXd3jzY,1119
jax/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jax/random.py,sha256=ob5ywXpD2iwG63Ns_0D7jrnXYC6wZSIDoguaB_YNMZ0,7445
jax/scipy/__init__.py,sha256=tPpkFO6v7DYqeFxKCKWYW02UwSWFxPCrWj4vL64Y7jc,1410
jax/scipy/__pycache__/__init__.cpython-38.pyc,,
jax/scipy/__pycache__/fft.cpython-38.pyc,,
jax/scipy/__pycache__/linalg.cpython-38.pyc,,
jax/scipy/__pycache__/ndimage.cpython-38.pyc,,
jax/scipy/__pycache__/signal.cpython-38.pyc,,
jax/scipy/__pycache__/special.cpython-38.pyc,,
jax/scipy/cluster/__init__.py,sha256=vQWrNtPmBCHRT1-G_u6MN9byPWzUfwNawdc9HzSYGcI,768
jax/scipy/cluster/__pycache__/__init__.cpython-38.pyc,,
jax/scipy/cluster/__pycache__/vq.cpython-38.pyc,,
jax/scipy/cluster/vq.py,sha256=LrojEdKqZlhFlSZCYbuCWck7Jn2MeF9aqFf6ip_Y2DM,776
jax/scipy/fft.py,sha256=0EABRVTc4R4OlsBbYFtTV6_dSkVmUdzSnuAZyPW4_nE,809
jax/scipy/interpolate/__init__.py,sha256=RI5oUXPC-8tKmYURnnrnA-e1LDbnNVOVVqkGSpagEBM,760
jax/scipy/interpolate/__pycache__/__init__.cpython-38.pyc,,
jax/scipy/linalg.py,sha256=0ixmk9ODxI6Kd1XOLAjcGGblXnT2LH2WmiEH6nF94a8,1360
jax/scipy/ndimage.py,sha256=JUcTRgkwTxZ6GwR3yMLFU31szaYuUPc83JGfW2yWxOQ,788
jax/scipy/optimize/__init__.py,sha256=sLE5C83qmDBkS7qF0IzbeStYQwrAJWAze9-pmTVPFNg,822
jax/scipy/optimize/__pycache__/__init__.cpython-38.pyc,,
jax/scipy/signal.py,sha256=ILku2cOpXj0XWAGwBVTv7cb-n2BI_O8gl1R0juJZGxo,975
jax/scipy/sparse/__init__.py,sha256=HCUon--lvEN7ep4bU1gcntvclsQS3K3C2okuFHJpWz4,757
jax/scipy/sparse/__pycache__/__init__.cpython-38.pyc,,
jax/scipy/sparse/__pycache__/linalg.cpython-38.pyc,,
jax/scipy/sparse/linalg.py,sha256=sXphTZ_Gba0IfRZ6vuGkvwcslNi0nOwvt-yQ3XrtBVc,810
jax/scipy/spatial/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/scipy/spatial/__pycache__/__init__.cpython-38.pyc,,
jax/scipy/spatial/__pycache__/transform.cpython-38.pyc,,
jax/scipy/spatial/transform.py,sha256=fKQnQ0IXpn5ri0pDjhEWQxc7Oem02FWhD2wb1Om4bro,802
jax/scipy/special.py,sha256=95AmpwKWakMZgSaTh9k-7c-el6A4d3Hd1U-yLAgJtg4,1420
jax/scipy/stats/__init__.py,sha256=1GPfTj0Oj1FJh2hmrbOyKdijHFLJW_g5TLYYc7PwSYk,1887
jax/scipy/stats/__pycache__/__init__.cpython-38.pyc,,
jax/scipy/stats/__pycache__/bernoulli.cpython-38.pyc,,
jax/scipy/stats/__pycache__/beta.cpython-38.pyc,,
jax/scipy/stats/__pycache__/betabinom.cpython-38.pyc,,
jax/scipy/stats/__pycache__/cauchy.cpython-38.pyc,,
jax/scipy/stats/__pycache__/chi2.cpython-38.pyc,,
jax/scipy/stats/__pycache__/dirichlet.cpython-38.pyc,,
jax/scipy/stats/__pycache__/expon.cpython-38.pyc,,
jax/scipy/stats/__pycache__/gamma.cpython-38.pyc,,
jax/scipy/stats/__pycache__/gennorm.cpython-38.pyc,,
jax/scipy/stats/__pycache__/geom.cpython-38.pyc,,
jax/scipy/stats/__pycache__/laplace.cpython-38.pyc,,
jax/scipy/stats/__pycache__/logistic.cpython-38.pyc,,
jax/scipy/stats/__pycache__/multinomial.cpython-38.pyc,,
jax/scipy/stats/__pycache__/multivariate_normal.cpython-38.pyc,,
jax/scipy/stats/__pycache__/nbinom.cpython-38.pyc,,
jax/scipy/stats/__pycache__/norm.cpython-38.pyc,,
jax/scipy/stats/__pycache__/pareto.cpython-38.pyc,,
jax/scipy/stats/__pycache__/poisson.cpython-38.pyc,,
jax/scipy/stats/__pycache__/t.cpython-38.pyc,,
jax/scipy/stats/__pycache__/truncnorm.cpython-38.pyc,,
jax/scipy/stats/__pycache__/uniform.cpython-38.pyc,,
jax/scipy/stats/__pycache__/vonmises.cpython-38.pyc,,
jax/scipy/stats/bernoulli.py,sha256=V_oR9XYdYZBKZ3Nh5Y5nH_6HicaJtsqoaRFg4GYeujs,819
jax/scipy/stats/beta.py,sha256=yCJ4xUDEbkxuZTWSqXT0Tvce3buEBY1ymHXB-0wONGE,833
jax/scipy/stats/betabinom.py,sha256=-B4rZ-30Nvr4Lxg9D2DXcqxKdBI3pEItbQ-8ncNJWs0,792
jax/scipy/stats/cauchy.py,sha256=JAkmiQL1QRHXkAmCkTpwUpObZBOMnpgCFUDjH2VruPU,863
jax/scipy/stats/chi2.py,sha256=hP6cnjR6OgVWb_KjokacxwQq7l1Wmlbh3xrLJY-dwkA,833
jax/scipy/stats/dirichlet.py,sha256=KOGl-X9h2kKS0YlfWzsbMT5xEg3ONDgn-MWvQMBmD_A,792
jax/scipy/stats/expon.py,sha256=WS-OP1ng1jUlodys956ykXfnvs18u7hfGzQv7PnKLDQ,788
jax/scipy/stats/gamma.py,sha256=x4CT6o_xM6ERzlpdtMBO3YUonDUhrfmGn9WaGAvT4Bk,834
jax/scipy/stats/gennorm.py,sha256=_NdhXS5lWu6cDZgyizZfDlm1rFVDgXGAEOYIk3Cp7FU,804
jax/scipy/stats/geom.py,sha256=uyOJ8a88yGVTe5YTjg_JO0sLZzBi9OdHMAEOYxtpaaQ,787
jax/scipy/stats/laplace.py,sha256=Oi_fMUikhKflSBJCu1R7UjGkgJLij45M-_9Q5VG0yuw,804
jax/scipy/stats/logistic.py,sha256=c-_nDR9e1a8yk7Ngv7Og5G1vfqjhFLzdTMxDE5UwzUc,845
jax/scipy/stats/multinomial.py,sha256=mysALVSGzMZahxx69AeoGbS-p1Vod2RkGdHZfKJAtRw,794
jax/scipy/stats/multivariate_normal.py,sha256=OL3t4XR7qmqBuw7Ojl3Chvsbwf_UsqtkiTL-9gHQ8JU,802
jax/scipy/stats/nbinom.py,sha256=eMTqIxq9njSnXtEInWHGJBfINEo6N4yVrwji4jsyc58,643
jax/scipy/stats/norm.py,sha256=2g89q7EQ-DCPNAO1CEYA623RrZcfX4weJ8C1vKnzMj4,861
jax/scipy/stats/pareto.py,sha256=gzEhI-vI4gllIHFFSe1VaTvGbf6GYzPG4siMkxOKRsg,789
jax/scipy/stats/poisson.py,sha256=hMSNeBSToe3D9W6-isGDSZDB-Qyk8GBUtdT8J2PHCMU,804
jax/scipy/stats/t.py,sha256=RO4Zd0Lm2creTHIsFR2tVerxe5TdzRWMOlSSyaH3s1w,784
jax/scipy/stats/truncnorm.py,sha256=PnB_oNVAW0trLzh8ua_T9V0vPnrlBmeBpA1FkPDPoXE,855
jax/scipy/stats/uniform.py,sha256=HKppa0nu4HOg6RFGyV735zhgqX0xsMwYfM0mr_2kQ28,790
jax/scipy/stats/vonmises.py,sha256=nQvzYinMztVFMPZm0fd14ZmHxxoi1CZC_fvRgmBwAMo,791
jax/sharding.py,sha256=UgnvqjbgLSbXzUHLJUzA1d9KgFba-1Nzq1sMAecUB94,1184
jax/stages.py,sha256=fxDMZsTC4Ehzedq5af92xB0RMrU0gfLlLCKE4MWioOg,1272
jax/test_util.py,sha256=Ld3VuPa_Q4huIBWhk-ZImd3IaB-5QKZkWu6QlDYApaU,835
jax/tools/__init__.py,sha256=BTyMF0yh0M75AsNwF_0NSd5gHnIVpxhsLznsWkxu3XQ,581
jax/tools/__pycache__/__init__.cpython-38.pyc,,
jax/tools/__pycache__/colab_tpu.cpython-38.pyc,,
jax/tools/__pycache__/jax_to_ir.cpython-38.pyc,,
jax/tools/colab_tpu.py,sha256=M8F2u_rcvDdTKHB502zhticEZEqNGE8j5dzgPwoELgo,1553
jax/tools/jax_to_ir.py,sha256=dk4suEURxT4YCKijzKQOkSiqe7qkzKYiPIdxGDtuF_I,8702
jax/tree_util.py,sha256=E1y3r-DCE-sgcOFolAsB3q1CH3IDBnfQyw6LXHj17Bw,3024
jax/typing.py,sha256=tpCqNUt1m5JUbkLUQeF50O0DOonvrCahZuqgiiaXTxs,2996
jax/util.py,sha256=hG_kZcqiOEO2HjTehzSyEFVPctNZneOIORqfC9hwzyA,1091
jax/version.py,sha256=RwHpJkDF7vUyPTrpdmq93UC831oa8IA74HHyVFD9C8Y,1005
