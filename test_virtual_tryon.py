"""
Test rapide du système Virtual Try-On avec les images créées
"""

import sys
import os
from PIL import Image

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_virtual_tryon():
    """Test du système Virtual Try-On"""
    print("🧪 Test du système Virtual Try-On")
    print("="*50)
    
    try:
        from models.virtual_tryon_model import create_model
        print("✅ Import du modèle: OK")
        
        # Créer le modèle
        print("🤖 Initialisation du modèle...")
        model = create_model()
        print("✅ Modèle initialisé: OK")
        
        # Vérifier que les images de test existent
        person_path = "data/persons/test_person.jpg"
        dress_path = "data/clothes/test_dress.jpg"
        
        if not os.path.exists(person_path):
            print(f"❌ Image de personne non trouvée: {person_path}")
            print("💡 Exécutez d'abord: python create_test_images.py")
            return False
            
        if not os.path.exists(dress_path):
            print(f"❌ Image de vêtement non trouvée: {dress_path}")
            print("💡 Exécutez d'abord: python create_test_images.py")
            return False
        
        print("✅ Images de test trouvées: OK")
        
        # Test de prédiction
        print("🎯 Test de prédiction...")
        result = model.predict(person_path, dress_path, person_height=170)
        
        if result is not None:
            print("✅ Prédiction réussie: OK")
            
            # Sauvegarder le résultat
            output_path = "output/test_result.jpg"
            os.makedirs("output", exist_ok=True)
            result.save(output_path, quality=95)
            print(f"✅ Résultat sauvegardé: {output_path}")
            
            return True
        else:
            print("❌ Prédiction échouée")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Point d'entrée principal"""
    success = test_virtual_tryon()
    
    print("\n" + "="*50)
    if success:
        print("🎉 Test réussi ! Le système fonctionne correctement.")
        print("🌐 Vous pouvez maintenant utiliser l'interface web:")
        print("   streamlit run app.py")
        print("   http://localhost:8501")
    else:
        print("⚠️ Test échoué. Vérifiez les erreurs ci-dessus.")
        print("💡 L'interface web peut quand même fonctionner en mode démo.")
    
    print("="*50)

if __name__ == "__main__":
    main()
