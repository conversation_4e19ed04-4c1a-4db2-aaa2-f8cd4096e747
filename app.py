"""
Interface utilisateur principale pour le système Virtual Try-On
Utilise Streamlit pour une interface web simple et intuitive
"""

import streamlit as st

# Configuration de la page DOIT être la première commande Streamlit
st.set_page_config(
    page_title="Virtual Try-On IA",
    page_icon="👗",
    layout="wide",
    initial_sidebar_state="expanded"
)

import os
import tempfile
from PIL import Image
import logging
from datetime import datetime
import sys
import numpy as np

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import conditionnel pour éviter les erreurs si certains modules ne sont pas installés
try:
    from config import UI_CONFIG, PROMPTS, IMAGE_CONFIG
except ImportError:
    # Configuration de base si config.py n'est pas accessible
    UI_CONFIG = {
        "title": "Virtual Try-On IA",
        "description": "Essayez virtuellement des vêtements avec l'intelligence artificielle"
    }
    PROMPTS = {
        "user_instructions": "1. Uploadez une photo de personne\n2. Uploadez une photo de vêtement\n3. Cliquez sur 'Essayer le vêtement'"
    }
    IMAGE_CONFIG = {
        "supported_formats": [".jpg", ".jpeg", ".png", ".bmp"]
    }

# Import conditionnel des modules IA
try:
    from models.virtual_tryon_model import create_model
    from utils.image_processing import ImageProcessor
    from utils.pose_detection import PoseDetector
    from utils.segmentation import ImageSegmenter
    AI_MODULES_AVAILABLE = True
except ImportError:
    AI_MODULES_AVAILABLE = False
    print("⚠️ Modules IA non disponibles - Mode démonstration activé")

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VirtualTryOnApp:
    """Application principale de Virtual Try-On"""

    def __init__(self):
        self.model = None
        self.image_processor = None
        self.pose_detector = None
        self.segmenter = None

        # Initialiser les composants
        self._initialize_components()
        self._initialize_model()

    def _initialize_components(self):
        """Initialise les composants IA si disponibles"""
        if AI_MODULES_AVAILABLE:
            try:
                self.image_processor = ImageProcessor()
                self.pose_detector = PoseDetector()
                self.segmenter = ImageSegmenter()
                logger.info("Composants IA initialisés avec succès")
            except Exception as e:
                logger.warning(f"Erreur lors de l'initialisation des composants: {e}")
                self.image_processor = None
                self.pose_detector = None
                self.segmenter = None
        else:
            logger.info("Mode démonstration - Composants IA non disponibles")

    def _initialize_model(self):
        """Initialise le modèle de Virtual Try-On"""
        if AI_MODULES_AVAILABLE:
            try:
                with st.spinner("Chargement du modèle IA..."):
                    self.model = create_model()
                    logger.info("Modèle initialisé avec succès")
            except Exception as e:
                logger.error(f"Erreur lors de l'initialisation du modèle: {e}")
                st.warning("⚠️ Modèle IA non disponible - Mode démonstration activé")
                self.model = None
        else:
            st.info("ℹ️ Mode démonstration activé - Modèle IA non disponible")
            self.model = None
    
    def run(self):
        """Lance l'application Streamlit"""
        # Titre principal
        st.title(UI_CONFIG["title"])
        st.markdown(UI_CONFIG["description"])
        
        # Sidebar avec instructions
        self._create_sidebar()
        
        # Interface principale
        self._create_main_interface()
    
    def _create_sidebar(self):
        """Crée la barre latérale avec les instructions"""
        st.sidebar.header("📋 Instructions")
        st.sidebar.markdown(PROMPTS["user_instructions"])
        
        st.sidebar.header("⚙️ Paramètres")
        
        # Options de qualité
        quality = st.sidebar.selectbox(
            "Qualité de traitement",
            ["Standard", "Haute qualité"],
            index=0
        )
        
        # Options de debug
        show_debug = st.sidebar.checkbox("Afficher les étapes de traitement", False)
        
        return quality, show_debug
    
    def _create_main_interface(self):
        """Crée l'interface principale"""
        # Colonnes pour l'upload
        col1, col2 = st.columns(2)
        
        with col1:
            st.header("👤 Photo de la personne")
            person_image = st.file_uploader(
                "Choisissez une photo de la personne",
                type=IMAGE_CONFIG["supported_formats"],
                key="person_upload"
            )
            
            # Champ optionnel pour la taille
            person_height = st.number_input(
                "Taille de la personne (cm) - optionnel",
                min_value=140,
                max_value=220,
                value=170,
                step=1
            )
        
        with col2:
            st.header("👕 Photo du vêtement")
            clothing_image = st.file_uploader(
                "Choisissez une photo du vêtement",
                type=IMAGE_CONFIG["supported_formats"],
                key="clothing_upload"
            )
            
            # Type de vêtement
            clothing_type = st.selectbox(
                "Type de vêtement",
                ["Robe", "Maillot de bain", "T-shirt", "Chemise", "Pantalon", "Jupe", "Veste", "Autre"],
                index=0
            )
        
        # Affichage des images uploadées
        if person_image and clothing_image:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Image de la personne")
                person_pil = Image.open(person_image)
                st.image(person_pil, use_container_width=True)

            with col2:
                st.subheader("Image du vêtement")
                clothing_pil = Image.open(clothing_image)
                st.image(clothing_pil, use_container_width=True)
            
            # Bouton de traitement
            if st.button("🎯 Essayer le vêtement", type="primary"):
                self._process_virtual_tryon(
                    person_image, clothing_image, 
                    person_height, clothing_type
                )
    
    def _process_virtual_tryon(self, person_image, clothing_image, person_height, clothing_type):
        """Traite la demande d'essayage virtuel"""
        try:
            # Créer des fichiers temporaires
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as person_temp:
                person_pil = Image.open(person_image)
                person_pil.save(person_temp.name)
                person_temp_path = person_temp.name
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as clothing_temp:
                clothing_pil = Image.open(clothing_image)
                clothing_pil.save(clothing_temp.name)
                clothing_temp_path = clothing_temp.name
            
            # Validation des images
            is_valid, person_pil, clothing_pil = self._validate_images(person_pil, clothing_pil)
            if not is_valid:
                return
            
            # Traitement avec barre de progression
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Étape 1: Analyse de la pose
            status_text.text("Analyse de la pose de la personne...")
            progress_bar.progress(20)

            pose_info = None
            if self.pose_detector:
                try:
                    pose_info = self.pose_detector.detect_pose(person_pil)
                    if pose_info is None:
                        st.warning("⚠️ Pose non détectée clairement. Le résultat pourrait être moins précis.")
                except Exception as e:
                    logger.warning(f"Erreur détection de pose: {e}")

            # Étape 2: Segmentation
            status_text.text("Segmentation des images...")
            progress_bar.progress(40)

            person_segmentation = None
            clothing_segmentation = None
            if self.segmenter:
                try:
                    person_segmentation = self.segmenter.segment_person(person_pil)
                    clothing_segmentation = self.segmenter.segment_clothing(clothing_pil, clothing_type.lower())
                except Exception as e:
                    logger.warning(f"Erreur segmentation: {e}")
            
            # Étape 3: Traitement IA
            status_text.text("Génération de l'essayage virtuel...")
            progress_bar.progress(60)
            
            if self.model is not None:
                result_image = self.model.predict(
                    person_temp_path, 
                    clothing_temp_path, 
                    person_height
                )
            else:
                # Mode démo sans modèle entraîné
                result_image = self._create_demo_result(person_pil, clothing_pil)
            
            progress_bar.progress(80)
            
            # Étape 4: Post-traitement
            status_text.text("Finalisation du résultat...")
            progress_bar.progress(100)
            
            # Affichage du résultat
            if result_image:
                self._display_result(result_image, person_pil, clothing_pil)
            else:
                st.error("❌ Erreur lors de la génération de l'essayage virtuel.")
            
            # Nettoyage
            progress_bar.empty()
            status_text.empty()
            
            # Supprimer les fichiers temporaires
            os.unlink(person_temp_path)
            os.unlink(clothing_temp_path)
            
        except Exception as e:
            logger.error(f"Erreur lors du traitement: {e}")
            st.error(f"❌ Erreur: {str(e)}")
    
    def _validate_images(self, person_image: Image.Image, clothing_image: Image.Image) -> tuple:
        """Valide les images uploadées et les convertit en RGB"""
        # Vérifier la taille des images
        if person_image.size[0] < 256 or person_image.size[1] < 256:
            st.error("❌ L'image de la personne est trop petite (minimum 256x256 pixels)")
            return False, None, None

        if clothing_image.size[0] < 256 or clothing_image.size[1] < 256:
            st.error("❌ L'image du vêtement est trop petite (minimum 256x256 pixels)")
            return False, None, None

        # Convertir en RGB si nécessaire
        if person_image.mode != 'RGB':
            person_image = person_image.convert('RGB')

        if clothing_image.mode != 'RGB':
            clothing_image = clothing_image.convert('RGB')

        return True, person_image, clothing_image
    
    def _create_demo_result(self, person_image: Image.Image, clothing_image: Image.Image) -> Image.Image:
        """Crée un résultat de démonstration (sans modèle entraîné)"""
        try:
            # S'assurer que les images sont en mode RGB
            if person_image.mode != 'RGB':
                person_image = person_image.convert('RGB')
            if clothing_image.mode != 'RGB':
                clothing_image = clothing_image.convert('RGB')

            # Simple composition pour la démonstration
            result = person_image.copy()

            # Redimensionner le vêtement
            clothing_resized = clothing_image.resize((result.width // 3, result.height // 3))

            # Coller le vêtement sur la personne (position approximative)
            paste_x = result.width // 3
            paste_y = result.height // 4

            # Créer un masque simple pour la transparence
            mask = Image.new('L', clothing_resized.size, 128)
            result.paste(clothing_resized, (paste_x, paste_y), mask)

            # S'assurer que le résultat est en RGB
            if result.mode != 'RGB':
                result = result.convert('RGB')

            return result
            
        except Exception as e:
            logger.error(f"Erreur lors de la création du résultat démo: {e}")
            return person_image
    
    def _display_result(self, result_image: Image.Image, person_image: Image.Image, clothing_image: Image.Image):
        """Affiche le résultat de l'essayage virtuel"""
        st.success("✅ Essayage virtuel terminé !")
        
        # Affichage en colonnes
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.subheader("Original")
            st.image(person_image, use_container_width=True)

        with col2:
            st.subheader("Vêtement")
            st.image(clothing_image, use_container_width=True)

        with col3:
            st.subheader("Résultat")
            st.image(result_image, use_container_width=True)
        
        # Options de sauvegarde
        st.subheader("💾 Sauvegarder le résultat")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Bouton de téléchargement
            import io
            img_buffer = io.BytesIO()

            # Convertir en RGB si nécessaire pour éviter l'erreur JPEG
            if result_image.mode in ('RGBA', 'LA', 'P'):
                # Créer un fond blanc et coller l'image dessus
                rgb_image = Image.new('RGB', result_image.size, (255, 255, 255))
                if result_image.mode == 'P':
                    result_image = result_image.convert('RGBA')
                rgb_image.paste(result_image, mask=result_image.split()[-1] if result_image.mode == 'RGBA' else None)
                result_image = rgb_image

            result_image.save(img_buffer, format='JPEG', quality=95)
            img_buffer.seek(0)
            
            st.download_button(
                label="📥 Télécharger l'image",
                data=img_buffer.getvalue(),
                file_name=f"essayage_virtuel_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg",
                mime="image/jpeg"
            )
        
        with col2:
            if st.button("🔄 Nouvel essayage"):
                st.experimental_rerun()

def main():
    """Point d'entrée principal"""
    app = VirtualTryOnApp()
    app.run()

if __name__ == "__main__":
    main()
