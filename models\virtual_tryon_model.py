"""
Modèle principal de Virtual Try-On
Basé sur les architectures VITON et des techniques de deep learning modernes
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
from typing import Dict, Tuple, Optional
import logging

from config import MODEL_CONFIG
from utils.image_processing import ImageProcessor
from utils.pose_detection import PoseDetector
from utils.segmentation import ImageSegmenter

logger = logging.getLogger(__name__)

class GeometricMatchingModule(nn.Module):
    """Module pour l'alignement géométrique du vêtement sur la personne"""
    
    def __init__(self, input_channels=6):
        super(GeometricMatchingModule, self).__init__()
        
        # Extracteur de caractéristiques
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(input_channels, 64, 4, 2, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 4, 2, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 256, 4, 2, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 512, 4, 2, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )
        
        # Régression pour les paramètres de transformation
        self.regression = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(256, 6)  # Paramètres de transformation affine
        )
    
    def forward(self, person_representation, clothing_item):
        """
        Args:
            person_representation: Représentation de la personne [B, 3, H, W]
            clothing_item: Image du vêtement [B, 3, H, W]
        
        Returns:
            Paramètres de transformation [B, 6]
        """
        # Concaténer les entrées
        combined_input = torch.cat([person_representation, clothing_item], dim=1)
        
        # Extraire les caractéristiques
        features = self.feature_extractor(combined_input)
        
        # Prédire les paramètres de transformation
        transformation_params = self.regression(features)
        
        return transformation_params

class TryOnModule(nn.Module):
    """Module principal pour générer l'image d'essayage"""
    
    def __init__(self):
        super(TryOnModule, self).__init__()
        
        # Encodeur
        self.encoder = nn.Sequential(
            nn.Conv2d(9, 64, 4, 2, 1),  # person + clothing + mask
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 4, 2, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 256, 4, 2, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 512, 4, 2, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )
        
        # Décodeur
        self.decoder = nn.Sequential(
            nn.ConvTranspose2d(512, 256, 4, 2, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(256, 128, 4, 2, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(128, 64, 4, 2, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(64, 3, 4, 2, 1),
            nn.Tanh()
        )
    
    def forward(self, person_image, transformed_clothing, person_mask):
        """
        Args:
            person_image: Image de la personne [B, 3, H, W]
            transformed_clothing: Vêtement transformé [B, 3, H, W]
            person_mask: Masque de la personne [B, 3, H, W]
        
        Returns:
            Image d'essayage générée [B, 3, H, W]
        """
        # Concaténer toutes les entrées
        combined_input = torch.cat([person_image, transformed_clothing, person_mask], dim=1)
        
        # Encoder
        encoded = self.encoder(combined_input)
        
        # Décoder
        result = self.decoder(encoded)
        
        return result

class VirtualTryOnModel(nn.Module):
    """Modèle complet de Virtual Try-On"""
    
    def __init__(self):
        super(VirtualTryOnModel, self).__init__()
        
        self.device = torch.device(MODEL_CONFIG["device"])
        
        # Modules du modèle
        self.geometric_matching = GeometricMatchingModule()
        self.tryon_module = TryOnModule()
        
        # Utilitaires
        self.image_processor = ImageProcessor()
        self.pose_detector = PoseDetector()
        self.segmenter = ImageSegmenter()
        
        # Déplacer vers le device approprié
        self.to(self.device)
        
        logger.info(f"Modèle Virtual Try-On initialisé sur {self.device}")
    
    def forward(self, person_image, clothing_image, person_mask=None):
        """
        Forward pass du modèle complet
        
        Args:
            person_image: Tensor de l'image de la personne [B, 3, H, W]
            clothing_image: Tensor de l'image du vêtement [B, 3, H, W]
            person_mask: Masque de la personne (optionnel) [B, 3, H, W]
        
        Returns:
            Image d'essayage générée [B, 3, H, W]
        """
        batch_size = person_image.size(0)
        
        # Si pas de masque fourni, créer un masque par défaut
        if person_mask is None:
            person_mask = torch.ones_like(person_image)
        
        # 1. Alignement géométrique
        transformation_params = self.geometric_matching(person_image, clothing_image)
        
        # 2. Appliquer la transformation au vêtement
        transformed_clothing = self._apply_transformation(clothing_image, transformation_params)
        
        # 3. Générer l'image d'essayage
        result = self.tryon_module(person_image, transformed_clothing, person_mask)
        
        return result
    
    def _apply_transformation(self, clothing_image, transformation_params):
        """
        Applique une transformation géométrique au vêtement
        
        Args:
            clothing_image: Image du vêtement [B, 3, H, W]
            transformation_params: Paramètres de transformation [B, 6]
        
        Returns:
            Vêtement transformé [B, 3, H, W]
        """
        batch_size = clothing_image.size(0)
        
        # Créer la matrice de transformation affine
        theta = transformation_params.view(batch_size, 2, 3)
        
        # Créer la grille de transformation
        grid = F.affine_grid(theta, clothing_image.size(), align_corners=False)
        
        # Appliquer la transformation
        transformed = F.grid_sample(clothing_image, grid, align_corners=False)
        
        return transformed
    
    def predict(self, person_image_path: str, clothing_image_path: str, 
                person_height: Optional[float] = None) -> Optional[Image.Image]:
        """
        Prédiction complète avec préprocessing et postprocessing
        
        Args:
            person_image_path: Chemin vers l'image de la personne
            clothing_image_path: Chemin vers l'image du vêtement
            person_height: Taille de la personne en cm (optionnel)
        
        Returns:
            Image PIL du résultat ou None si erreur
        """
        try:
            logger.info(f"Début de la prédiction: personne={person_image_path}, vêtement={clothing_image_path}")
            
            # 1. Charger et préprocesser les images
            person_image = self.image_processor.load_image(person_image_path)
            clothing_image = self.image_processor.load_image(clothing_image_path)
            
            if person_image is None or clothing_image is None:
                logger.error("Impossible de charger les images")
                return None
            
            # 2. Détecter la pose
            pose_info = self.pose_detector.detect_pose(person_image)
            if pose_info is None:
                logger.warning("Aucune pose détectée, utilisation de paramètres par défaut")
            
            # 3. Segmenter la personne
            person_segmentation = None
            if self.segmenter:
                try:
                    person_segmentation = self.segmenter.segment_person(person_image)
                except Exception as e:
                    logger.warning(f"Erreur lors de la segmentation: {e}")

            if person_segmentation is None:
                logger.warning("Segmentation de la personne échouée - utilisation d'un masque par défaut")
                person_mask_np = np.ones((person_image.size[1], person_image.size[0]), dtype=np.uint8) * 255
            else:
                person_mask_np = person_segmentation['mask']
            
            # 4. Segmenter le vêtement
            clothing_segmentation = None
            if self.segmenter:
                try:
                    clothing_segmentation = self.segmenter.segment_clothing(clothing_image)
                except Exception as e:
                    logger.warning(f"Erreur lors de la segmentation du vêtement: {e}")
            
            # 5. Préprocesser pour le modèle
            person_tensor = self.image_processor.preprocess_person_image(person_image)
            clothing_tensor = self.image_processor.preprocess_clothing_image(clothing_image)
            
            # Créer le masque de la personne
            person_mask_pil = Image.fromarray(person_mask_np).convert('RGB')
            input_size = MODEL_CONFIG.get("input_size", (512, 384))
            person_mask_pil = person_mask_pil.resize((input_size[1], input_size[0]))
            person_mask_tensor = transforms.ToTensor()(person_mask_pil).unsqueeze(0)
            
            # 6. Déplacer vers le device
            person_tensor = person_tensor.to(self.device)
            clothing_tensor = clothing_tensor.to(self.device)
            person_mask_tensor = person_mask_tensor.to(self.device)
            
            # 7. Prédiction
            try:
                self.eval()
                with torch.no_grad():
                    result_tensor = self.forward(person_tensor, clothing_tensor, person_mask_tensor)

                # 8. Post-traitement
                result_image = self.image_processor.postprocess_result(result_tensor.cpu().squeeze(0))
            except Exception as e:
                logger.warning(f"Erreur lors de la prédiction IA: {e}")
                logger.info("Utilisation du mode démonstration")
                # Fallback vers le mode démonstration
                result_image = self._create_demo_result(person_image, clothing_image)
            
            logger.info("Prédiction terminée avec succès")
            return result_image
            
        except Exception as e:
            logger.error(f"Erreur lors de la prédiction: {e}")
            return None

    def _create_demo_result(self, person_image: Image.Image, clothing_image: Image.Image) -> Image.Image:
        """Crée un résultat de démonstration simple"""
        try:
            # S'assurer que les images sont en mode RGB
            if person_image.mode != 'RGB':
                person_image = person_image.convert('RGB')
            if clothing_image.mode != 'RGB':
                clothing_image = clothing_image.convert('RGB')

            # Simple composition pour la démonstration
            result = person_image.copy()

            # Redimensionner le vêtement
            clothing_resized = clothing_image.resize((result.width // 3, result.height // 3))

            # Coller le vêtement sur la personne (position approximative)
            paste_x = result.width // 3
            paste_y = result.height // 4

            # Créer un masque simple pour la transparence
            mask = Image.new('L', clothing_resized.size, 128)
            result.paste(clothing_resized, (paste_x, paste_y), mask)

            # S'assurer que le résultat est en RGB
            if result.mode != 'RGB':
                result = result.convert('RGB')

            return result

        except Exception as e:
            logger.error(f"Erreur lors de la création du résultat démo: {e}")
            return person_image
    
    def save_model(self, path: str):
        """Sauvegarde le modèle"""
        try:
            torch.save({
                'model_state_dict': self.state_dict(),
                'config': MODEL_CONFIG
            }, path)
            logger.info(f"Modèle sauvegardé: {path}")
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde: {e}")
    
    def load_model(self, path: str):
        """Charge un modèle pré-entraîné"""
        try:
            checkpoint = torch.load(path, map_location=self.device)
            self.load_state_dict(checkpoint['model_state_dict'])
            logger.info(f"Modèle chargé: {path}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors du chargement: {e}")
            return False

def create_model() -> VirtualTryOnModel:
    """Factory function pour créer le modèle"""
    return VirtualTryOnModel()
